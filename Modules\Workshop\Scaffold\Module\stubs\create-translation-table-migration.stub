<?php

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class Create$MODULE_NAME$$CLASS_NAME$TranslationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('$LOWERCASE_MODULE_NAME$__$LOWERCASE_CLASS_NAME$_translations', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->increments('id');
            // Your translatable fields

            $table->integer('$LOWERCASE_CLASS_NAME$_id')->unsigned();
            $table->string('locale')->index();
            $table->unique(['$LOWERCASE_CLASS_NAME$_id', 'locale']);
            $table->foreign('$LOWERCASE_CLASS_NAME$_id')->references('id')->on('$LOWERCASE_MODULE_NAME$__$PLURAL_LOWERCASE_CLASS_NAME$')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('$LOWERCASE_MODULE_NAME$__$LOWERCASE_CLASS_NAME$_translations', function (Blueprint $table) {
            $table->dropForeign(['$LOWERCASE_CLASS_NAME$_id']);
        });
        Schema::dropIfExists('$LOWERCASE_MODULE_NAME$__$LOWERCASE_CLASS_NAME$_translations');
    }
}
