<?php $__env->startSection('content'); ?>

    <?php if(!$currentUser->office_no || $currentUser->office_no == 3): ?>
    <div class="row justify-content-center office_3"  >
        <?php if(_in_any_role(['admin', 'executive', 'director', 'sales-supervisor', 'department-head', 'section-head'])): ?>
        <div class="col-md-12 col-12">
            <div class="card">  
                <div class="card-body">
                    <h3 class="mb-4">รายงานเจ้าหน้าที่ขายทั่วไป สำนักการตลาด</h3>
                    <div class="row justify-content-center">

                        <div class="col-md-3 col-sm-4 col-6 mb-4">
                            <a href="<?php echo e(route('supervisor.salemarketing.users.index')); ?>" class="gridItem">
                                <img src="<?php echo e(_asset('svg/pink/undraw_Online_friends_re_eqaj.svg')); ?>" width="100%"><br>
                                <h4 class="mt-4">ประวัติการเข้าสู่ระบบ</h4>
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-4 col-6 mb-4">
                            <a href="<?php echo e(route('supervisor.salemarketing.sales.index')); ?>" class="gridItem">
                                <img src="<?php echo e(_asset('svg/pink/undraw_Taking_notes_re_bnaf.svg')); ?>" width="100%"><br>
                                <h4 class="mt-4">ประวัติการพบลูกค้า</h4>
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-4 col-6 mb-4">
                            <a href="<?php echo e(route('supervisor.salemarketing.sales.summary')); ?>" class="gridItem">
                                <img src="<?php echo e(_asset('svg/pink/undraw_Segmentation_re_gduq.svg')); ?>" width="100%"><br>
                                <h4 class="mt-4">วิเคราะห์สถิติ</h4>
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-4 col-6 mb-4">
                            <a href="<?php echo e(route('supervisor.salemarketing.demand.index')); ?>" class="gridItem">
                                <img src="<?php echo e(_asset('svg/pink/undraw_Gift_card_re_5dyy.svg')); ?>" width="100%"><br>
                                <h4 class="mt-4">สรุปความต้องการลูกค้า</h4>
                            </a>
                        </div> 
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
        <?php if(_in_any_role(['admin', 'section-head', 'sales-general'])): ?>
        <div class="col-md-12 col-12">
            <div class="card">
                <div class="card-body">
                    <h3 class="mb-4">เครื่องมือเจ้าหน้าที่ขายทั่วไป สำนักการตลาด</h3>
                    <div class="row justify-content-center">
                        <div class="col-md-3 col-sm-4 col-6 mb-4">
                            <a href="<?php echo e(route('user.salemarketing.sales.create')); ?>" class="gridItem">
                                <img src="<?php echo e(_asset('svg/pink/undraw_Online_learning_re_qw08.svg')); ?>" width="100%"><br>
                                <h4 class="mt-4">บันทึกการพบลูกค้า</h4>
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-4 col-6 mb-4">
                            <a href="<?php echo e(route('user.salemarketing.sales.index')); ?>" class="gridItem">
                                <img src="<?php echo e(_asset('svg/pink/undraw_subscribe_vspl.svg')); ?>" width="100%"><br>
                                <h4 class="mt-4">ประวัติการพบลูกค้า</h4>
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-4 col-6 mb-4">
                            <a target="_blank"
                                href="<?php echo e(config('asgard.salemarketing.data.department.' . ($currentUser->dept_no ?? 1) . '.catalog.link')); ?>"
                                class="gridItem">
                                <img src="<?php echo e(_asset('svg/pink/undraw_Booking_re_gw4j.svg')); ?>" width="100%"><br>
                                <h4 class="mt-4">Catalog</h4>
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-4 col-6 mb-4">
                            <a href="<?php echo e(route('page-fixed', ['uri' => 'qr'])); ?>" class="gridItem">
                                <img src="<?php echo e(_asset('svg/pink/undraw_Camera_re_cnp4.svg')); ?>" width="100%"><br>
                                <h4 class="mt-4">QR List</h4>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
    <?php endif; ?>
    <?php if(!$currentUser->office_no || $currentUser->office_no == 4): ?>
    <div class="row justify-content-center office_4"  >
        <?php if(_in_any_role(['admin', 'executive', 'director', 'sales-supervisor', 'department-head', 'section-head'])): ?>
        <div class="col-md-12 col-12">
            <div class="card">  
                <div class="card-body">
                    <h3 class="mb-4">รายงานเจ้าหน้าที่ขายทั่วไป สำนักการผลิต</h3>
                    <div class="row justify-content-center">

                        <div class="col-md-3 col-sm-4 col-6 mb-4">
                            <a href="<?php echo e(route('supervisor.saleprinting.users.index')); ?>" class="gridItem">
                                <img src="<?php echo e(_asset('svg/pink/undraw_Online_friends_re_eqaj.svg')); ?>" width="100%"><br>
                                <h4 class="mt-4">ประวัติการเข้าสู่ระบบ</h4>
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-4 col-6 mb-4">
                            <a href="<?php echo e(route('supervisor.saleprinting.sales.index')); ?>" class="gridItem">
                                <img src="<?php echo e(_asset('svg/pink/undraw_Taking_notes_re_bnaf.svg')); ?>" width="100%"><br>
                                <h4 class="mt-4">ประวัติการพบลูกค้า</h4>
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-4 col-6 mb-4">
                            <a href="<?php echo e(route('supervisor.saleprinting.sales.summary')); ?>" class="gridItem">
                                <img src="<?php echo e(_asset('svg/pink/undraw_Segmentation_re_gduq.svg')); ?>" width="100%"><br>
                                <h4 class="mt-4">วิเคราะห์สถิติ</h4>
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-4 col-6 mb-4">
                            <a href="<?php echo e(route('supervisor.saleprinting.demand.index')); ?>" class="gridItem">
                                <img src="<?php echo e(_asset('svg/pink/undraw_Gift_card_re_5dyy.svg')); ?>" width="100%"><br>
                                <h4 class="mt-4">สรุปความต้องการลูกค้า</h4>
                            </a>
                        </div> 
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <?php if(_in_any_role(['admin', 'director', 'section-head', 'sales-general'])): ?>
        <div class="col-md-12 col-12">
            <div class="card">
                <div class="card-body">
                    <h3 class="mb-4">เครื่องมือเจ้าหน้าที่ขายทั่วไป สำนักการผลิต</h3>
                    <div class="row justify-content-center">
                        <div class="col-md-3 col-sm-4 col-6 mb-4">
                            <a href="<?php echo e(route('user.saleprinting.sales.create')); ?>" class="gridItem">
                                <img src="<?php echo e(_asset('svg/pink/undraw_Online_learning_re_qw08.svg')); ?>" width="100%"><br>
                                <h4 class="mt-4">บันทึกการพบลูกค้า</h4>
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-4 col-6 mb-4">
                            <a href="<?php echo e(route('user.saleprinting.sales.index')); ?>" class="gridItem">
                                <img src="<?php echo e(_asset('svg/pink/undraw_subscribe_vspl.svg')); ?>" width="100%"><br>
                                <h4 class="mt-4">ประวัติการพบลูกค้า</h4>
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-4 col-6 mb-4">
                            <a target="_blank"
                                href="<?php echo e(config('asgard.saleprinting.data.department.' . (auth()->user()->dept_no ?? 1) . '.catalog.link')); ?>"
                                class="gridItem">
                                <img src="<?php echo e(_asset('svg/pink/undraw_Booking_re_gw4j.svg')); ?>" width="100%"><br>
                                <h4 class="mt-4">Catalog</h4>
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-4 col-6 mb-4">
                            <a href="<?php echo e(route('page-fixed', ['uri' => 'qr'])); ?>" class="gridItem">
                                <img src="<?php echo e(_asset('svg/pink/undraw_Camera_re_cnp4.svg')); ?>" width="100%"><br>
                                <h4 class="mt-4">QR List</h4>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
    <?php endif; ?>


    
    <?php if(!$currentUser->office_no || $currentUser->office_no == 3): ?>
    
    <div class="row"> 
        <?php if(_in_any_role(['admin', 'executive', 'director', 'printing-supervisor', 'department-head'])): ?>
        
            <div class="col-md-6 col-12">
                <div class="card">
                    <div class="card-body">
                        <h3 class="mb-4">รายงานการผลิต/จัดส่ง</h3>
                        <div class="row justify-content-center">
                            <div class="col-6 mb-4">
                                <a href="<?php echo e(route('supervisor.printing.printing.index')); ?>" class="gridItem">
                                    <img src="<?php echo e(_asset('svg/pink/undraw_Bookshelves_re_lxoy.svg')); ?>" width="100%"><br>
                                    <h4 class="mt-4">รายการตัดสินพิมพ์</h4>
                                </a>
                            </div>
                            <div class="col-6 mb-4">
                                <a href="<?php echo e(route('supervisor.printings.report.index')); ?>" class="gridItem">
                                    <img src="<?php echo e(_asset('svg/pink/undraw_Segmentation_re_gduq.svg')); ?>" width="100%"><br>
                                    <h4 class="mt-4">วิเคราะห์สถิติ</h4>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
        
        <?php if(_in_any_role(['admin', 'executive', 'director', 'printing-supervisor', 'department-head'])): ?>
            <div class="col-md-6 col-12">
                <div class="card">
                    <div class="card-body">
                        <h3 class="mb-4">เครื่องมือการผลิต/จัดส่ง</h3>
                        <div class="row justify-content-center">

                            <div class="col-6 mb-4">
                                <a href="<?php echo e(route('user.printing.record.index')); ?>" class="gridItem">
                                    <img src="<?php echo e(_asset('svg/pink/undraw_Notebook_re_id0r.svg')); ?>" width="100%"><br>
                                    <h4 class="mt-4">บันทึกการผลิต/จัดส่ง</h4>
                                </a>
                            </div>
                            
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
    <?php endif; ?>

    <?php if(!$currentUser->office_no || $currentUser->office_no == 3 || $currentUser->office_no == 4 || $currentUser->office_no == 6): ?>
    
    <div class="row"> 

        <?php if(_in_any_role(['admin', 'executive','director'])): ?>
            <div class="col-md-6 col-12">
                <div class="card">
                    <div class="card-body">
                        <h3 class="mb-4">เครื่องมือร้านค้า</h3>
                        <div class="row ">

                            <div class="col-6 mb-4">
                                <a href="<?php echo e(route('user.overdue.record.index')); ?>" class="gridItem">
                                    <img src="<?php echo e(_asset('svg/pink/undraw_Notebook_re_id0r.svg')); ?>" width="100%"><br>
                                    <h4 class="mt-4">บันทึกการรับ/จัดส่ง</h4>
                                </a>
                            </div>
                            
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>


        <?php if(_in_any_role(['admin', 'director', 'officer-general'])): ?>
            <div class="col-md-6 col-12">
                <div class="card">
                    <div class="card-body">
                        <h3 class="mb-4">เครื่องมือคิดราคา</h3>
                        <div class="row justify-content-center">
    
                            <div class="col-6 mb-4">
                                <a href="<?php echo e(route('user.printing.pricing.index')); ?>" class="gridItem">
                                    <img src="<?php echo e(_asset('svg/pink/undraw_Notebook_re_id0r.svg')); ?>" width="100%"><br>
                                    <h4 class="mt-4">รายการใบคิดราคา</h4>
                                </a>
                            </div>
                            <div class="col-6 mb-4">
                                <a href="<?php echo e(route('user.printing.pricing.setting', ['group'=>'general'])); ?>" class="gridItem">
                                    <img src="<?php echo e(_asset('svg/pink/undraw_heavy_box_agqi.svg')); ?>" width="100%"><br>
                                    <h4 class="mt-4">ตั้งค่าใบคิดราคา</h4>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
    <?php endif; ?>

    <?php if(!$currentUser->office_no || $currentUser->office_no == 1): ?>
    <div class="row ">
        <?php if(_in_any_role(['admin', 'officer-general'])): ?>
            <div class="col-md-6 col-12">
                <div class="card">
                    <div class="card-body">
                        <h3 class="mb-4">เครื่องมือเจ้าหน้าที่ HR</h3>
                        <div class="row justify-content-center">
    
                            <div class="col-6 mb-4">
                                <a href="<?php echo e(route('user.notice.notice.index', ['type'=>'recruit'])); ?>" class="gridItem">
                                    <img src="<?php echo e(_asset('svg/pink/undraw_Notebook_re_id0r.svg')); ?>" width="100%"><br>
                                    <h4 class="mt-4">ประกาศรับสมัครงาน</h4>
                                </a>
                            </div>
                            <div class="col-6 mb-4">
                                <a href="<?php echo e(route('user.notice.notice.index', ['type'=>'announce'])); ?>" class="gridItem">
                                    <img src="<?php echo e(_asset('svg/pink/undraw_heavy_box_agqi.svg')); ?>" width="100%"><br>
                                    <h4 class="mt-4">ประกาศทั่วไป</h4>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
    <?php endif; ?>


    <?php if(_in_any_role(['printery'])): ?>
    <div class="row justify-content-center">
        <div class="col-md-6 col-12">
            <div class="card">
                <div class="card-body">
                    
                    <div class="row justify-content-center">

                        <div class="col-6 mb-4">
                            <a href="<?php echo e(route('user.printing.record.index')); ?>" class="gridItem">
                                <img src="<?php echo e(_asset('svg/pink/undraw_Notebook_re_id0r.svg')); ?>" width="100%"><br>
                                <h4 class="mt-4">บันทึกการสั่งพิมพ์</h4>
                            </a>
                        </div>
                        
                    </div>
                </div>
            </div>
        </div>
        </div>
    <?php endif; ?>
    
<?php $__env->stopSection(); ?>


<?php $__env->startPush('css-stack'); ?>
    <style type="text/css">
    </style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Projects\Web\htdocs\ssp4-v11\Themes\Multikart\views/home.blade.php ENDPATH**/ ?>