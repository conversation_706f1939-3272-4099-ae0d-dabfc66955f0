




<?php echo $__env->make("school::partials.modal-select-school", array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php $__env->startSection('content'); ?>
    
    <?php echo Form::open(['route' => ['user.saleprinting.sales.store', []], 'enctype' => 'multipart/form-data']); ?>

    <div class="row justify-content-center">
        <h2 class="col-12 mb-5 text-center" style="color: #ff8084 ">บันทึกการพบลูกค้า</h2>
    </div>
    <div class="row justify-content-center">
        <div class="col-lg-7 col-md-9 p-0 card-right">

            <!--  Section 1  -->

            <div class="card">
                <div class="card-header">
                    <h4>ข้อมูลโรงเรียน ร้านค้า หน่วยงาน</h4>
                </div>
                <div class="card-body">

                    <?php if ($__env->exists("saleprinting::member.sales.partials.create-fields.school-data")) echo $__env->make("saleprinting::member.sales.partials.create-fields.school-data", array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

                </div>
            </div>


            <!--  Section 2  -->

            <div class="card tab2-card">
                <div class="card-header">
                    <h4>ข้อมูลลูกค้า 1</h4>
                </div>
                <div class="card-body">
                    <?php if ($__env->exists("saleprinting::member.sales.partials.create-fields.customer-data-1")) echo $__env->make("saleprinting::member.sales.partials.create-fields.customer-data-1", array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                </div>
            </div>

            <div class="card tab2-card">
                <div class="card-header">
                    <h4>ข้อมูลลูกค้า 2</h4>
                </div>
                <div class="card-body">
                    <?php if ($__env->exists("saleprinting::member.sales.partials.create-fields.customer-data-2")) echo $__env->make("saleprinting::member.sales.partials.create-fields.customer-data-2", array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                </div>
            </div>


            <!--  Section 3  -->

            <div class="card tab2-card">
                <div class="card-header">
                    <h4>ข้อมูลการขาย</h4>
                </div>
                <div class="card-body">
                    <?php if ($__env->exists("saleprinting::member.sales.partials.create-fields.presentation-data")) echo $__env->make("saleprinting::member.sales.partials.create-fields.presentation-data", array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                </div>
            </div>


            <!--  Section 4  -->

            

            <!--  Section 5  -->

            <div class="card tab2-card">
                <div class="card-header">
                    <h4>ผลการตอบรับจากลูกค้า</h4>
                </div>
                <div class="card-body">
                    <?php if ($__env->exists("saleprinting::member.sales.partials.create-fields.result-data")) echo $__env->make("saleprinting::member.sales.partials.create-fields.result-data", array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                </div>
            </div>

            <div class="card tab2-card">
                
                <div class="card-body">
                    <?php if ($__env->exists("saleprinting::member.sales.partials.create-fields.result-demand")) echo $__env->make("saleprinting::member.sales.partials.create-fields.result-demand", array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                </div>
            </div>


            <!--  Section 6  -->

            <div class="card tab2-card">
                <div class="card-header">
                    <h4>ภาพกิจกรรม</h4>
                </div>
                <div class="card-body">

                    <?php if ($__env->exists("saleprinting::member.sales.partials.create-fields.images-data")) echo $__env->make("saleprinting::member.sales.partials.create-fields.images-data", array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

                    <div class="row">
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary btn-block btn-flat">SAVE</button>
                        </div>
                    </div>
                </div>
            </div>


        </div>
    </div>
    <?php echo Form::close(); ?>

<?php $__env->stopSection(); ?>

<?php if ($__env->exists("saleprinting::member.sales.partials.scripts")) echo $__env->make("saleprinting::member.sales.partials.scripts", array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

<?php echo $__env->make('layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Projects\Web\htdocs\ssp4-v11\Modules/SalePrinting/Resources/views/member/sales/create.blade.php ENDPATH**/ ?>