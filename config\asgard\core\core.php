<?php

return [


    /*
    |--------------------------------------------------------------------------
    | The format that'll be used for display datetime
    |--------------------------------------------------------------------------
    */
    'date-format' => "d M Y",

    'time-format' => "h:i A",

    'datetime-format' => "d M Y h:i A",

    'datetime-short-format' => "d M y H:i",

    'sql-format' => [
        'minute' => '%Y-%m-%d %H:%i',
        'hour' => '%Y-%m-%d %H',
        'day' => '%Y-%m-%d',
        'week' => '%x-%v',
        'month' => '%Y-%m',
        'year' => '%Y',
    ],
    'php-format' => [
        'minute' => 'Y-m-d H:i',
        'hour' => 'Y-m-d H',
        'day' => 'Y-m-d',
        'week' => 'x-v',
        'month' => 'Y-m',
        'year' => 'Y',
    ],

    /*
    |--------------------------------------------------------------------------
    | The prefix that'll be used for the administration
    |--------------------------------------------------------------------------
    */
    'admin-prefix' => 'backend',

    /*
    |--------------------------------------------------------------------------
    | Location where your themes are located
    |--------------------------------------------------------------------------
    */
    'themes_path' => base_path() . '/Themes',

    /*
    |--------------------------------------------------------------------------
    | Which administration theme to use for the back end interface
    |--------------------------------------------------------------------------
    */
    'admin-theme' => 'AdminLTE',

    /*
    |--------------------------------------------------------------------------
    | AdminLTE skin
    |--------------------------------------------------------------------------
    | You can customize the AdminLTE colors with this setting. The following
    | colors are available for you to use: skin-blue, skin-green,
    | skin-black, skin-purple, skin-red and skin-yellow.
    */
    'skin' => 'skin-yellow',

    /*
    |--------------------------------------------------------------------------
    | WYSIWYG Backend Editor
    |--------------------------------------------------------------------------
    | Define which editor you would like to use for the backend wysiwygs.
    | These classes are event handlers, listening to EditorIsRendering
    | you can define your own handlers and use them here
    | Options:
    | - \Modules\Core\Events\Handlers\LoadCkEditor::class
    | - \Modules\Core\Events\Handlers\LoadSimpleMde::class
    */
    'wysiwyg-handler' => \Modules\Core\Events\Handlers\LoadTinyMCE::class,
    
    /*
    |--------------------------------------------------------------------------
    | Custom CKeditor configuration file
    |--------------------------------------------------------------------------
    | Define a custom CKeditor configuration file to instead of the one
    | provided by default. This is useful if you wish to customise
    | the toolbar and other possible options.
    */
    'ckeditor-config-file-path' => '',

    /*
    |--------------------------------------------------------------------------
    | Middleware
    |--------------------------------------------------------------------------
    | You can customise the Middleware that should be loaded.
    | The localizationRedirect middleware is automatically loaded for both
    | Backend and Frontend routes.
    */
    'middleware' => [
        'backend' => [
            'auth.admin',
        ],
        'frontend' => [
        ],
        'api' => [
            'api',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Define which assets will be available through the asset manager
    |--------------------------------------------------------------------------
    | These assets are registered on the asset manager
    */
    'admin-assets' => [
        // Css
        'main.css' => ['module' => 'core:css/main.css'],
        'bootstrap.css' => ['theme' => 'vendor/bootstrap/dist/css/bootstrap.min.css'],
        'alertify.core.css' => ['theme' => 'css/vendor/alertify/alertify.core.css'],
        'alertify.default.css' => ['theme' => 'css/vendor/alertify/alertify.default.css'],
        'dataTables.bootstrap.css' => ['theme' => 'vendor/datatables.net-bs/css/dataTables.bootstrap.min.css'],
        'icheck.blue.css' => ['theme' => 'vendor/iCheck/skins/flat/blue.css'],
        'AdminLTE.css' => ['theme' => 'vendor/admin-lte/dist/css/AdminLTE.css'],
        'AdminLTE.all.skins.css' => ['theme' => 'vendor/admin-lte/dist/css/skins/_all-skins.min.css'],
        'asgard.css' => ['theme' => 'css/asgard.css'],
        'flag-icon.css' => ['theme' => 'vendor/flag-icon-css/css/flag-icon.min.css'],
        //'gridstack.css' => ['module' => 'dashboard:vendor/gridstack/dist/gridstack.min.css'],
        'gridstack.css' => ['module' => 'dashboard:gridstack/gridstack.min.css'],
        // 'daterangepicker.css' => ['theme' => 'vendor/admin-lte/plugins/daterangepicker/daterangepicker-bs3.css'],
        // 'selectize.css' => ['module' => 'core:vendor/selectize/dist/css/selectize.css'],
        // 'selectize-default.css' => ['module' => 'core:vendor/selectize/dist/css/selectize.default.css'],
        'animate.css' => ['theme' => 'vendor/animate.css/animate.min.css'],
        'pace.css' => ['theme' => 'vendor/admin-lte/plugins/pace/pace.min.css'],
        // 'simplemde.css' => ['theme' => 'vendor/simplemde/dist/simplemde.min.css'],
        // Javascript
        'bootstrap.js' => ['theme' => 'vendor/bootstrap/dist/js/bootstrap.min.js'],
        'font-awesome.js' => ['cdn' => 'https://use.fontawesome.com/releases/v5.7.1/js/all.js'],
        'font-awesome-v4-shims.js' => ['cdn' => 'https://use.fontawesome.com/releases/v5.7.1/js/v4-shims.js'],
        'mousetrap.js' => ['theme' => 'js/vendor/mousetrap.min.js'],
        'alertify.js' => ['theme' => 'js/vendor/alertify/alertify.js'],
        'icheck.js' => ['theme' => 'vendor/iCheck/icheck.min.js'],
        'jquery.dataTables.js' => ['theme' => 'vendor/datatables.net/js/jquery.dataTables.min.js'],
        'dataTables.bootstrap.js' => ['theme' => 'vendor/datatables.net-bs/js/dataTables.bootstrap.min.js'],
        'jquery.slug.js' => ['theme' => 'js/vendor/jquery.slug.js'],
        'adminlte.js' => ['theme' => 'vendor/admin-lte/dist/js/adminlte.min.js'],
        'keypressAction.js' => ['module' => 'core:js/keypressAction.js'],
        'lodash.js' => ['module' => 'dashboard:vendor/lodash/lodash.min.js'],
        'jquery-ui-core.js' => ['module' => 'dashboard:vendor/jquery-ui/ui/minified/core.min.js'],
        'jquery-ui-widget.js' => ['module' => 'dashboard:vendor/jquery-ui/ui/minified/widget.min.js'],
        'jquery-ui-mouse.js' => ['module' => 'dashboard:vendor/jquery-ui/ui/minified/mouse.min.js'],
        'jquery-ui-draggable.js' => ['module' => 'dashboard:vendor/jquery-ui/ui/minified/draggable.min.js'],
        'jquery-ui-resizable.js' => ['module' => 'dashboard:vendor/jquery-ui/ui/minified/resizable.min.js'],
        //'gridstack.js' => ['module' => 'dashboard:vendor/gridstack/dist/gridstack.min.js'],
        'gridstack.js' => ['module' => 'dashboard:gridstack/gridstack.min.js'],
        // 'daterangepicker.js' => ['theme' => 'vendor/admin-lte/plugins/daterangepicker/daterangepicker.js'],
        // 'selectize.js' => ['module' => 'core:vendor/selectize/dist/js/standalone/selectize.min.js'],
        'main.js' => ['theme' => 'js/main.js'],
        'chart.js' => ['theme' => 'vendor/admin-lte/plugins/chartjs/Chart.js'],
        'pace.js' => ['theme' => 'vendor/admin-lte/plugins/pace/pace.min.js'],
        // 'moment.js' => ['theme' => 'vendor/admin-lte/plugins/daterangepicker/moment.min.js'],
        // 'simplemde.js' => ['theme' => 'vendor/simplemde/dist/simplemde.min.js'],
         
        // Custom
        'functions.js' => ['module' => 'core:js/functions.js'],
        
        // DateRangePicker
        'moment.js' => ['module' => 'core:vendor/daterangepicker/moment.min.js'],
        'daterangepicker.css' => ['module' => 'core:vendor/daterangepicker/daterangepicker-bs3.css'],
        'daterangepicker.js' => ['module' => 'core:vendor/daterangepicker/daterangepicker.js'],

        // DateTimePicker
        'datetimepicker.css' => ['module' => 'core:vendor/datetimepicker/bootstrap-datetimepicker.css'],
        'datetimepicker.js' => ['module' => 'core:vendor/datetimepicker/bootstrap-datetimepicker.js'],
        'datetimepicker-th.js' => ['module' => 'core:vendor/datetimepicker/locales/bootstrap-datetimepicker.th.js'],

        // DatePicker
        'datepicker.css' => ['module' => 'core:vendor/datepicker/datepicker3.css'],
        'datepicker.js' => ['module' => 'core:vendor/datepicker/bootstrap-datepicker.js'],
        'datepicker-th.js' => ['module' => 'core:vendor/datepicker/locales/bootstrap-datepicker.th.js'],

        // TimePicker
        'timepicker.css' => ['module' => 'core:vendor/timepicker/bootstrap-timepicker.min.css'],
        'timepicker.js' => ['module' => 'core:vendor/timepicker/bootstrap-timepicker.min.js'],

        // SweetAlert2
        // 'sweetalert2.css' => ['module' => 'core:vendor/sweetalert2/v7.15.1/sweetalert2.min.css'],
        // 'sweetalert2.js' => ['module' => 'core:vendor/sweetalert2/v7.15.1/sweetalert2.all.min.js'],
        'sweetalert2.css' => ['module' => 'core:vendor/sweetalert2/v11.1.5/sweetalert2.min.css'],
        'sweetalert2.js' => ['module' => 'core:vendor/sweetalert2/v11.1.5/sweetalert2.all.min.js'],

        // Select2
        'select2.css' => ['module' => 'core:vendor/select2/4.1.0-rc.0/select2.min.css'],
        'select2.js' => ['module' => 'core:vendor/select2/4.1.0-rc.0/select2.min.js'],

        // Selectize
        'selectize.css' => ['module' => 'core:vendor/selectize/dist/css/selectize.css'],
        'selectize-default.css' => ['module' => 'core:vendor/selectize/dist/css/selectize.default.css'],
        'selectize-bootstrap2.css' => ['module' => 'core:vendor/selectize/dist/css/selectize.bootstrap2.css'],
        'selectize-bootstrap3.css' => ['module' => 'core:vendor/selectize/dist/css/selectize.bootstrap3.css'],
        'selectize-legacy.css' => ['module' => 'core:vendor/selectize/dist/css/selectize.legacy.css'],
        'selectize.js' => ['module' => 'core:vendor/selectize/dist/js/standalone/selectize.min.js'],

        // SimpleMDE
        'simplemde.css' => ['module' => 'core:vendor/simplemde/dist/simplemde.min.css'],
        'simplemde.js' => ['module' => 'core:vendor/simplemde/dist/simplemde.min.js'],

        // TinyMCE
        'tinymce.css' => ['module' => 'core:vendor/tinymce/skins/default/skin.min.css'],
        'tinymce.js' => ['module' => 'core:vendor/tinymce/tinymce.min.js'],
        'jquery.tinymce.js' => ['module' => 'core:vendor/tinymce/jquery.tinymce.min.js'],
        'tinymce.istylesmedia.js' => ['module' => 'core:vendor/tinymce/plugins/istylesmedia/plugin.min.js'],

        // CKEditor
        'ckeditor.js' => ['theme' => 'js/vendor/ckeditor/ckeditor.js'],

        // Font Awesome
        'font-awesome5.css' => ['module' => 'core:vendor/font-awesome/v5.8.2/css/all.min.css'],
        'font-awesome5.js' => ['module' => 'core:vendor/font-awesome/v5.8.2/js/all.min.js'],

        // jQuery Touchable Drag And Drop 
        'jquery.draganddrop.css' => ['module' => 'core:vendor/jquery.draganddrop/src/draganddrop.css'],
        'jquery.draganddrop.js' => ['module' => 'core:vendor/jquery.draganddrop/src/draganddrop.js'],

        // Clipboard
        'clipboard.js' => ['module' => 'core:vendor/clipboard/clipboard.min.js'],
        // 'clipboard.js' => ['theme' => 'vendor/clipboard/dist/clipboard.min.js'],

        // Bootstrap
        // 'bootstrap.css' => ['module' => 'core:vendor/bootstrap/v3.3.7/dist/css/bootstrap.min.css'],
        // 'bootstrap.js' => ['module' => 'core:vendor/bootstrap/v3.3.7/dist/js/bootstrap.min.js'],

    ],
  
    'editor-assets' => [
        // SimpleMDE
        // 'simplemde.css' => ['module' => 'core:vendor/simplemde/dist/simplemde.min.css'],
        // 'simplemde.js' => ['module' => 'core:vendor/simplemde/dist/simplemde.min.js'],

        // TinyMCE
        'tinymce.css' => ['module' => 'core:vendor/tinymce/skins/default/skin.min.css'],
        'tinymce.js' => ['module' => 'core:vendor/tinymce/tinymce.min.js'],
        'jquery.tinymce.js' => ['module' => 'core:vendor/tinymce/jquery.tinymce.min.js'],
        'tinymce.istylesmedia.js' => ['module' => 'core:vendor/tinymce/plugins/istylesmedia/plugin.min.js'],

        // CKEditor
        'ckeditor.js' => ['theme' => 'js/vendor/ckeditor/ckeditor.js'],
    ],

    'datatable-assets' => [
        // DataTables
        // - Css
        'dataTables.bootstrap.css' => ['module' => 'core:vendor/datatables.net-bs/css/dataTables.bootstrap.min.css'],
        'dataTables.buttons.css' => ['module' => 'core:vendor/datatables.net/extensions/Buttons-1.5.1/css/buttons.dataTables.min.css'],
        'dataTables.buttons-bs.css' => ['module' => 'core:vendor/datatables.net/extensions/Buttons-1.5.1/css/buttons.bootstrap.min.css'],
        'dataTables.fixedHeader-bs.css' => ['module' => 'core:vendor/datatables.net/extensions/FixedHeader-3.1.3/css/fixedHeader.bootstrap.min.css'],

        // - Javascript
        'jquery.dataTables.js' => ['module' => 'core:vendor/datatables.net/js/jquery.dataTables.min.js'],
        'dataTables.bootstrap.js' => ['module' => 'core:vendor/datatables.net-bs/js/dataTables.bootstrap.min.js'],
        'dataTables.buttons.js' => ['module' => 'core:vendor/datatables.net/extensions/Buttons-1.5.1/js/dataTables.buttons.min.js'],
        'dataTables.buttons-bs.js' => ['module' => 'core:vendor/datatables.net/extensions/Buttons-1.5.1/js/buttons.bootstrap.min.js'],
        'dataTables.buttons-print.js' => ['module' => 'core:vendor/datatables.net/extensions/Buttons-1.5.1/js/buttons.print.js'],
        'dataTables.buttons-html5.js' => ['module' => 'core:vendor/datatables.net/extensions/Buttons-1.5.1/js/buttons.html5.min.js'],
        'dataTables.buttons-colVis.js' => ['module' => 'core:vendor/datatables.net/extensions/Buttons-1.5.1/js/buttons.colVis.min.js'],
        'dataTables.jszip.js' => ['module' => 'core:vendor/datatables.net/extensions/JSZip-2.5.0/jszip.min.js'],
        'dataTables.pdfmake.js' => ['module' => 'core:vendor/datatables.net/extensions/pdfmake-0.1.32/pdfmake.min.js'],
        'dataTables.custom_fonts.js' => ['module' => 'core:vendor/datatables.net/extensions/pdfmake-0.1.32/custom_fonts.js'],
        'dataTables.fixedHeader.js' => ['module' => 'core:vendor/datatables.net/extensions/FixedHeader-3.1.3/js/dataTables.fixedHeader.min.js'],
        'dataTables.responsive.js' => ['module' => 'core:vendor/datatables.net/extensions/Responsive-2.2.1/js/dataTables.responsive.min.js'],
        'dataTables.paginationInput.js' => ['module' => 'core:vendor/datatables.net/plugins/pagination/input.js'],

        'istyles.dataTables.config.js' => ['module' => 'core:vendor/datatables.net/js/istyles.dataTables.config.js'],
        'istyles.dataTables.extensions.js' => ['module' => 'core:vendor/datatables.net/js/istyles.dataTables.extensions.js'],
    ],

    /*
    |--------------------------------------------------------------------------
    | Define which default assets will always be included in your pages
    | through the asset pipeline
    |--------------------------------------------------------------------------
    */
    'admin-required-assets' => [
        'css' => [
            'bootstrap.css',
            'font-awesome5.css',
            // 'dataTables.bootstrap.css',
            'icheck.blue.css',
            'AdminLTE.css',
            'AdminLTE.all.skins.css',
            'animate.css',
            'pace.css',
            'select2.css',
            // 'selectize-default.css',
            'asgard.css',
            'flag-icon.css',
            'sweetalert2.css',
            'main.css',
        ],
        'js' => [
            'bootstrap.js',
            'font-awesome5.js',
            // 'font-awesome.js',
            // 'font-awesome-v4-shims.js',
            'mousetrap.js',
            'icheck.js',
            // 'jquery.dataTables.js',
            // 'dataTables.bootstrap.js',
            'jquery.slug.js',
            'keypressAction.js',
            'adminlte.js',
            'pace.js',
            'select2.js',
            // 'selectize.js',
            'sweetalert2.js',
            'clipboard.js',
            'main.js',
            'functions.js',
        ],
    ],
 
    'datatable-required-assets' => [
        'css' => [
            'dataTables.bootstrap.css',
            'dataTables.fixedHeader-bs.css'
        ],
        'js' => [
            'jquery.dataTables.js'
            , 'istyles.dataTables.extensions.js'
            , 'istyles.dataTables.config.js'
        ]
    ],

    /*
    |--------------------------------------------------------------------------
    | Enable module view overrides at theme locations
    |--------------------------------------------------------------------------
    | By default you can only have module views in resources/views/asgard/[module]
    | setting this setting to true will add ability for you to store those views
    | in any of front or backend themes in my-theme/views/modules/[module]/...
    |
    | useViewNamespaces.backend-theme needs to be enabled at module level
    */
    'enable-theme-overrides' => false,

    /*
    |--------------------------------------------------------------------------
    | Check if asgard was installed
    |--------------------------------------------------------------------------
    */
    'is_installed' => env('INSTALLED', false),
];
