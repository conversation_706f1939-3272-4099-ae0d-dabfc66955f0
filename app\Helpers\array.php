<?php

use Illuminate\Support\Arr;

if (!function_exists('_json_encode')) {
    function _json_encode($array, $options = 0, $depth = 512)
    {
        return json_encode($array, !$options ? JSON_UNESCAPED_UNICODE : $options, $depth);
    }
}
if (!function_exists('_json_decode')) {
    function _json_decode($string, $assoc = true, $stripcslashes = true)
    {
        $string = trim($string);
        if ($string == '')
            return array();
        else {
            // Remove all non printable characters in a string
            // https://stackoverflow.com/questions/1176904/php-how-to-remove-all-non-printable-characters-in-a-string
            $dataBal_aji = preg_replace('/[\x00-\x1F\x7F-\xFF]/', '', $string);

            if ($stripcslashes) {
                $dataBal_aji = str_replace('\"', '"', $dataBal_aji);
                $dataBal_aji = json_decode($dataBal_aji, $assoc);
                $dataBal_aji = _array_map_recursive('stripcslashes', _array_wrap($dataBal_aji));
            }else
                $dataBal_aji = json_decode($dataBal_aji, true);
            return $dataBal_aji;
        }
    }
}

if (!function_exists('_json_text_to_array_text')) {
    function _json_text_to_array_text($json_txt)
    {
        return str_replace(["{", "}", "\":"], ["[", "]", "\"=>"], $json_txt);
    }
}

if (!function_exists('_array_map_recursive')) {
    function _array_map_recursive($callback, $array)
    {
        foreach ($array as $key => $value) {
            if (is_array($array[$key])) {
                $array[$key] = _array_map_recursive($callback, $array[$key]);
            } else {
                $array[$key] = call_user_func($callback, $array[$key]);
            }
        }
        return $array;
    }
}

if (!function_exists('_array_to_csv')) {
    function _array_to_csv($array, $delimiter = ',', $enclosure = '"')
    {
        $contents = '';
        $handle = fopen('php://temp', 'r+');
        foreach ($array as $line) {
            fputcsv($handle, $line, $delimiter, $enclosure);
        }
        rewind($handle);
        while (!feof($handle)) {
            $contents .= fread($handle, 8192);
        }
        fclose($handle);
        return $contents;
    }
}

if (!function_exists('_array_to_db')) {
    function _array_to_db($array)
    {
        $result = _json_encode($array, JSON_UNESCAPED_UNICODE);
        if (!$result)
            $result = implode(', ', $array);
        return $result;
    }
}

if (!function_exists('_db_to_array')) {
    function _db_to_array($json)
    {
        $result = json_decode($json, true);
        if (!$result)
            $result = explode(', ', $json);
        return $result;
    }
}

if (!function_exists('_array_get')) {
    function _array_get($data, $index = null, $default = false)
    {
        if (is_array($data))
            $data = isset($data[$index]) ? $data[$index] : ($default === false ? end($data) : $default);
        return $data;
    }
}

if (!function_exists('_array_lang')) {
    function _array_lang($input, $lang = false)
    {
        $str = $input;
        if (is_array($input)) {
            if (!$lang)
                $lang = session('locale');
            if (isset($input[$lang]) && $input[$lang] != '') {
                $str = $input[$lang];
            } else {
                $input = array_filter($input, function ($value) {
                    return $value !== '';
                });
                $str = count($input) ? Arr::first($input) : '';
            }
        }
        return $str;
    }
}

if (!function_exists('_array_to_object')) {
    /**
     * Convert array to object
     *
     * @param Array $array
     * @return Array
     */
    function _array_to_object($array)
    {
        return json_decode(_json_encode($array), false);
    }
}

if (!function_exists('_array_default')) {
    /**
     * Set default of array key
     *
     * @param Array $default
     * @param Array $array
     * @return Array
     */
    function _array_default($default, &$array)
    {
        if (!is_array($array))
            $array = [];
        foreach ($default as $k => $v)
            $array[$k] = isset($array[$k]) ? $array[$k] : $default[$k];
    }
}

if (!function_exists('_array_dot_revert')) {
    /**
     * Set default of array key
     *
     * @param Array $array
     * @param Array $result
     * @param String $separator
     * @return Array
     */
    function _array_dot_revert($array, &$result = [], $separator = '.')
    {
        foreach (_array_wrap($array) as $k => $val) {
            $keys = explode($separator, $k);
            $key1 = array_shift($keys);
            if (!count($keys)) {
                $result[$key1] = $val;
            } else {
                $key2 = implode($separator, $keys);
//                $result[$key1][$key2] = $val;
                _array_dot_revert([$key2 => $val], $result[$key1], $separator);
            }
        }
        return $result;
    }
}

if (!function_exists('_xml_to_array')) {
    function _xml_to_array($xml)
    {
        return json_decode(_json_encode(simplexml_load_string($xml, null, LIBXML_NOCDATA)), TRUE);
    }
}

if (!function_exists('_array_to_xml')) {
    function _array_to_xml($array, $withHeader = true)
    {
        $header = "";
        if ($withHeader)
            $header = "<?xml version=\"1.0\" encoding=\"UTF-8\" ?>";
        $simple_xml = new \SimpleXMLElement($header . "<Request></Request>");
        _array_to_xml_recursive($simple_xml, $array);
        return $simple_xml->asXML();
    }
}

if (!function_exists('_array_to_xml_recursive')) {
    function _array_to_xml_recursive(&$xml_student_info, $array, $parentKey = null)
    {
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                if (!is_numeric($key)) {
                    if (!ctype_digit(implode('', array_keys($value)))) {
                        $subnode = $xml_student_info->addChild("$key");
                        _array_to_xml_recursive($subnode, $value, $key);
                    } else
                        _array_to_xml_recursive($xml_student_info, $value, $key);
                } else {
                    $subnode = $xml_student_info->addChild($parentKey);
                    _array_to_xml_recursive($subnode, $value, $parentKey);
                }
            } else {
                if (!is_numeric($key))
                    $xml_student_info->addChild("$key", htmlspecialchars("$value"));
                else
                    $xml_student_info->addChild("$parentKey", htmlspecialchars("$value"));
            }
        }
    }
}

if (!function_exists('_can_loop')) {
    /**
     * Check can loop
     *
     * @return bool
     */
    function _can_loop($var, $min_length = 0)
    {
        return ((is_array($var) && count($var) > $min_length) || (is_object($var) && (method_exists($var, 'count') ? ($var->count() > $min_length) : ($var->length > $min_length))));
    }

}

if (!function_exists('_array_wrap')) {
    function _array_wrap($var, $default = [], $min_length = 0)
    {
        return _can_loop($var, $min_length) ? $var : (_can_loop($default, $min_length) ? $default : []);
    }
}

if (!function_exists('_in_array')) {
    function _in_array($item, $array = [])
    {
        return in_array($item, _array_wrap($array));
    }
}

if (!function_exists('_array_only')) {
    function _array_only($arrayCollect, $keys)
    {
        return is_object($arrayCollect) ? optional($arrayCollect)->only($keys) : Arr::only(Arr::wrap($arrayCollect), $keys);
    }
}
