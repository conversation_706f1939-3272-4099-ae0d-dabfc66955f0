<?php

/*
 * CURL
 * */

use Illuminate\Support\Arr;

if (!function_exists('_dbd_company_profile')) {
    function _dbd_company_profile($cookies, $search)
    {
        $method = "GET";
        $data = null;
        if (is_numeric($search))
            $url = "https://datawarehouse.dbd.go.th/company/profile/3/{$search}";
        else
            $url = "https://datawarehouse.dbd.go.th{$search}";

        $html = _connect_curl($ch, $method, $url, $data, [
            'cookies' => "{$cookies}",
            'headers' => ['Cookie' => "{$cookies}"],
        ]);

        $dom = new \DOMDocument('1.0', 'UTF-8');
        $dom->loadHTML($html);
        $xpath = new \DOMXPath($dom);

        $entry = $xpath->query("//table[contains(@class, 'table-2column')]/tr", $dom);
        $custom = $rows = [];
        foreach ($entry as $row) {
            $cols = [];
            foreach ($xpath->query("./*", $row) as $col) {
                $cols[] = str_replace("\t", "", str_replace("\r\n", "-", $col->textContent));
            }
            if (count($cols) === 2)
                $rows[$cols[0]] = $cols[1];
            else $custom[] = $cols[0];
        }

        $rows[$custom[1]] = $custom[2];
        unset($rows[""]);
        return $rows;
    }
}

/*
 * CURL
 * */
if (!function_exists('_dbd_profit_loss')) {
    function _dbd_profit_loss($cookies, $search, $yearFilter = "2562", $withKey = false)
    {
        $method = "POST";
        $page = "profitloss"; // balancesheet
        $data = "yearFilter={$yearFilter}&compareType=YEAR&compareBizSize=SAME&compareBizArea=TH&compareAvgType=MEDIAN&comparePage={$page}&module=JURISTIC";
//        $url = "https://datawarehouse.dbd.go.th/{$page}/year/3/{$search}";

        if (is_numeric($search))
            $url = "https://datawarehouse.dbd.go.th/{$page}/year/3/{$search}";
        else
            $url = "https://datawarehouse.dbd.go.th" . str_replace("/company/profile", "/{$page}/year", $search);

        $html = _connect_curl($ch, $method, $url, $data, [
            'cookies' => "{$cookies}",
            'headers' => ['Cookie' => "{$cookies}"],
        ]);

        $dom = new \DOMDocument('1.0', 'UTF-8');
        $dom->loadHTML($html);
        $xpath = new \DOMXPath($dom);


        $fixTable = $dom->getElementById("fixTable");
        $row = $xpath->query("//tbody/tr[@class='td-total']", $fixTable)->item(0);
        $cols = [];
        foreach ($xpath->query("./td", $row) as $col) {
            $cols[] = $col->textContent;
        }
        if ($withKey) {
            $years = [];
            for ($y = $yearFilter - 2; $y <= $yearFilter; $y++) {
                $years[] = $y;
            }
            return array_combine($years, Arr::only($cols, [1, 3, 5]));
        }
        return Arr::only($cols, [1, 3, 5]);
    }
}
/*
 * CURL
 * */
if (!function_exists('_dbd_summary')) {
    function _dbd_summary($cookies, $search, $type = null, $withKey = false)
    {
        if ($type === "fin") {
            $method = "GET";
            $data = "textSearch={$search}&sType=fin";
            $url = "https://datawarehouse.dbd.go.th/searchJuristicInfo/s/fin";
        } else { // $type == info
//            $data = "textSearch={$search}";
            $method = "POST";
            $data = "selectedType=&textStr={$search}&textDec=&fType=2&textSearch={$search}&chk=N";
            $url = "https://datawarehouse.dbd.go.th/searchJuristic";
        }
        $html = _connect_curl($ch, $method, $url, $data, [
            'cookies' => "{$cookies}",
            'headers' => ['Cookie' => "{$cookies}"],
        ]);

        $dom = new \DOMDocument('1.0', 'UTF-8');
        $dom->loadHTML($html);
        $xpath = new \DOMXPath($dom);


        $fixTable = $dom->getElementById("fixTable");
        $rows = [];
        foreach ($xpath->query("./tbody/tr", $fixTable) as $row) {
            $link = $row->getAttribute("data-href");
            $cols = [$link];
            foreach ($xpath->query("./td", $row) as $col) {
                $cols[] = $col->textContent;
            }
            $rows[] = $cols;
        }
        if ($withKey) {
            $thead = ["link"];
            foreach ($xpath->query("./thead/tr/th", $fixTable) as $col) {
                $thead[] = $col->textContent;
            }
            $rows2 = [];
            foreach ($rows as $row) {
                $rows2[] = array_combine($thead, $row);
            }
            return $rows2;
        }
        return $rows;
    }
}