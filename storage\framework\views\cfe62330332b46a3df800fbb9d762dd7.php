

<div class="row">
    <div
        class="form-group col-sm-12 <?php echo e($errors->has('res_r5.result') ? ' has-error has-feedback' : ''); ?>">
        <select name="res_r5[result]" id="result" class="form-control">
            <?php $__currentLoopData = Arr::get($selects, 'results'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $p => $result): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <option <?php echo e(old('res_r5.result') === $p ? 'selected' : ''); ?>

                    value="<?php echo e($p); ?>"><?php echo e($result); ?>

                </option>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </select>
        <?php echo $errors->first('res_r5.result', '<span class="help-block">:message</span>'); ?>

    </div>
</div>

<div class="row">
    <div
        class="form-group col-sm-12 <?php echo e($errors->has('res_r5.result_note') ? ' has-error has-feedback' : ''); ?>">
        <label for="result_note">เหตุผล/รายละเอียดเพิ่มเติม (ถ้ามี)</label>
        <textarea name="res_r5[result_note]" class="form-control"></textarea>
        <?php echo $errors->first('res_r5.result_note', '<span class="help-block">:message</span>'); ?>

    </div>
</div><?php /**PATH C:\Projects\Web\htdocs\ssp4-v11\Modules/SalePrinting/Resources/views/member/sales/partials/create-fields/result-data.blade.php ENDPATH**/ ?>