<?php $section_no = customer; ?>


<a id="btn_filter_school"  
    class="btn custom-bg-outline-gray custom-filter-label current_filter_school right_side_toggle right_side_toggle_<?php echo e($section_no); ?>"
    data-sidebar="<?php echo e($section_no); ?>" href="javascript:void(0)">
    <?php echo e(request()->customer ?? "- โรงเรียน/ร้านค้า/หน่วยงาน -"); ?>

</a>

<?php $__env->startPush('js-stack'); ?>
    
    <script id="script-filter-school" type="text/javascript">
        $(document).ready(function() {
            var qs = $('input#search-customer').quicksearch('#customer-list  li', {
                selector: '.filter_customer',
                // minValLength: 3,
                onAfter: function() {
                    $("#search-customer-result").html(this.currentMatchedResults());
                }
            });
            
        });
    </script>
<?php $__env->stopPush(); ?>


<?php $__env->startPush('sidebar-stack'); ?>

    <!-- Right sidebar Start-->
    <div class="right-sidebar right_side_bar_<?php echo e($section_no); ?>" id="right_side_bar_<?php echo e($section_no); ?>">
        <div>
            <div class="container p-0">
                <div class="modal-header p-l-20 p-r-20">
                    <div class="col-sm-8 p-0">
                        <h6 class="modal-title font-weight-bold">โรงเรียน/ร้านค้า/หน่วยงาน ที่เข้าพบ ปี
                            <?php echo e($current_fy); ?><br><span style="color: #ff8084;"><?php echo e($section['codename']); ?></span></h6>
                    </div>
                    <div class="col-sm-4 text-right p-0 right_side_close " data-section="<?php echo e($section_no); ?>">
                        <i class="mr-2 fa fa-times fa-2x"></i>
                        
                    </div>
                </div>
            </div>
            <div class="friend-list-search m-0 ">
                <div class="row">
                    <input id="search-customer" type="text" placeholder="พิมพ์เพื่อค้นหา.."><i class="fa fa-search"></i>
                    <div class="col-8 text-left p-0 pl-2">
                        <small>แสดง <span id="search-customer-result"></span> จาก <span><?php echo e($customer_fy->count()); ?></span> แถว </small>
                    </div>
                    <div class="col-4 text-right p-0 pr-2 filter_customer act-click" data-title="แสดงทั้งหมดของ ปี <?php echo e($current_fy); ?>">
                        <small>รีเซ็ต</small>
                    </div>
                </div>      
            </div>
            <div class="p-l-10 p-r-10">
                <div class="chat-box">
                    
                    <div id="customer-list" class="people-list friend-list">
                        <ul class="list">

                            <?php $__currentLoopData = $customer_fy; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $s => $sch): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li class="clearfix">
                                    
                                    
                                    
                                    
                                    <a class="about">
                                        <div class="name filter_customer text-nowrap <?php echo e($sch->_s == '' ? 'text-info' : ''); ?>"  
                                            data-customer="<?php echo e(trim($sch->_n)); ?>"  
                                            data-title="รายงานของ โรงเรียน/ร้านค้า/หน่วยงาน : <?php echo e($sch->_n); ?>">
                                            <?php echo e($s + 1); ?>. <?php echo e($sch->_n); ?> 
                                            <div class="status">&nbsp;&nbsp; - จำนวน <?php echo e($sch->_c); ?> รายการ</div>
                                        </div>
                                    </a>
                                </li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Right sidebar Ends-->

<?php $__env->stopPush(); ?>
<?php /**PATH C:\Projects\Web\htdocs\ssp4-v11\Modules/SaleMarketing/Resources/views/member/report/partials/filter-input-customer.blade.php ENDPATH**/ ?>