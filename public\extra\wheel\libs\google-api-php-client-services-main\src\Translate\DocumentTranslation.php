<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Translate;

class DocumentTranslation extends \Google\Collection
{
  protected $collection_key = 'byteStreamOutputs';
  /**
   * @var string[]
   */
  public $byteStreamOutputs;
  /**
   * @var string
   */
  public $detectedLanguageCode;
  /**
   * @var string
   */
  public $mimeType;

  /**
   * @param string[]
   */
  public function setByteStreamOutputs($byteStreamOutputs)
  {
    $this->byteStreamOutputs = $byteStreamOutputs;
  }
  /**
   * @return string[]
   */
  public function getByteStreamOutputs()
  {
    return $this->byteStreamOutputs;
  }
  /**
   * @param string
   */
  public function setDetectedLanguageCode($detectedLanguageCode)
  {
    $this->detectedLanguageCode = $detectedLanguageCode;
  }
  /**
   * @return string
   */
  public function getDetectedLanguageCode()
  {
    return $this->detectedLanguageCode;
  }
  /**
   * @param string
   */
  public function setMimeType($mimeType)
  {
    $this->mimeType = $mimeType;
  }
  /**
   * @return string
   */
  public function getMimeType()
  {
    return $this->mimeType;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(DocumentTranslation::class, 'Google_Service_Translate_DocumentTranslation');
