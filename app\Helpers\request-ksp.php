<?php

use Illuminate\Support\Arr;

if (!function_exists('_ksp_make_request')) {
    function _ksp_make_request($url = "", $data = [])
    {
        $html = _make_request('POST', $url, [], $data, []);

        $dom = new \DOMDocument('1.0');
        $dom->loadHTML('<?xml encoding="utf-8" ?>' . _tis620_to_utf8($html));
        $xpath = new \DOMXPath($dom);

        $entry = $xpath->query("//table[2]/tr", $dom);
        $rows = [];
        $keys = [];
        foreach ($entry as $r => $row) {
            if (!$r) {
                foreach ($xpath->query("./*", $row) as $c => $col) {
                    $keys[$c] = trim($col->textContent);
                }
                continue;
            }
            $cols = [];
            foreach ($xpath->query("./*", $row) as $c => $col) {
                // $textContent = str_replace("\r\n", "-", $col->textContent);
                // $textContent = str_replace("\t", "", $textContent);
                $cols[$c] = trim($col->textContent, " \t\n\r\0\x0B\xC2\xA0");
            }
            $rows[] = $cols;
        }

        return ['header' => $keys, 'data' => $rows, 'status' => Arr::get($rows, '0.0') != 'ไม่พบข้อมูล' && Arr::get($rows, '0.1') != ''];
    }
}
if (!function_exists('_ksp_id_txt_to_array')) {
    /**
     * @param string $txtID (รหัสประจำตัวประชาชน ex: 3909900466451)
     */
    function _ksp_id_txt_to_array($txtID = "")
    {
        if (is_array($txtID))
            return $txtID;
        $txtID = str_replace('-', '', $txtID);
        return [
            "txtID0" => substr($txtID, 0, 1),
            "txtID1" => substr($txtID, 1, 4),
            "txtID2" => substr($txtID, 5, 5),
            "txtID3" => substr($txtID, 10, 2),
            "txtID4" => substr($txtID, 12, 1),
        ];
    }
}
if (!function_exists('_ksp_license_regis')) {
    /**
     * ข้อมูล "ขอขึ้นทะเบียนรับใบอนุญาตฯ"
     * licensetype 10
     * 
     * @param string $txtID (รหัสประจำตัวประชาชน ex: 3909900466451)
     * @param bool $redirectSSP (ignore timeout on some server)
     */
    function _ksp_license_regis($txtID = "", $redirectSSP = false)
    {
        if ($redirectSSP) {
            $data = ["txtID" => $txtID];
            $url = "https://suksapanpanit.com/crm/api/ksp/_ksp_license_regis.php";
        } else {
            $arrID = _ksp_id_txt_to_array($txtID);
            $data = array_merge($arrID,  [
                "action" => '1',
                "ddlEDU_OCCUPY_TYPE" => '10',
                "txtPassportID" => '',
                "licensetype" => ['10'],
            ]);
            $url = "https://www.ksp.or.th/service/license_search_ajax.php";
        }
        $result = _ksp_make_request($url, $data);
        $result['title'] = "ขอขึ้นทะเบียนรับใบอนุญาตฯ";
        return $result;
    }
}
if (!function_exists('_ksp_license_renew')) {
    /**
     * ข้อมูล "ขอต่ออายุใบอนุญาตฯ"
     * licensetype 20
     * 
     * @param string $txtID (รหัสประจำตัวประชาชน ex: 3909900466451)
     * @param bool $redirectSSP (ignore timeout on some server)
     */
    function _ksp_license_renew($txtID = "", $redirectSSP = false)
    {

        if ($redirectSSP) {
            $data = ["txtID" => $txtID];
            $url = "https://suksapanpanit.com/crm/api/ksp/_ksp_license_renew.php";
        } else {
            $arrID = _ksp_id_txt_to_array($txtID);
            $data = array_merge($arrID,  [
                "action" => '1',
                "ddlEDU_OCCUPY_TYPE" => '10', // 10 ครู, 12 ผู้บริหารสถานศึกษา, 13 ผู้บริหารการศึกษา, 14 ศึกษานิเทศก์
                "txtPassportID" => '',
                "licensetype" => ['20'],
            ]);
            $url = "https://www.ksp.or.th/service/license_search_renew_ajax.php";
        }
        $result = _ksp_make_request($url, $data);
        $result['title'] = "ขอต่ออายุใบอนุญาตฯ";
        return $result;
    }
}
if (!function_exists('_ksp_license_teach')) {
    /**
     * ข้อมูล "ใบอนุญาตปฏิบัติการสอน"
     * licensetype 40
     * 
     * @param string $txtID (รหัสประจำตัวประชาชน ex: 3909900466451)
     * @param bool $redirectSSP (ignore timeout on some server)
     */
    function _ksp_license_teach($txtID = "", $redirectSSP = false)
    {
        if ($redirectSSP) {
            $data = ["txtID" => $txtID];
            $url = "https://suksapanpanit.com/crm/api/ksp/_ksp_license_teach.php";
        } else {
            $arrID = _ksp_id_txt_to_array($txtID);
            $data = array_merge($arrID,  [
                "action" => '1',
                "ddlEDU_OCCUPY_TYPE" => '10',
                "txtPassportID" => '',
                "licensetype" => ['40'],
            ]);
            $url = "https://www.ksp.or.th/service/license_search_teach_ajax.php";
        }
        $result = _ksp_make_request($url, $data);
        $result['title'] = "ใบอนุญาตปฏิบัติการสอน";
        return $result;
    }
}
if (!function_exists('_ksp_allow_teach')) {
    /**
     * ข้อมูล "ขออนุญาตประกอบวิชาชีพโดยไม่มีใบอนุญาต"
     * licensetype 50
     * 
     * @param string $txtID (รหัสประจำตัวประชาชน ex: 3909900466451)
     * @param bool $redirectSSP (ignore timeout on some server)
     */
    function _ksp_allow_teach($txtID = "", $redirectSSP = false)
    {
        if ($redirectSSP) {
            $data = ["txtID" => $txtID];
            $url = "https://suksapanpanit.com/crm/api/ksp/_ksp_allow_teach.php";
        } else {
            $arrID = _ksp_id_txt_to_array($txtID);
            $data = array_merge($arrID,  [
                "action" => '1',
                "ddlEDU_OCCUPY_TYPE" => '10',
                "txtPassportID" => '',
                "licensetype" => ['50'],
            ]);
            $url = "https://www.ksp.or.th/service/license_search_allow_ajax.php";
        }
        $result = _ksp_make_request($url, $data);
        $result['title'] = "ขออนุญาตประกอบวิชาชีพโดยไม่มีใบอนุญาต";
        return $result;
    }
}

if (!function_exists('_ksp_license_tepis')) {
    /**
     * ข้อมูล "ใบอนุญาตปฏิบัติการสอน" 
     * licensetype 50
     * 
     * @param string $txtID (เลขที่ใบอนุญาต ex: 62109000245019)
     * @param bool $redirectSSP (ignore timeout on some server)
     */
    function _ksp_license_tepis($txtID = "", $redirectSSP = false)
    {
        if ($redirectSSP) {
            $data = ["txtID" => $txtID];
            $url = "https://suksapanpanit.com/crm/api/ksp/_ksp_license_tepis.php";
        } else {
            // $arrID = _ksp_id_txt_to_array($txtID);
            $data = [
                "action" => '1',
                "search" => '1',
                "txtLicenseID" => str_replace(' ', '', $txtID), 
                "txtAllowID" => '',
            ];
            $url = "https://www.ksp.or.th/service/tepis_license_ajax.php";
        }
        $result = _ksp_make_request($url, $data);
        $result['title'] = "ใบอนุญาตปฏิบัติการสอน";
        return $result;
    }
}
