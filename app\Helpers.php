<?php
$files = glob(__DIR__ . '/Helpers/*.php');
if ($files === false) {
    throw new RuntimeException("Failed to glob for function files");
}
foreach ($files as $file) {
    require_once $file;
}
unset($file);
unset($files);

use Illuminate\Contracts\Routing\UrlGenerator;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

if (!function_exists('array_dot')) {
    /**
     * Fix new laravel have no "array_dot" function 
     * but new generated modules still use "array_dot" in the template
     *
     * @param  string $path
     * @return array
     */
    function array_dot($arr = null)
    {
        return Arr::dot($arr);
    }
}

if (!function_exists('_url')) {
    /**
     * Generate a url for the application.
     *
     * @param  string $path
     * @param  mixed $parameters
     * @param  bool|null $secure
     * @return \Illuminate\Contracts\Routing\UrlGenerator|string
     */
    function _url($path = null, $parameters = [], $secure = null)
    {
        if (is_null($path)) {
            return app(UrlGenerator::class);
        }
        $start = Str::startsWith($path, ['http://', 'https://']);
        if ($start)
            return $path;
        return app(UrlGenerator::class)->to(env("APP_DIR") . "/" . ltrim($path, "/"), $parameters, $secure);
    }
}

if (!function_exists('_asset')) {
    /**
     * Generate an asset path for the application.
     *
     * @param  string $path
     * @param  bool|null $secure
     * @return string
     */
    function _asset($path, $secure = null)
    {
        return app('url')->asset(env("APP_DIR") . "/" . $path, $secure);
    }
}

if (!function_exists('_redirect')) {
    /**
     * Get an instance of the redirector.
     *
     * @param  string|null $to
     * @param  int $status
     * @param  array $headers
     * @param  bool|null $secure
     * @return \Illuminate\Routing\Redirector|\Illuminate\Http\RedirectResponse
     */
    function _redirect($to = null, $status = 302, $headers = [], $secure = null)
    {
        if (is_null($to)) {
            return app('redirect');
        }
        return app('redirect')->to(_url($to), $status, $headers, $secure);
    }
}

if (!function_exists('_redirect_intended')) {
    /**
     * Create a new redirect response to the previously intended location.
     *
     * @param  string $default
     * @param  int $status
     * @param  array $headers
     * @param  bool|null $secure
     * @return \Illuminate\Http\RedirectResponse
     */
    function _redirect_intended($default = '/', $status = 302, $headers = [], $secure = null)
    {
        $path = session()->pull('url.intended', $default);

        return app('redirect')->to(_url($path), $status, $headers, $secure);
//        return _redirect()->intended($default, $status, $headers, $secure);
    }
}



if (!function_exists('_array_combine')) {
    function _array_combine($keys =  [], $data = [], $number_fields =  [])
    {
        $data_formatted = array_combine(array_keys($keys), $data);
        if($data_formatted['null-01'] != ''){
            unset($keys['null-01']);
            array_pop($data);
        }
        $data_formatted = array_combine(array_keys($keys), $data);

        foreach ($number_fields as $number_field) {
            if (isset($data_formatted[$number_field]))
                $data_formatted[$number_field] = (float)str_replace(',', '', $data_formatted[$number_field]);
        }
        return $data_formatted;
    }
}



if (!function_exists('_calculate_yearly_diff')) {
    function _calculate_yearly_diff($index_year, $current_year = null, $prev_year = null)
    {

        $max = max([$current_year, $prev_year]);
        $diff = is_null($prev_year) ? null : $current_year - $prev_year;
//        $diff = $current_year - $prev_year;
        return [
            'value' => $current_year,
            'diff' => $diff,
            'up' => $current_year > $prev_year,
            'percent' => $diff ? round(abs($diff) * 100 / $max, 2) : null
//            'percent' => $diff ? round(abs($diff) * 100 / $max, 2) : null
        ];
    }
}
