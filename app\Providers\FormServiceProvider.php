<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Support\FormBuilder;
use App\Support\HtmlBuilder;
use Spatie\Html\Html;
use Illuminate\Support\HtmlString;

class FormServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton('form', function ($app) {
            return new FormBuilder($app->make(Html::class));
        });

        $this->app->singleton('html.custom', function ($app) {
            return new HtmlBuilder(
                $app->make(Html::class),
                $app->make('form')
            );
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Override the Html facade to use our custom implementation
        $this->app->alias('html.custom', 'Html');

        // Load the Form macros from the Core module
        $this->loadFormMacros();
    }

    /**
     * Load Form macros and register them on our FormBuilder
     */
    protected function loadFormMacros(): void
    {
        // We need to manually register the macros on our FormBuilder class
        // since the original macros.php file registers them on the Form facade

        $this->registerI18nMacros();
        $this->registerNormalMacros();
    }

    /**
     * Register i18n form macros
     */
    protected function registerI18nMacros(): void
    {
        FormBuilder::macro('i18nInput', function ($name, $title, $errors, $lang, $object = null, array $options = []) {
            /** @var FormBuilder $this */
            $options = array_merge(['class' => 'form-control', 'placeholder' => $title], $options);

            $string = "<div class='form-group " . ($errors->has($lang . '.' . $name) ? ' has-error' : '') . "'>";
            $string .= $this->label("{$lang}[{$name}]", $title);

            if (is_object($object)) {
                $currentData = $object->hasTranslation($lang) ? $object->translate($lang)->{$name} : '';
            } else {
                $currentData = '';
            }

            $string .= $this->text("{$lang}[{$name}]", old("{$lang}[{$name}]", $currentData), $options);
            $string .= $errors->first("{$lang}.{$name}", '<span class="help-block">:message</span>');
            $string .= '</div>';

            return new HtmlString($string);
        });

        FormBuilder::macro('i18nInputOfType', function ($type, $name, $title, $errors, $lang, $object = null, array $options = []) {
            /** @var FormBuilder $this */
            $options = array_merge(['class' => 'form-control', 'placeholder' => $title], $options);

            $string = "<div class='form-group " . ($errors->has($lang . '.' . $name) ? ' has-error' : '') . "'>";
            $string .= $this->label("{$lang}[{$name}]", $title);

            if (is_object($object)) {
                $currentData = $object->hasTranslation($lang) ? $object->translate($lang)->{$name} : '';
            } else {
                $currentData = '';
            }

            $string .= $this->input($type, "{$lang}[{$name}]", old("{$lang}[{$name}]", $currentData), $options);
            $string .= $errors->first("{$lang}.{$name}", '<span class="help-block">:message</span>');
            $string .= '</div>';

            return new HtmlString($string);
        });

        FormBuilder::macro('i18nTextarea', function ($name, $title, $errors, $lang, $object = null, array $options = []) {
            /** @var FormBuilder $this */
            $options = array_merge(['class' => 'ckeditor', 'rows' => 10, 'cols' => 10], $options);

            $string = "<div class='form-group " . ($errors->has($lang . '.' . $name) ? ' has-error' : '') . "'>";
            $string .= $this->label("{$lang}[{$name}]", $title);

            if (is_object($object)) {
                $currentData = $object->hasTranslation($lang) ? $object->translate($lang)->{$name} : '';
            } else {
                $currentData = '';
            }

            $string .= $this->textarea("{$lang}[$name]", old("{$lang}[{$name}]", $currentData), $options);
            $string .= $errors->first("{$lang}.{$name}", '<span class="help-block">:message</span>');
            $string .= '</div>';

            return new HtmlString($string);
        });

        FormBuilder::macro('i18nSelect', function ($name, $title, $errors, $lang, array $choice, $object = null, array $options = []) {
            /** @var FormBuilder $this */
            if (array_key_exists('multiple', $options)) {
                $nameForm = "{$lang}[$name][]";
            } else {
                $nameForm = "{$lang}[$name]";
            }

            $string = "<div class='form-group dropdown" . ($errors->has($lang . '.' . $name) ? ' has-error' : '') . "'>";
            $string .= "<label for='$nameForm'>$title</label>";

            if (is_object($object)) {
                $currentData = $object->hasTranslation($lang) ? $object->translate($lang)->{$name} : '';
            } else {
                $currentData = false;
            }

            $array_option = ['class' => 'form-control'];

            if (array_key_exists('class', $options)) {
                $array_option = ['class' => $array_option['class'] . ' ' . $options['class']];
                unset($options['class']);
            }

            $options = array_merge($array_option, $options);

            $string .= $this->select($nameForm, $choice, old($nameForm, $currentData), $options);
            $string .= $errors->first("{$lang}.{$name}", '<span class="help-block">:message</span>');
            $string .= '</div>';

            return new HtmlString($string);
        });
    }

    /**
     * Register normal form macros
     */
    protected function registerNormalMacros(): void
    {
        FormBuilder::macro('normalInput', function ($name, $title, $errors, $object = null, array $options = []) {
            /** @var FormBuilder $this */
            $options = array_merge(['class' => 'form-control', 'placeholder' => $title], $options);

            $string = "<div class='form-group " . ($errors->has($name) ? ' has-error' : '') . "'>";
            $string .= $this->label($name, $title);

            if (is_object($object)) {
                $currentData = isset($object->{$name}) ? $object->{$name} : '';
            } else {
                $currentData = null;
            }

            $string .= $this->text($name, old($name, $currentData), $options);
            $string .= $errors->first($name, '<span class="help-block">:message</span>');
            $string .= '</div>';

            return new HtmlString($string);
        });

        FormBuilder::macro('normalInputOfType', function ($type, $name, $title, $errors, $object = null, array $options = []) {
            /** @var FormBuilder $this */
            $options = array_merge(['class' => 'form-control', 'placeholder' => $title], $options);

            $string = "<div class='form-group " . ($errors->has($name) ? ' has-error' : '') . "'>";
            $string .= $this->label($name, $title);

            if (is_object($object)) {
                $currentData = isset($object->{$name}) ? $object->{$name} : '';
            } else {
                $currentData = null;
            }

            $string .= $this->input($type, $name, old($name, $currentData), $options);
            $string .= $errors->first($name, '<span class="help-block">:message</span>');
            $string .= '</div>';

            return new HtmlString($string);
        });

        FormBuilder::macro('normalTextarea', function ($name, $title, $errors, $object = null, array $options = []) {
            /** @var FormBuilder $this */
            $options = array_merge(['class' => 'ckeditor', 'rows' => 10, 'cols' => 10], $options);

            $string = "<div class='form-group " . ($errors->has($name) ? ' has-error' : '') . "'>";
            $string .= $this->label($name, $title);

            if (is_object($object)) {
                $currentData = $object->{$name} ?: '';
            } else {
                $currentData = null;
            }

            $string .= $this->textarea($name, old($name, $currentData), $options);
            $string .= $errors->first($name, '<span class="help-block">:message</span>');
            $string .= '</div>';

            return new HtmlString($string);
        });

        FormBuilder::macro('normalSelect', function ($name, $title, $errors, array $choice, $object = null, array $options = []) {
            /** @var FormBuilder $this */
            if (array_key_exists('multiple', $options)) {
                $nameForm = $name . '[]';
            } else {
                $nameForm = $name;
            }

            $string = "<div class='form-group dropdown" . ($errors->has($name) ? ' has-error' : '') . "'>";
            $string .= "<label for='$nameForm'>$title</label>";

            if (is_object($object)) {
                $currentData = isset($object->$name) ? $object->$name : '';
            } else {
                $currentData = false;
            }

            $array_option = ['class' => 'form-control'];

            if (array_key_exists('class', $options)) {
                $array_option = ['class' => $array_option['class'] . ' ' . $options['class']];
                unset($options['class']);
            }

            $options = array_merge($array_option, $options);

            $string .= $this->select($nameForm, $choice, old($nameForm, $currentData), $options);
            $string .= $errors->first($name, '<span class="help-block">:message</span>');
            $string .= '</div>';

            return new HtmlString($string);
        });

        FormBuilder::macro('normalFile', function ($name, $title, $errors, $object = null, array $options = []) {
            /** @var FormBuilder $this */
            $options = array_merge(['class' => 'form-control', 'placeholder' => $title, 'multiple' => 'multiple'], $options);

            $string = "<div class='form-group " . ($errors->has($name) ? ' has-error' : '') . "'>";
            $string .= $this->label($name, $title);

            $string .= $this->file($name, $options);
            $string .= $errors->first($name, '<span class="help-block">:message</span>');
            $string .= '</div>';

            return new HtmlString($string);
        });
    }
}
