

<?php $__env->startSection('title'); ?>
    <?php echo e(_trans('user::auth.login')); ?> | <?php echo \Illuminate\View\Factory::parentPlaceholder('title'); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make("school::partials.modal-select-school", array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php $__env->startSection('content'); ?>
    <div class="authentication-box">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-7 p-0 card-right">
                    <div class="card tab2-card">
                        <div class="card-body">
                            <ul class="nav nav-tabs nav-material" id="top-tab" role="tablist">
                                <li class="nav-item">
                                    <a class="nav-link <?php echo e(!old('profile') && !request('regis') ? 'active' : ''); ?>" id="top-login-tab"
                                        data-toggle="tab" href="#top-login" role="tab" aria-controls="top-login"
                                        aria-selected="true"><span class="icon-user mr-2"></span>Login</a>
                                </li>
                                
                            </ul>
                            <div class="tab-content" id="top-tabContent">
                                <div class="tab-pane fade <?php echo e(!old('profile') && !request('regis') ? 'show active' : ''); ?>" id="top-login"
                                    role="tabpanel" aria-labelledby="top-profile-tab">
                                    <?php if(Session::has('loginError')): ?>
                                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                            <span class="glyphicon glyphicon-exclamation-sign" aria-hidden="true"></span>
                                            
                                            <?php echo session('loginError'); ?>


                                            <button type="button" class="close" data-dismiss="alert"
                                                aria-label="Close">
                                                <span aria-hidden="true">&times;</span>
                                            </button>
                                        </div>
                                    <?php endif; ?>
                                    <?php if(Session::has('success')): ?>
                                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                                            <span class="glyphicon glyphicon-exclamation-sign" aria-hidden="true"></span>
                                            
                                            <?php echo session('success'); ?>


                                            <button type="button" class="close" data-dismiss="alert"
                                                aria-label="Close">
                                                <span aria-hidden="true">&times;</span>
                                            </button>
                                        </div>
                                    <?php endif; ?>
                                    <form enctype="multipart/form-data"
                                        class="form-validate-login form-horizontal auth-form"
                                        action="<?php echo e(_route('login.post')); ?>" method="post">
                                        <?php echo e(csrf_field()); ?>

                                        <div
                                            class="form-group has-feedback <?php echo e($errors->has('email') ? ' has-error' : ''); ?>">
                                            <input type="email" class="form-control" id="email" name="email"
                                                placeholder="<?php echo e(_trans('user::auth.email')); ?>"
                                                value="<?php echo e(old('email')); ?>">
                                            <span class="glyphicon glyphicon-envelope form-control-feedback"></span>
                                            <?php echo $errors->first('email', '<span class="help-block">:message</span>'); ?>

                                        </div>
                                        <div
                                            class="form-group has-feedback <?php echo e($errors->has('password') ? ' has-error' : ''); ?>">
                                            <input type="password" class="form-control" id="password-login"
                                                name="password" placeholder="<?php echo e(_trans('user::auth.password')); ?>"
                                                value="<?php echo e(old('password')); ?>">
                                            <span class="eye-toggle">
                                                <i class="fa fa-eye-slash"></i>
                                            </span>
                                            <span class="glyphicon glyphicon-lock form-control-feedback"></span>
                                            <?php echo $errors->first('password', '<span class="help-block">:message</span>'); ?>

                                        </div>
                                        <div class="form-terms">
                                            <div class="custom-control custom-checkbox mr-sm-2">
                                                <input type="checkbox" class="custom-control-input" name="remember_me"
                                                    checked value="1" id="customControlAutosizing">
                                                <label class="custom-control-label" for="customControlAutosizing">Remember
                                                    me</label>
                                                <a href="<?php echo e(_route('reset')); ?>" class="btn btn-default forgot-pass">lost
                                                    your password</a>
                                            </div>
                                        </div>
                                        <div class="form-button">
                                            <button class="btn btn-primary" type="submit">Login</button>
                                        </div>
                                        <div class="form-footer d-none">
                                            <span>Or Login up with social platforms</span>
                                            <ul class="social">
                                                
                                                
                                                <li><a class="fab fa-google-plus-g"
                                                        href="<?php echo e(_route('login-social', ['google', 'r' => $roles])); ?>"></a>
                                                </li>
                                                
                                                
                                                
                                                <li><a class="fab fa-facebook-f"
                                                        href="<?php echo e(_route('login-social', ['facebook', 'r' => $roles])); ?>"></a>
                                                </li>
                                                <li><a class="fab fa-line btn-agreement" data-label="Line"
                                                        data-href="<?php echo e(_route('login-social', ['line', 'r' => $roles])); ?>"></a>
                                                </li>
                                                
                                                
                                                
                                                
                                            </ul>
                                        </div>
                                    </form>
                                </div>
                                <div class="tab-pane fade <?php echo e(old('profile') || request('regis') ? 'show active' : ''); ?>" id="top-register"
                                    role="tabpanel" aria-labelledby="contact-top-tab">
                                    <div class="row justify-content-center">
                                        <div class="col-12 col-md-12">
                                            <?php if(Session::has('loginError')): ?>
                                                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                                    <span class="glyphicon glyphicon-exclamation-sign"
                                                        aria-hidden="true"></span>
                                                    
                                                    <?php echo session('loginError'); ?>


                                                    <button type="button" class="close" data-dismiss="alert"
                                                        aria-label="Close">
                                                        <span aria-hidden="true">&times;</span>
                                                    </button>
                                                </div>
                                            <?php endif; ?>
                                            <?php if(Session::has('success')): ?>
                                                <div class="alert alert-success alert-dismissible fade show" role="alert">
                                                    <span class="glyphicon glyphicon-exclamation-sign"
                                                        aria-hidden="true"></span>
                                                    
                                                    <?php echo session('success'); ?>


                                                    <button type="button" class="close" data-dismiss="alert"
                                                        aria-label="Close">
                                                        <span aria-hidden="true">&times;</span>
                                                    </button>
                                                </div>
                                            <?php endif; ?>
                                            <?php echo Form::open(['route' => 'register.post']); ?>

                                            <div class="row">
                                                <div
                                                    class="form-group col-sm-6 <?php echo e($errors->has('no') ? ' has-error has-feedback' : ''); ?>">
                                                    <input type="text" name="no" class="form-control"
                                                        placeholder="รหัสพนักงาน"
                                                        value="<?php echo e(old('no')); ?>">
                                                    <?php echo $errors->first('no', '<span class="help-block">:message</span>'); ?>

                                                </div>
                                                <div
                                                    class="form-group col-sm-6 <?php echo e($errors->has('pre_name') ? ' has-error has-feedback' : ''); ?>">
                                                    <select name="pre_name" id="pre_name" class="form-control"
                                                        placeholder="<?php echo e(_trans('user::users.form.pre_name')); ?>">
                                                        <option value="">- <?php echo e(_trans('user::users.form.pre_name')); ?> -
                                                        </option>
                                                        <?php $__currentLoopData = Arr::get($selects, 'pre_name'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $p => $pre_name): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <option <?php echo e(old('pre_name') == $pre_name ? 'selected' : ''); ?>

                                                                value="<?php echo e($pre_name); ?>"><?php echo e($pre_name); ?>

                                                            </option>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </select>
                                                    <?php echo $errors->first('pre_name', '<span class="help-block">:message</span>'); ?>

                                                </div>
                                            </div>
                                            <div class="row">
                                                <div
                                                    class="form-group col-sm-6 <?php echo e($errors->has('first_name') ? ' has-error has-feedback' : ''); ?>">
                                                    <input type="text" name="first_name" class="form-control"
                                                        placeholder="<?php echo e(_trans('user::users.form.first_name')); ?>"
                                                        value="<?php echo e(old('first_name')); ?>">
                                                    <?php echo $errors->first('first_name', '<span class="help-block">:message</span>'); ?>

                                                </div>
                                                <div
                                                    class="form-group col-sm-6 <?php echo e($errors->has('last_name') ? ' has-error has-feedback' : ''); ?>">
                                                    <input type="text" name="last_name" class="form-control"
                                                        placeholder="<?php echo e(_trans('user::users.form.last_name')); ?>"
                                                        value="<?php echo e(old('last_name')); ?>">
                                                    <?php echo $errors->first('last_name', '<span class="help-block">:message</span>'); ?>

                                                </div>
                                            </div>
                                            <div class="row">
                                                
                                                <div
                                                    class="form-group col-sm-6 <?php echo e($errors->has('role') ? ' has-error has-feedback' : ''); ?>">
                                                    <select name="role" id="roles" class="form-control"
                                                        placeholder="<?php echo e(_trans('user::users.form.roles')); ?>">
                                                        <option value="">- เลือกตำแหน่ง -
                                                        </option>
                                                        <?php $__currentLoopData = Arr::get($selects, 'roles'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $p => $roles): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <option
                                                                <?php echo e(old('role') == $roles['name'] ? 'selected' : ''); ?>

                                                                value="<?php echo e($roles['name']); ?>">
                                                                <?php echo e($roles['name']); ?>

                                                            </option>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </select>
                                                    <?php echo $errors->first('role', '<span class="help-block">:message</span>'); ?>

                                                </div>
                                                <div
                                                    class="form-group col-sm-6 <?php echo e($errors->has('section_no') ? ' has-error has-feedback' : ''); ?>">
                                                    <select name="section_no" id="section" class="form-control">
                                                        <option value="">- เลือกสังกัด -</option>
                                                        <?php $__currentLoopData = Arr::get($selects, 'section'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $p => $section): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <option
                                                                <?php echo e(old('section_no') == $p ? 'selected' : ''); ?>

                                                                value="<?php echo e($p); ?>"><?php echo e($section); ?>

                                                            </option>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </select>
                                                    <?php echo $errors->first('section_no', '<span class="help-block">:message</span>'); ?>

                                                </div>
                                                
                                            </div> 
                                             
                                            <div class="row">
                                                <div class="col-12">
                                                    <div
                                                        class="form-group has-feedback <?php echo e($errors->has('email') ? ' has-error has-feedback' : ''); ?>">
                                                        <input type="email" name="email" class="form-control"
                                                            placeholder="<?php echo e(_trans('user::auth.email')); ?>"
                                                            value="<?php echo e(old('email')); ?>">
                                                        <span
                                                            class="glyphicon glyphicon-envelope form-control-feedback"></span>
                                                        <?php echo $errors->first('email', '<span class="help-block">:message</span>'); ?>

                                                    </div>
                                                    <div
                                                        class="form-group has-feedback <?php echo e($errors->has('password') ? ' has-error has-feedback' : ''); ?>">
                                                        <input type="password" name="password" class="form-control"
                                                            placeholder="<?php echo e(_trans('user::auth.password')); ?>">
                                                        <span class="eye-toggle">
                                                            <i class="fa fa-eye-slash"></i>
                                                        </span>
                                                        <span class="glyphicon glyphicon-lock form-control-feedback"></span>
                                                        <?php echo $errors->first('password', '<span class="help-block">:message</span>'); ?>

                                                    </div>
                                                    <div
                                                        class="form-group has-feedback <?php echo e($errors->has('password_confirmation') ? ' has-error has-feedback' : ''); ?>">
                                                        <input type="password" name="password_confirmation"
                                                            class="form-control"
                                                            placeholder="<?php echo e(_trans('user::auth.password confirmation')); ?>">
                                                        <span class="eye-toggle">
                                                            <i class="fa fa-eye-slash"></i>
                                                        </span>
                                                        <span
                                                            class="glyphicon glyphicon-log-in form-control-feedback"></span>
                                                        <?php echo $errors->first('password_confirmation', '<span class="help-block">:message</span>'); ?>

                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-12">
                                                    <button type="submit"
                                                        class="btn btn-primary btn-block btn-flat"><?php echo e(_trans('user::auth.register me')); ?></button>
                                                </div>
                                            </div>
                                            <?php echo Form::close(); ?>


                                            <a href="javascript:void(0)"
                                                class="text-center btn-login"><?php echo e(_trans('user::auth.I already have a membership')); ?></a>

                                            
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row justify-content-center">
                <div class="col-md-7 text-right">
                    <a href="<?php echo e(_url('/')); ?>" class="btn btn-primary back-btn"><i
                            data-feather="arrow-left"></i>back</a>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('css-stack'); ?>
    <link rel="stylesheet" type="text/css" href="<?php echo e(_asset('themes/multikart/css/date-picker.css')); ?>">
    <style>
        .btn.bg-pink {
            background-color: #ff8084 !important;
            color: #fff;
            /* padding: 0!important; */
        }

        .authentication-box .container .form-group {
            position: relative;
        }

        .eye-toggle {
            display: inline-block;
            padding: 10px;
            position: absolute;
            top: 4px;
            right: 5px;
            font-size: 20px;
            color: #787878;
        }

        #top-register .eye-toggle {
            top: 0;
        }

        .swal2-popup {
            width: 36em !important;
        }

        .authentication-box .form-footer .social li a {
            color: #ff8084 !important;
            font-size: 18px;
        }

    </style>
<?php $__env->stopPush(); ?>
<?php $__env->startPush('js-stack'); ?>
    
    <script src="<?php echo e(_asset('themes/multikart/js/datepicker/datepicker.js')); ?>"></script>
    <script src="<?php echo e(_asset('themes/multikart/js/datepicker/datepicker.en.js')); ?>"></script>
    <script type="text/javascript">
        $(document).ready(function() {
            $(document).on("click", ".btn-agreement", function() {
                var label = $(this).data("label");
                var href = $(this).data("href");
                Swal.fire({
                    icon: 'info',
                    title: 'ข้อตกลงเกี่ยวกับการใช้ข้อมูลจาก ' + label + '',
                    html: '<div class="text-left">■ <strong>ข้อมูลที่จะเก็บและนำไปใช</strong> <br>' +
                        'Email <br>' +
                        '■ <strong>วัตถุประสงค์ในการเก็บข้อมูล การใช้ และการส่งต่อให้กับบุคคลภายนอก</strong> <br>' +
                        'ข้อมูลข้างต้นจะถูกนำไปใช้สำหรับ <br>' +
                        '(1) การให้บริการตลอดจนการพัฒนาและปรับปรุงบริการต่างๆ <br>' +
                        '(2) แจ้งเตือนเมื่อเปืดจัดอบรมใหม่ <br>' +
                        '(3) แจ้งห้องอบรมที่ผู้ใช้ได้ลงทะเบียนไว้ <br>' +
                        '(4) ส่งไปรับรอง (กรณี"การอบรมที่เข้าร่วม"มีการออกใบรับรองให้)</div>',
                    // showCloseButton: true,
                    showCancelButton: true,
                    focusConfirm: false,
                    confirmButtonText: '<i class="fa fa-thumbs-up"></i> Agree!',
                    confirmButtonAriaLabel: 'Thumbs up, great!',
                    confirmButtonColor: '#ff8084',
                    cancelButtonText: 'Cancel',
                    // '<i class="fa fa-thumbs-down"></i>',
                    cancelButtonAriaLabel: 'Thumbs down'
                }).then((result) => {
                    /* Read more about isConfirmed, isDenied below */
                    if (result.isConfirmed) {
                        window.location.href = href;
                    }
                });
            });

            $(document).on("click", ".eye-toggle", function() {
                var input = $(this).parent().find("input");
                var eye = $(this).find('i');
                eye.toggleClass('fa-eye-slash').toggleClass('fa-eye');
                if (eye.hasClass("fa-eye-slash"))
                    input.attr("type", "password");
                else
                    input.attr("type", "text");
            });

            $(document).on("click", ".btn-login", function() {
                $('#top-login-tab').tab('show');
            });
            $(document).on("click", ".btn-row-school-select", function() {
                var o_school_id = $(this).data("id");
                var o_school_no = $(this).data("school_no");
                var o_school_name = $(this).data("school_name");
                var o_school_province = $(this).data("school_province");
                var o_school_district = $(this).data("school_district");
                var o_school_subdistrict = $(this).data("school_subdistrict");
                var o_school_postcode = $(this).data("school_postcode");

                // $("#o_user_id").val(o_user_id);
                $("#o_school_id").val(o_school_id);
                $("#o_school_no").val(o_school_no);
                $("#o_school_name").val(o_school_name);
                $("#o_school_province").val(o_school_province);
                $("#o_school_district").val(o_school_district);
                $("#o_school_subdistrict").val(o_school_subdistrict);
                $("#o_school_postcode").val(o_school_postcode);

                $("#modal-school-selection").modal("hide");
            });

        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.account', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Projects\Web\htdocs\ssp4-v11\Modules/User/Resources/views/public/logins/login2.blade.php ENDPATH**/ ?>