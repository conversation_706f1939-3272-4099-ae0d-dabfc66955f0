

<div class="row">
    <div class="form-group col-sm-12 <?php echo e($errors->has('res_r1.customer_type') ? ' has-error has-feedback' : ''); ?>">
        <label for="">ลูกค้าที่เข้าพบ</label>
        <select name="res_r1[customer_type]" id="customer_type" class="form-control">
            
            <?php $__currentLoopData = Arr::get($selects, 'customer_type'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $p => $customer_type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <option <?php echo e(old('res_r1.customer_type') === $p ? 'selected' : ''); ?> value="<?php echo e($p); ?>">
                    <?php echo e($customer_type); ?>

                </option>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </select>
        <?php echo $errors->first('res_r1.customer_type', '<span class="help-block">:message</span>'); ?>

    </div>
    <div id="customer_school" class="col-12">
        <?php echo Form::hidden('res_r1[school_id]', old('res_r1.school_id'), ['id' => 'o_school_id']); ?>

        <?php echo Form::hidden('res_r1[school_no]', old('res_r1.school_no'), ['id' => 'o_school_no']); ?>

        <?php echo Form::hidden('res_r1[school_size]', old('res_r1.school_size'), ['id' => 'o_school_size']); ?>

        <?php echo Form::hidden('res_r1[school_type]', old('res_r1.school_type'), ['id' => 'o_school_type']); ?>

        
        <div
            class="form-group has-feedback <?php echo e($errors->has('res_r1[school_name]') ? ' has-error has-feedback' : ''); ?>">
            
            <div class="input-group">
                <div class="input-group-prepend">
                    <button data-toggle="modal" data-target="#modal-school-selection" type="button"
                        class="btn custom-bg-pink-light">
                        <i class="fa fa-search"></i>
                    </button>
                </div>
                <!-- /btn-group -->
                <?php echo Form::text('res_r1[customer_name]', old('res_r1.customer_name'), ['class' => 'form-control', 'readonly' => '', 'placeholder' => _trans('user::users.form.school_name'), 'id' => 'o_school_name']); ?>

            </div>
        </div>
    </div>
    <div id="customer_other"
        class='col-12 d-none form-group<?php echo e($errors->has('res_r1.customer_name') ? ' has-error' : ''); ?>'>
        <label for="">ชื่อหน่วยงาน ร้านค้า ที่เข้าพบ</label>
        <?php echo Form::input('text', 'res_r1[customer_name]', old('res_r1.customer_name'), ['class' => 'form-control', 'disabled' => 'disabled', 'id' => 'customer_name']); ?>

        <?php echo $errors->first('res_r1.customer_name', '<span class="help-block">:message</span>'); ?>

    </div>
</div>
<div class="row">
    <div class="col-sm-6 form-group">
        <label for="customer_postcode">รหัสไปรษณีย์</label>
        <input type="text" class="form-control" name="res_r1[customer_postcode]" id="customer_postcode"
            placeholder="รหัสไปรษณีย์">
    </div>
    <div class="col-sm-6 form-group">
        <label for="customer_subdistrict">ตำบล/แขวง</label>
        <select id="customer_subdistrict" name="res_r1[customer_subdistrict_id]" placeholder="ตำบล/แขวง" disabled
            class="form-control">
        </select>
    </div>
</div>
<div class="row">
    <div class="col-sm-6 form-group">
        <label for="customer_district">อำเภอ/เขต</label>
        <select id="customer_district" name="res_r1[customer_district_id]" placeholder="อำเภอ/เขต" style="width: 100%"
            disabled class="form-control selectize">
        </select>
    </div>
    <div class="col-sm-6 form-group">
        <label for="school">จังหวัด</label>
        <select id="customer_province" name="res_r1[customer_province_id]" placeholder="จังหวัด" style="width: 100%"
            class="form-control selectize">
            <option value="0">- เลือกจังหวัด -</option>
            <?php $__currentLoopData = Arr::get($selects, 'province'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $p => $province): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <option value="<?php echo e($p); ?>"><?php echo e($province); ?></option>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </select>
    </div>
    <input id="customer_region" type="hidden" name="res_r1[customer_region_id]">
    
</div>
<?php /**PATH C:\Projects\Web\htdocs\ssp4-v11\Modules/SalePrinting/Resources/views/member/sales/partials/create-fields/school-data.blade.php ENDPATH**/ ?>