<?php
return [
    'long_month' => [1 => 'มกราคม', 'กุมภาพันธ์', 'มีนาคม', 'เมษายน', 'พฤษภาคม', 'มิถุนายน', 'กรกฎาคม', 'สิงหาคม', 'กันยายน', 'ตุลาคม', 'พฤศจิกายน', 'ธันวาคม'],
    'short_month' => [1 => 'ม.ค.', 'ก.พ.', 'มี.ค.', 'เม.ย.', 'พ.ค.', 'มิ.ย.', 'ก.ค.', 'ส.ค.', 'ก.ย.', 'ต.ค.', 'พ.ย.', 'ธ.ค.'],
 
    "unit" =>  [
        0 => "เล่ม", 1 => "กล่อง", 2 => "บาท", 3 => "ชุด", 4 => "เครื่อง", 5 => "License", 6 => "Pcs."
    ],
    "warehouse" =>     [
        0 => "Main Warehouse",
        1 => "01 คลังแบบเรียนเสริม",
        2 => "02 คลังแบบเรียนหลัก",
        6 => "06 คลังแบบพิมพ์",
        8 => "08 คลังสินค้าพิเศษ",
        13 => "13 คลังสินค้าชำรุด",
        14 => "14 Shop E-commerce",
        15 => "15 คลังสินค้า ตชด.",
        17 => "17 คลังค้าปลีก",
        21 => "19 คลังสินค้าเฉพาะกิจ",
    ],
    "branch" => [
        0 => "สำนักงานใหญ่", 1 => "ศึกษาภัณฑ์พาณิชย์ลาดพร้าว", 2 => "ศึกษาภัณฑ์พาณิชย์ สกสค.", 4 => "ศึกษาภัณฑ์พาณิชย์อ้อมน้อย", 5 => "ศึกษาภัณฑ์พาณิชย์อิมพิเรียลเวิลด์สำโรง", 6 => "ศึกษาภัณฑ์พาณิชย์ราชบพิธ", 8 => "ศึกษาภัณฑ์พาณิชย์สนามกีฬา", 9 => "ศึกษาภัณฑ์พาณิชย์สตรีท", 10 => "ศึกษาภัณฑ์พาณิชย์ท้องฟ้าจำลอง"
    ],

    "group_keyword" => [
        // "อื่นๆ",
        "การงานอาชีพ" => ["อาชีพ"],
        "คณิตศาสตร์" => ["คณิต"],
        "ภาษต่างประเทศ" => ["ภาษต่างประเทศ", "ภาษาศาสตร์"],
        "ภาษาอังกฤษ" => ["อังกฤษ"],
        "ภาษาไทย" => ["ไทย"],
        "วิทยาศาสตร์" => ["วิทยาศาสตร์", "วิทย์"],
        "ศิลป" => ["ศิลป"],
        "สังคมศึกษา" => ["สังคม"],
        "สุขศึกษาและพลศึกษา" => ["สุขศึกษา", "พลศึกษา"],
    ],

    'customer_demand' => [ 
        6 => [
            'label' => "เงินสนับสนุน",
            'class' => 'success',
            'color' => '#44ab5c',
            'code' => 'financial',
            'unit' => 'บาท',
            'value' => 6,
        ],
        1 => [
            'label' => "อบรม CEFR",
            'class' => 'info',
            'color' => '#17a2b8',
            'code' => 'training',
            'unit' => 'License',
            'value' => 1,
        ],
        11 => [
            'label' => "อบรมคณิตศาสตร์",
            'class' => 'info',
            'color' => '#17a2b8',
            'code' => 'training-math',
            'unit' => 'License',
            'value' => 11,
        ],
        12 => [
            'label' => "อบรมวิทยาศาสตร์",
            'class' => 'info',
            'color' => '#17a2b8',
            'code' => 'training-sci',
            'unit' => 'License',
            'value' => 12,
        ],
        13 => [
            'label' => "อบรมภาษาไทย",
            'class' => 'info',
            'color' => '#17a2b8',
            'code' => 'training-thai',
            'unit' => 'License',
            'value' => 13,
        ],
        14 => [
            'label' => "อบรมภาษาอังกฤษ",
            'class' => 'info',
            'color' => '#17a2b8',
            'code' => 'training-eng',
            'unit' => 'License',
            'value' => 14,
        ],
        2 => [
            'label' => "D-Book",
            'class' => 'teal',
            'color' => '#20c997',
            'code' => 'dbook',
            'unit' => 'License',
            'value' => 2,
        ],
        3 => [
            'label' => "คู่มือ & แผนการสอน",
            'class' => 'warning',
            'color' => '#ffbc58',
            'code' => 'manual',
            'unit' => 'ชุด',
            'value' => 3,
        ],
        4 => [
            'label' => "Teacher Club",
            'class' => 'orange',
            'color' => '#fd7e14',
            'code' => 'teacherclub',
            'unit' => 'Member',
            'value' => 4,
        ],
        5 => [
            'label' => "Apple iPad",
            'class' => 'gray-dark',
            'color' => '#343a40',
            'code' => 'ipad',
            'unit' => 'เครื่อง',
            'value' => 5,
        ],
        7 => [
            'label' => "กระดาน Active Board",
            'class' => 'purple',
            'color' => '#6f42c1',
            'code' => 'activeboard',
            'unit' => 'ชุด',
            'value' => 7,
        ],
        0 => [
            'label' => "สื่ออื่นๆ",
            'class' => 'gray',
            'color' => '#6c757d',
            'code' => 'other',
            'value' => 0,
        ]
    ],
    'customer_type' => [
        1 => [
            'label' => "สถานศึกษา",
            'class' => 'info',
            'color' => '#17a2b8',
            'code' => 'school',
            'value' => 1,
        ],
        2 => [
            'label' => "ร้านค้าตัวแทน",
            'class' => 'teal',
            'color' => '#20c997',
            'code' => 'dealer',
            'value' => 2,
        ],
        3 => [
            'label' => "ร้านค้าทั่วไป",
            'class' => 'warning',
            'color' => '#ffbc58',
            'code' => 'shop',
            'value' => 3,
        ],
        4 => [
            'label' => "การอบรม",
            'class' => 'warning',
            'color' => '#6c757d',
            'code' => 'shop',
            'value' => 4,
        ],
        0 => [
            'label' => "หน่วยงานอื่นๆ",
            'class' => 'gray',
            'color' => '#6c757d',
            'code' => 'other',
            'value' => 0,
        ]
    ],
    'presentation' => [ 
        1 => [
            'label' => "โทรศัพท์",
            'class' => 'info',
            'color' => '#17a2b8',
            'code' => 'phone',
            'value' => 1,
        ],  
        2 => [
            'label' => "Line",
            'class' => 'teal',
            'color' => '#20c997',
            'code' => 'line',
            'value' => 2,
        ],   
        3 => [
            'label' => "Email",
            'class' => 'warning',
            'color' => '#ffbc58',
            'code' => 'email',
            'value' => 3,
        ],  
        4 => [
            'label' => "เข้าพบ",
            'class' => 'primary',
            'color' => '#ff8084',
            'code' => 'meet',
            'value' => 4,
        ], 
        0 => [
            'label' => "อื่นๆ",
            'class' => 'gray',
            'color' => '#6c757d',
            'code' => 'other',
            'value' => 0,
        ]
    ],
    'results' => [
        -1 => [
            'label' => 'ทั้งหมด',
            'class' => 'primary',
            'color' => '#ff8084',
            'code' => 'all',
            'value' => -1,
        ],
        3 => [
            'label' => 'ตกลงซื้อ',
            'class' => 'success',
            'color' => '#44ab5c',
            'code' => 'success',
            'value' => 3,
        ],
        2 => [
            'label' => 'รอตัดสินใจ',
            'class' => 'warning',
            'color' => '#ffbc58',
            'code' => 'wait',
            'value' => 2,
        ],
        4 => [
            'label' => 'นัดหมายใหม่',
            'class' => 'info',
            'color' => '#17a2b8',
            'code' => 'appointment',
            'value' => 4,
        ],
        5 => [
            'label' => 'ติดต่อการอบรม',
            'class' => 'primary',
            'color' => '#ff8084',
            'code' => 'training',
            'value' => 5,
        ],
        1 => [
            'label' => 'ไม่สนใจ',
            'class' => 'danger',
            'color' => '#dc3545',
            'code' => 'reject',
            'value' => 1,
        ],
        0 => [
            'label' => 'อื่นๆ',
            'class' => 'gray',
            'color' => '#6c757d',
            'code' => 'other',
            'value' => 0,
        ],
    ],
    'result_cond' => [
        "" => [
            'label' => 'ทั้งหมด',
            'class' => 'primary',
            'color' => '#ff8084',
            'code' => 'all',
            'value' => "",
        ], 
        1 => [
            'label' => 'มีเงื่อนไขเพิ่มเติม',
            'class' => 'danger',
            'color' => '#dc3545',
            'code' => 'yes',
            'value' => 1,
        ],
        0 => [
            'label' => 'ไม่มีเงื่อนไขเพิ่มเติม',
            'class' => 'gray',
            'color' => '#6c757d',
            'code' => 'no',
            'value' => 0,
        ],
    ],
    'department' => [
        31 => [
            "name" => "ฝ่ายค้าส่ง",
            "class" => "secondary",
            "value" => 31,
            "catalog" => [
                "qr" => "QR Catalog-ฝ่ายค้าส่ง.png",
                "link" => "https://suksapanpanit.com/crm/extra/catalogs/2021-pro/"
            ],
            "section" => [
                3101 => [
                    "name" => "ส่วนการตลาดภูมิภาคและค้าส่ง",
                    "codename" => "สตภ.",
                    "value" => 3101,
                ],
                3102 => [
                    "name" => "ส่วนอำนวยการและวิเคราะห์ค้าส่ง",
                    "codename" => "สอส.",
                    "value" => 3102,
                ]
            ],
            "teams" => [
                'head' =>  [
                    'teamname' => 'หน่วยขายภูมิภาค',
                    'members' =>  [
                        [
                            'name' => 'ฝ่ายโอ',
                            'img' => '1-1.png',
                            'link' => 'https://line.me/ti/p/J_WENMMDQT',
                        ]
                    ],
                ],
                'subhead' =>  [
                    'teamname' => '',
                    'members' =>  [
                        [
                            'name' => 'พี่นก',
                            'img' => '1-2.png',
                            'link' => 'https://line.me/ti/p/w9GWEmVPlK',
                        ],
                        [
                            'name' => 'พี่ลี',
                            'img' => '1-3.png',
                            'link' => 'https://line.me/ti/p/IPtIJMMOcQ',
                        ]
                    ],
                ],
                'other' => [
                    'n' =>  [
                        'teamname' => 'ภาคเหนือ',
                        'members' =>  [
                            [
                                'name' => 'สเกน',
                                'img' => '2-1.png',
                                'link' => 'https://line.me/ti/p/zNuqoTohsL',
                            ],
                            [
                                'name' => 'แนน',
                                'img' => '2-2.png',
                                'link' => 'https://line.me/ti/p/I5kKL-usVN',
                            ],
                        ],
                    ],
                    'en' =>  [
                        'teamname' => 'ภาคอีสาน',
                        'btnclass' => 'contact1',
                        'members' =>  [
                            [
                                'name' => 'หนึ่ง',
                                'img' => '3-1.png',
                                'link' => 'https://line.me/ti/p/Us5vX11FaR',
                            ],
                            [
                                'name' => 'เอ็ม',
                                'img' => '3-2.png',
                                'link' => 'https://line.me/ti/p/cu4GzztPHl',
                            ],
                        ],
                    ],
                    'ce' =>  [
                        'teamname' => 'ภาคกลางและภาคตะวันออก',
                        'btnclass' => 'contact1',
                        'members' =>  [
                            [
                                'name' => 'เจ๊บัติ',
                                'img' => '4-1.png',
                                'link' => 'https://line.me/ti/p/WljTX5AEBx',
                            ],
                            [
                                'name' => 'ปุ๊ก',
                                'img' => '4-2.png',
                                'link' => 'https://line.me/ti/p/PHXsMv0x4u',
                            ],
                            [
                                'name' => 'กิ๊ฟ(ปวีณา)',
                                'img' => '4-3.png',
                                'link' => 'https://line.me/ti/p/U4g2qQi_rm',
                            ],
                        ],
                    ],
                    's' =>  [
                        'teamname' => 'ภาคใต้',
                        'btnclass' => 'contact2',
                        'members' =>  [
                            [
                                'name' => 'โอม',
                                'img' => '5-1.png',
                                'link' => 'https://line.me/ti/p/8a_McP8GVk',
                            ],
                            [
                                'name' => 'ไบร์ท',
                                'img' => '5-2.png',
                                'link' => 'https://line.me/ti/p/RlV1_SVIRU',
                            ],
                        ],
                    ],
                    'p' =>  [
                        'teamname' => 'แบบพิมพ์',
                        'desc' => '(ใบระเบียนแสดงผลการเรียน, ใบประกาศนียบัตร, แบบพิมพ์ทั่วไป, แบบพิมพ์ลูกเสือ ฯ)',
                        'btnclass' => 'contact2',
                        'members' =>  [
                            [
                                'name' => 'กิ๊ฟ(อุษา)',
                                'img' => '6-1.png',
                                'link' => 'https://line.me/ti/p/eFfKMH92fH',
                            ],
                            [
                                'name' => 'บุ๋ม',
                                'img' => '6-2.png',
                                'link' => 'https://line.me/ti/p/3mikkbWBPo',
                            ],
                            [
                                'name' => 'บิ๊ก',
                                'img' => '6-3.png',
                                'link' => 'https://line.me/ti/p/9KLX-XWrH_',
                            ],
                        ],
                    ],
                ]
            ]

        ], 32 =>  [
            "name" => "ฝ่ายค้าปลีก",
            "class" => "warning",
            "value" => 32,
            "catalog" => [
                "qr" => "QR Catalog-ฝ่ายค้าปลีก.png",
                "link" => "https://suksapanpanit.com/crm/extra/catalogs/2021/"
            ],
            "section" => [
                3201 => [
                    "name" => "Key Account.",
                    "codename" => "Key Account.",
                    "value" => 3201,
                ],
                // 3202 => [
                //     "name" => "Snipper",
                //     "codename" => "Snipper",
                //     "value" => 3202,
                // ],

                /** 
                 * 0 => "สำนักงานใหญ่", 
                 * 1 => "ศึกษาภัณฑ์พาณิชย์ลาดพร้าว", 
                 * 2 => "ศึกษาภัณฑ์พาณิชย์ สกสค.", 
                 * 4 => "ศึกษาภัณฑ์พาณิชย์อ้อมน้อย", 
                 * 5 => "ศึกษาภัณฑ์พาณิชย์อิมพิเรียลเวิลด์สำโรง", 
                 * 6 => "ศึกษาภัณฑ์พาณิชย์ราชบพิธ", 
                 * 8 => "ศึกษาภัณฑ์พาณิชย์สนามกีฬา", 
                 * 9 => "ศึกษาภัณฑ์พาณิชย์สตรีท", 
                 * 10 => "ศึกษาภัณฑ์พาณิชย์ท้องฟ้าจำลอง"
                 */
                3211 => [
                    "name" => "ศภ.ลาดพร้าว",
                    "codename" => "ศภ.ลาดพร้าว",
                    // "codename" => "b11",
                    "value" => 3211,
                ], 
                3212 => [
                    "name" => "ศภ.สกสค.",
                    "codename" => "ศภ.สกสค.",
                    // "codename" => "b12",
                    "value" => 3212,
                ],
                3214 => [
                    "name" => "ศภ.อ้อมน้อย",
                    "codename" => "ศภ.อ้อมน้อย",
                    // "codename" => "b14",
                    "value" => 3214,
                ],
                3215 => [
                    "name" => "ศภ.อิมพิเรียลเวิลด์",
                    "codename" => "ศภ.อิมพิเรียลเวิลด์",
                    // "codename" => "b15",
                    "value" => 3215,
                ],
                3216 => [
                    "name" => "ศภ.ราชบพิธ",
                    "codename" => "ศภ.ราชบพิธ",
                    // "codename" => "b16",
                    "value" => 3216,
                ],
                3219 => [
                    "name" => "ศภ.สตรีท",
                    "codename" => "ศภ.สตรีท",
                    // "codename" => "b19",
                    "value" => 3219,
                ],
                3220 => [
                    "name" => "ศภ.ท้องฟ้าจำลอง",
                    "codename" => "ศภ.ท้องฟ้าจำลอง",
                    // "codename" => "b20",
                    "value" => 3220,
                ],
            ],
            "teams" => [
                'sales' =>  [
                    'teamname' => 'Sales Team',
                    'members' =>  [
                        [
                            'name' => 'พี่กร',
                            'img' => '1พี่กร.png',
                            'link' => 'https://line.me/ti/p/b_bWKWi4qT#~',
                        ],
                        [
                            'name' => 'ชมพู่',
                            'img' => '2ชมพู่-2.png',
                            'link' => 'https://line.me/ti/p/pKBR3AKOSX',
                        ],
                        [
                            'name' => 'พี่อาย',
                            'img' => '3พี่อาย.png',
                            'link' => '#',
                        ],
                        [
                            'name' => 'พี่กุ้ก',
                            'img' => '4พี่กุ้ก.png',
                            'link' => 'https://line.me/ti/p/NaXgSEFwlw',
                        ],
                        [
                            'name' => 'พี่เล็ก',
                            'img' => '5พี่เล็ก.png',
                            'link' => 'https://line.me/ti/p/RKmFPC0QVj',
                        ],
                        [
                            'name' => 'ป่าน',
                            'img' => '6ป่าน.png',
                            'link' => 'https://line.me/ti/p/W3p1UKHRUh',
                            'btn_style' => 'bottom: 10%!important;',
                        ],
                        [
                            'name' => 'นุ่น',
                            'img' => '7นุ่น.png',
                            'link' => 'https://line.me/ti/p/yngneHxIl_',
                        ],
                        [
                            'name' => 'อ้อ',
                            'img' => '8อ้อ.png',
                            'link' => 'https://line.me/ti/p/wMEMrcEBUh',
                        ],
                        [
                            'name' => 'มาย',
                            'img' => '9มาย.png',
                            'link' => 'https://line.me/ti/p/EKdvi-uqFR',
                            'btn_style' => 'bottom: 10%!important;',
                        ],
                    ]
                ],
                'sniper' =>  [
                    'teamname' => 'Sniper Team',
                    'members' =>  [
                        [
                            'name' => 'พี่นุช',
                            'img' => '10พี่นุช.png',
                            'link' => 'https://line.me/ti/p/h_vBrll8a-',
                            'btn_style' => 'bottom: 12%!important;',
                        ],
                        [
                            'name' => 'ทราย',
                            'img' => '11ทราย.png',
                            'link' => 'https://line.me/ti/p/aqY4LE_UeC',
                            'btn_style' => 'bottom: 11%!important;',
                        ],
                        [
                            'name' => 'โบว์',
                            'img' => '12โบว์.png',
                            'link' => 'https://line.me/ti/p/O2MmTXtF-q',
                            'btn_style' => 'bottom: 10%!important;',
                        ],
                        [
                            'name' => 'เวิด์ล',
                            'img' => '13เวิด์ล.png',
                            'link' => 'https://line.me/ti/p/fzO1PtpJHl',
                            'btn_style' => 'bottom: 13%!important;',
                        ],
                        [
                            'name' => 'เอก',
                            'img' => '14เอก.png',
                            'link' => 'https://line.me/ti/p/IUZEr2IplK',
                            'btn_style' => 'bottom: 13%!important;',
                        ],
                    ]
                ]
            ]
        ]
    ],

    'area_type' => [ 
        1 => [
            'title' => 'สำนักงานเขตพื้นที่การศึกษาประถมศึกษา',
            'label' => 'เขตพื้นที่การศึกษาประถมศึกษา',
            'alias' => 'สพป.',
            'value' => 'สพป',
            'code' => 'spp',
        ],
        2 => [
            'title' => 'สำนักงานเขตพื้นที่การศึกษามัธยมศึกษา',
            'label' => 'เขตพื้นที่การศึกษามัธยมศึกษา',
            'alias' => 'สพม.',
            'value' => 'สพม',
            'code' => 'spm',
        ],
        // 3 => [
        //     'title' => 'สำนักบริหารงานการศึกษาพิเศษ',
        //     'label' => 'สำนักบริหารงานการศึกษาพิเศษ',
        //     'alias' => 'สศศ.',
        //     'value' => 'สศศ',
        //     'code' => 'sss',
        // ],
        4 => [
            'title' => 'ไม่ระบุเขตพื้นที่การศึกษา',
            'label' => 'ไม่ระบุเขตพื้นที่การศึกษา',
            'alias' => 'ไม่ระบุ',
            'value' => 'ไม่ระบุ',
            'code' => 'none',
        ]
    ],

    // add more types, need to add create's form and printing as well
    'certificate_types' => [
        '11' => [
            'code' => '11',
            'label' => 'ปพ.1 : ป',
            'active' => true,
            'color' => 'navy',
            'prefix0' => 'ประถมศึกษา',
            'prefix1' => 'ประถมศึกษา',
            'prefix2' => 'ป.',
            'unit1' => 'เล่ม',
            'unit2' => 'ล.',
            'title' => 'ระเบียนแสดงผลการเรียนหลักสูตรแกนกลางการศึกษาขั้นพื้นฐาน ระดับประถมศึกษา',
            'style' => 'size: A4 portrait; margin: 10%;',
            'paper_width' => '1240',
            'paper_height' => '1754',
        ],
        '12' => [
            'code' => '12',
            'label' => 'ปพ.1 : บ',
            'active' => true,
            'asset' => '11', // use printing and creating template as type 11
            'color' => 'teal',
            'prefix0' => 'มัธยมศึกษาตอนต้น',
            'prefix1' => 'มัธยมศึกษา',
            'prefix2' => 'ม.', // ตอนต้น (การศึกษาภาคบังคับ) 
            'unit1' => 'เล่ม',
            'unit2' => 'ล.',
            'title' => 'ระเบียนแสดงผลการเรียนหลักสูตรแกนกลางการศึกษาขั้นพื้นฐาน ระดับมัธยมศึกษาตอนต้น',
            'style' => 'size: A4 portrait; margin: 10%;',
            'paper_width' => '1240',
            'paper_height' => '1754',
        ],
        '13' => [
            'code' => '13',
            'label' => 'ปพ.1 : พ',
            'active' => true,
            'asset' => '11', // use printing and creating template as type 11
            'color' => 'blue',
            'prefix0' => 'มัธยมศึกษาตอนปลาย',
            'prefix1' => 'มัธยมศึกษา',
            'prefix2' => 'ม.', // ตอนปลาย (การศึกษาขั้นพื้นฐาน)
            'unit1' => 'เล่ม',
            'unit2' => 'ล.',
            'title' => 'ระเบียนแสดงผลการเรียนหลักสูตรแกนกลางการศึกษาขั้นพื้นฐาน ระดับมัธยมศึกษาตอนปลาย',
            'style' => 'size: A4 portrait; margin: 10%;',
            'paper_width' => '1240',
            'paper_height' => '1754',
        ],
        '21' => [
            'code' => '21',
            'label' => 'ปพ.2 : บ',
            'active' => true,
            'color' => 'orange',
            'prefix0' => 'มัธยมศึกษาตอนต้น',
            'prefix1' => 'มัธยมศึกษา',
            'prefix2' => 'ม.', // ตอนต้น (การศึกษาภาคบังคับ) 
            'unit1' => 'แผ่น',
            'unit2' => 'ผ.',
            'title' => 'ใบประกาศนียบัตร ระดับมัธยมศึกษาตอนต้น',
            'style' => 'size: A5 landscape; margin: 10%;',
            'paper_width' => '1240',
            'paper_height' => '874',

        ],
        '22' => [
            'code' => '22',
            'label' => 'ปพ.2 : พ',
            'active' => true,
            'color' => 'maroon',
            'prefix0' => 'มัธยมศึกษาตอนปลาย',
            'prefix1' => 'มัธยมศึกษา',
            'prefix2' => 'ม.', // ตอนปลาย (การศึกษาขั้นพื้นฐาน)
            'unit1' => 'แผ่น',
            'unit2' => 'ผ.',
            'title' => 'ใบประกาศนียบัตร ระดับมัธยมศึกษาตอนปลาย',
            'style' => 'size: A5 landscape; margin: 10%;',
            'paper_width' => '1240',
            'paper_height' => '874',

        ],
        '31' => [
            'code' => '31',
            'label' => 'ปพ.3 : ป',
            'active' => false,
            'color' => '',
            'prefix0' => 'ประถมศึกษา',
            'prefix1' => 'ประถมศึกษา',
            'prefix2' => 'ป.',
            'unit1' => 'แผ่น',
            'unit2' => 'ผ.',
            'title' => 'แบบรายงานผู้สำเร็จการศึกษา ระดับประถมศึกษา',
            'style' => 'size: A5 landscape; margin: 10%;',
            'paper_width' => '1240',
            'paper_height' => '874',
        ],
        '32' => [
            'code' => '32',
            'label' => 'ปพ.3 : บ',
            'active' => false,
            'color' => '',
            'prefix0' => 'มัธยมศึกษาตอนต้น',
            'prefix1' => 'มัธยมศึกษา',
            'prefix2' => 'ม.', // ตอนต้น (การศึกษาภาคบังคับ) 
            'unit1' => 'แผ่น',
            'unit2' => 'ผ.',
            'title' => 'แบบรายงานผู้สำเร็จการศึกษา ระดับมัธยมศึกษาตอนต้น',
            'style' => 'size: A5 landscape; margin: 10%;',
            'paper_width' => '1240',
            'paper_height' => '874',
        ],
        '33' => [
            'code' => '33',
            'label' => 'ปพ.3 : พ',
            'active' => false,
            'color' => '',
            'prefix0' => 'มัธยมศึกษาตอนปลาย',
            'prefix1' => 'มัธยมศึกษา',
            'prefix2' => 'ม.', // ตอนปลาย (การศึกษาขั้นพื้นฐาน)
            'unit1' => 'แผ่น',
            'unit2' => 'ผ.',
            'title' => 'แบบรายงานผู้สำเร็จการศึกษา ระดับมัธยมศึกษาตอนปลาย',
            'style' => 'size: A5 landscape; margin: 10%;',
            'paper_width' => '1240',
            'paper_height' => '874',
        ],
    ],
];
