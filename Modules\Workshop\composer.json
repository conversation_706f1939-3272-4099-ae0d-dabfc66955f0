{"name": "idavoll/workshop-module", "type": "asgard-module", "description": "Workshop module for AsgardCMS. Handles the activation of modules.", "keywords": ["asgardcms", "workshop", "modules"], "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "support": {"email": "<EMAIL>", "issues": "https://github.com/AsgardCms/Platform/issues", "source": "https://github.com/AsgardCms/Workshop"}, "autoload-dev": {"psr-4": {"Modules\\Workshop\\": ".", "Modules\\": "Mo<PERSON>les/"}}, "extra": {"branch-alias": {"dev-master": "4.0.x-dev"}}, "minimum-stability": "dev", "prefer-stable": true}