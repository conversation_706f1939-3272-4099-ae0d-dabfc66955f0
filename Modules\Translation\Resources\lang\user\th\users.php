<?php

return [
    'button' => [
        'new-user' => 'New User',
    ],
    'title' => [
        'users' => 'Users',
        'select-user' => 'Select user',
        'new-user' => 'New user',
        'edit-user' => 'Edit user',
        'edit-profile' => 'Edit profile',
    ],
    'breadcrumb' => [
        'home' => 'Home',
        'users' => 'Users',
        'new' => 'New',
        'edit-user' => 'Edit user',
        'edit-profile' => 'Edit profile',
    ],
    'tabs' => [
        'data' => 'Data',
        'roles' => 'Roles',
        'permissions' => 'Permissions',
        'new password' => 'New password',
        'or send reset password mail' => 'or, send reset password email',
    ],
    'form' => [
        'no' => 'รหัสพนักงาน',
        'name' => 'ชื่อ - นามสกุล',
        'first-name' => 'ชื่อ',
        'last-name' => 'นามสกุล',
        'pre_name' => 'คำนำหน้าชื่อ',
        'nickname' => 'ชื่อเล่น',
        'first_name' => 'ชื่อ [TH]',
        'last_name' => 'นามสกุล [TH]',
        'email' => 'อีเมล์',
        // 'phone' => 'เบอร์โทร',
        'position' => 'ตำแหน่งงาน',
        'school_no' => 'รหัสโรงเรียน',
        'school_name' => 'ชื่อโรงเรียน',
        'status' => 'Status',
        'password' => 'รหัสผ่าน',
        'password-confirmation' => 'ยืนยันรหัสผ่าน',
        'new password' => 'New password',
        'new password confirmation' => 'New password confirmation',
        'is activated' => 'Activated',
        'roles' => 'Roles',

        'phone' => 'เบอร์โทรที่ติดต่อได้',
        'birthday' => 'วันเกิด',
        'age_range' => 'ช่วงอายุ',
        'department' => 'ฝ่าย',
        'branchs' => 'ร้านสาขา',
    ],
    'table' => [
        'created-at' => 'Created At',
        'first-name' => 'First name',
        'last-name' => 'Last name',
        'email' => 'Email',
        'actions' => 'Actions',
    ],
    'navigation' => [
        'back to index' => 'Back to users index',
    ],
    'new password setup' => 'Setup a new password',
    'or send reset password mail' => 'or, send reset password email',
    'send reset password email' => 'Send reset password email',
    'my account' => 'My Account',
    'profile' => 'Profile',
    'api-keys' => 'Api Keys',
    'generate new api key' => 'Generate new API key',
    'delete api key confirm' => 'Deleting an api key will prevent any application to use this key.',
    'your api keys' => 'Your API keys',
    'you have no api keys' => 'You have no API keys.',
    'generate one' => 'Generate one.',
    'token generated' => 'API token was successfully generated',
    'token deleted' => 'API token was successfully deleted',
    'last token can not be deleted' => 'Last token can not be deleted',
    'list user' => 'List users',
    'create user' => 'Create users',
    'edit user' => 'Edit users',
    'destroy user' => 'Delete users',
    'edit profile' => 'Edit Profile',
    'list api key' => 'List api keys',
    'create api key' => 'Create api keys',
    'destroy api key' => 'Delete api keys',
    'invalid login or password' => 'Invalid login or password.',
    'account not validated' => 'Account not yet validated. Please check your email.',
    'account is blocked' => 'Your account is blocked for :delay second(s).',
];
