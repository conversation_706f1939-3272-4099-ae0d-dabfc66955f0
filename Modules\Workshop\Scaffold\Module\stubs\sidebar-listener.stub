<?php

namespace Modules\$MODULE_NAME$\Listeners;

use Maatwebsite\Sidebar\Group;
use Maatwebsite\Sidebar\Item;
use Maatwebsite\Sidebar\Menu;
use Modules\Core\Events\BuildingSidebar;
use Modules\User\Contracts\Authentication;

class $CLASS_NAME$ implements \Maatwebsite\Sidebar\SidebarExtender
{
    /**
     * @var Authentication
     */
    protected $auth;

    /**
     * @param Authentication $auth
     *
     * @internal param Guard $guard
     */
    public function __construct(Authentication $auth)
    {
        $this->auth = $auth;
    }

    public function handle(BuildingSidebar $sidebar)
    {
        $sidebar->add($this->extendWith($sidebar->getMenu()));
    }

    /**
     * @param Menu $menu
     * @return Menu
     */
    public function extendWith(Menu $menu)
    {
        $menu->group(trans('core::sidebar.content'), function (Group $group) {
            $group->item(trans('$LOWERCASE_MODULE_NAME$::$PLURAL_LOWERCASE_MODULE_NAME$.title.$PLURAL_LOWERCASE_MODULE_NAME$'), function (Item $item) {
                $item->icon('fa fa-fw fa-copy');
                $item->weight(10);
                $item->authorize(
                     /* append */
                );
// append
            });
        });

        return $menu;
    }
}
