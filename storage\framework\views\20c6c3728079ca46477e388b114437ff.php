<?php $__env->startSection('content-header'); ?>
    <h1>
        <?php echo e(_trans('user::users.title.edit-profile')); ?>

    </h1>
    <ol class="breadcrumb">
        <li><a href="<?php echo e(route('dashboard.index')); ?>"><i class="fa fa-tachometer-alt"></i>
                <?php echo e(_trans('core::core.breadcrumb.home')); ?></a></li>
        
        <li class="active"><?php echo e(_trans('user::users.breadcrumb.edit-profile')); ?></li>
    </ol>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('control-panel-left'); ?>
    <h3 id="title_table" class="font-<?php echo e($title_table['class']); ?>"><?php echo e($title_table['title']); ?></h3>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('core::datatable', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php $__env->startSection('content'); ?>
    <div class="row">
        
        
        
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4>Filter</h4>
                    <div class="input-group mt-3">
                        <div class="input-group-prepend">
                            <a href="<?php echo e(route('supervisor.salemarketing.sales.index')); ?>"
                                class="input-group-text custom-bg-outline-gray" for=""><i
                                    class="fa fa-filter"></i></a>
                        </div>
                        <?php echo $__env->make('salemarketing::member.report.partials.filter-input-result', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                        <?php echo $__env->make('salemarketing::member.report.partials.filter-input-fy', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                        <?php echo $__env->make('salemarketing::member.report.partials.filter-input-date', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                        <?php echo $__env->make('salemarketing::member.report.partials.filter-input-customer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    </div>
                    <div class="card-header-right">
                        <?php echo $__env->make('partials.card-option', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    </div>

                    <input type="hidden" name="filter_customer" class="custom-filter"
                        value="<?php echo e(request()->has('customer') ? request()->customer : ''); ?>">
                    <input type="hidden" name="filter_fy" class="custom-filter"
                        value="<?php echo e(request()->has('fy') ? request()->fy : ''); ?>">
                    <input type="hidden" name="filter_dept_no" class="custom-filter"
                        value="<?php echo e(request()->has('dept_no') ? request()->dept_no : ''); ?>">
                    <input type="hidden" name="filter_section_no" class="custom-filter"
                        value="<?php echo e(request()->has('section_no') ? request()->section_no : ''); ?>">
                    <input type="hidden" name="filter_user_id" class="custom-filter"
                        value="<?php echo e(request()->has('user_id') ? request()->user_id : ''); ?>">
                </div>
            </div>
        </div>

        
        
        <?php if(_in_any_role(['section-head', 'department-head'])): ?>
            <?php if ($__env->exists('salemarketing::member.report.partials.sales-stat-dept', [
                'dept_no' => auth()->user()->dept_no,
            ])) echo $__env->make('salemarketing::member.report.partials.sales-stat-dept', [
                'dept_no' => auth()->user()->dept_no,
            ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        <?php elseif(_in_any_role(['admin', 'executive', 'director', 'sales-supervisor'])): ?>
            <?php if(request()->has('dept_no')): ?>
                <div class="col-12">

                    <a class="btn btn-danger mb-3 btn-flat d-print-none"
                        href="<?php echo e(route('supervisor.salemarketing.sales.index', ['fy' => $current_fy])); ?>"><i class="fa fa-arrow-left"></i>
                        <?php echo e(_trans('core::core.button.back')); ?></a>

                </div>
                <?php if ($__env->exists('salemarketing::member.report.partials.sales-stat-dept', [
                    'dept_no' => request()->dept_no,
                ])) echo $__env->make('salemarketing::member.report.partials.sales-stat-dept', [
                    'dept_no' => request()->dept_no,
                ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            <?php else: ?>
                <?php if ($__env->exists('salemarketing::member.report.partials.sales-stat-all')) echo $__env->make('salemarketing::member.report.partials.sales-stat-all', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            <?php endif; ?>
            

        <?php endif; ?>
        
        <div class="col-xl-12">
            <div class="card tab2-card">
                
                <div class="card-body">
                    
                    
                    
                    

                    <div class="tab-content" id="top-tabContent">
                        <div class="tab-pane fade show active" id="top-profile" role="tabpanel"
                            aria-labelledby="top-profile-tab">
                            
                            <?php echo $__env->yieldContent('content-datatable'); ?>

                            
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

<?php $__env->stopSection(); ?>


<?php $__env->startPush('sidebar-stack'); ?>

    <?php $__currentLoopData = Arr::get($salemarketing_data, 'department', []); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $dept_no => $dept): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <!-- Right sidebar Start-->
        <div class="right-sidebar right_side_bar_<?php echo e($dept_no); ?>" id="right_side_bar_<?php echo e($dept_no); ?>">
            <div>
                <div class="container p-0">
                    <div class="modal-header p-l-20 p-r-20">
                        <div class="col-sm-8 p-0">
                            <h6 class="modal-title font-weight-bold">เจ้าหน้าที่ขาย<br><span
                                    style="color: #ff8084;"><?php echo e($dept['name']); ?></span></h6>
                        </div>
                        <div class="col-sm-4 text-right p-0 right_side_close " data-dept="<?php echo e($dept_no); ?>">
                            <i class="mr-2 fa fa-times fa-2x"></i>
                            
                        </div>
                    </div>
                </div>
                <div class="friend-list-search m-0 ">
                    <div class="row">
                        <input id="search-dept" type="text" placeholder="พิมพ์เพื่อค้นหา.."><i class="fa fa-search"></i>
                        <div class="col-8 text-left p-0 pl-2">
                            <small>แสดง <span id="search-dept-result"></span> จาก <span><?php echo e($customer_fy->count()); ?></span> แถว </small>
                        </div>
                        
                    </div>      
                </div>
                
                <div class="p-l-10 p-r-10">
                    <div class="chat-box">
                        <div id="dept-list" class="people-list friend-list">
                            <ul class="list">
                                <li id="filter_dept_no_<?php echo e($dept_no); ?>"
                                    class="clearfix filter_dept_no filter_dept_no_<?php echo e($dept_no); ?>"
                                    data-dept_no="<?php echo e($dept_no); ?>" data-title="รายงานทั้งหมดของ<?php echo e($dept['name']); ?>"
                                    data-class="<?php echo e($dept['class']); ?>">
                                    <a class="about color-secondary" style="">
                                        <div class="name">แสดงทั้งหมดของ<?php echo e($dept['name']); ?></div>
                                    </a>
                                </li>
                                <?php $__currentLoopData = $dept_users[$dept_no]; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $usr): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li class="clearfix filter_user" data-user_id="<?php echo e($usr->id); ?>"
                                        data-dept_no="<?php echo e($usr->dept_no); ?>" data-section_no="<?php echo e($usr->section_no); ?>"
                                        data-title="รายงานของ <?php echo e($usr->name); ?>" data-class="<?php echo e($dept['class']); ?>">
                                        
                                        
                                        
                                        
                                        <a class="about">
                                            <div class="name filter_dept text-nowrap">(<?php echo e($usr->nickname); ?>) <?php echo e($usr->name); ?></div>
                                            
                                        </a>
                                    </li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Right sidebar Ends-->
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

<?php $__env->stopPush(); ?>

<?php $__env->startPush('css-stack'); ?>
    <style id="css-stack" type="text/css">
        .el-table td:last-child .btn.dropdown-toggle {
            padding: 5px 5px 5px 2px !important;
        }

        .el-table td:last-child .show .dropdown-menu>li {
            display: list-item;
        }

        .el-table td:last-child .show .dropdown-menu>li>a {
            display: block;
            padding: 10px 20px;
            clear: both;
            font-weight: 400;
            line-height: 1.42857143;
            color: #333;
            width: 100%;
            white-space: nowrap;
        }

        .el-table a.text-black {
            color: #000 !important;
        }

        .right-sidebar .chat-box .friend-list li.selected a .name,
        .right-sidebar .chat-box .friend-list li:hover a .name {
            color: #ff8084 !important;
        }

        .sales-history th {
            padding-bottom: 0.5rem 0.5rem 0 !important;
        }

        .sales-history td {
            padding: 0.5rem !important;
        }

        .sales-carousel {
            margin-bottom: 0.5rem !important;
        }

        .sales-carousel h6 {
            margin-bottom: 10px !important;
        }

        .sales-history>div,
        .sales-carousel>div {
            padding: 0.5rem 20px !important;
        }

        .sales-carousel,
        .right-sidebar .chat-box .friend-list li {
            cursor: pointer;
            display: block;
        }

        .dropdown a {
            cursor: pointer;
        }

        .right-sidebar .chat-box .friend-list {
            overflow-y: auto !important;
            overflow-x: hidden !important;
        }

        .right-sidebar {
            top: 65px !important;
        }
        .right-sidebar .chat-box .friend-list {
            max-height: calc(100vh - 250px);
            overflow: scroll;
        }
        .value-graph{
            display: none;
        }
        @media only screen and (max-width: 991px) {
            .right-sidebar {
                top: 65px !important;
            }

            /* .right-sidebar .chat-box .friend-list {
                            max-height: calc(100vh - 150px)!important; */
            /* overflow: scroll; */
            /* } */
        }
    </style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('js-stack'); ?>
    <script src="<?php echo e(asset('modules/core/vendor/jquery.quicksearch/2.4.0/jquery.quicksearch.min.js')); ?>"></script>
    <script src="<?php echo e(asset('themes/multikart/js/chart/sparkline/sparkline.js')); ?>"></script>
    <script type="text/javascript">
        var updateTitle = function(title, _class) {
            $("#title_table").attr("class", "font-" + _class).html(title);
        };

        var drawSparkline = function(elem) {
            let styles = {
                "primary": "#ff8084",
                "secondary": "#02cccd",
                "warning": "#ffbc58",
                "danger": "#a5a5a5"
            }
            let stats = $(elem).data('stats');
            if (!stats)
                return false;
            let color = styles[$(elem).data('class')];
            // $(elem).sparkline(Object.values(stats).reverse(), {
            $(elem).sparkline(Object.values(stats), {
                type: 'line',
                tooltipFormat: 'ปี \{\{x:years\}\} - \{\{y\}\}',
                tooltipValueLookups: {
                    years: Object.keys(stats)
                    // years: Object.keys(stats).reverse()
                },
                width: '100%',
                height: '100%',
                tooltipClassname: 'chart-sparkline',
                lineColor: color,
                fillColor: 'transparent',
                highlightLineColor: color,
                highlightSpotColor: false,
                targetColor: color,
                performanceColor: color,
                boxFillColor: color,
                medianColor: color,
                minSpotColor: false,
                maxSpotColor: false,
                spotColor: false
            });
        };

        $(document).ready(function() {
            var qs = $('input#search-dept').quicksearch('#dept-list  li', {
                selector: '.filter_dept',
                // minValLength: 3,
                onAfter: function() {
                    $("#search-dept-result").html(this.currentMatchedResults());
                }
            });
            $(".flot-chart-placeholder").each(function() {
                drawSparkline(this);
            })

            $(".right_side_close").on('click', function() {
                // let dept = $(this).data('dept') || 1;
                $('.right-sidebar').removeClass('show');
                // $('#right_side_bar_'+dept).toggleClass('show');
            });

            $(".right_side_toggle").on('click', function() {
                let sidebar = $(this).data('sidebar') || 1;
                $('.right-sidebar:not(#right_side_bar_' + sidebar + ')').removeClass('show');
                $('#right_side_bar_' + sidebar).toggleClass('show');
            });

            $('.filter_none').on('click', function() {
                updateTitle($(this).data('title'), $(this).data('class'));

                $('.right-sidebar').removeClass('show');
                resetPageDataTable();
                window.location.href = window.location.origin + window.location.pathname +
                    "?fy=<?php echo e($current_fy); ?>";
                // window.filter = [];
                // reloadDataTable();
            });

            $('.filter_dept_no').on('click', function() {
                $(".people-list li").removeClass("selected");
                $(this).addClass("selected");
                updateTitle($(this).data('title'), $(this).data('class'));

                let dept_no = $(this).data('dept_no');
                let section_no = "";
                let user_id = "";

                window.filter["filter_dept_no"] = dept_no;
                window.filter["filter_section_no"] = section_no;
                window.filter["filter_user_id"] = user_id;

                var query = buildQueryString(window.filter).replaceAll("filter_", "");
                resetPageDataTable();
                window.location.href = window.location.origin + window.location.pathname + "?" + query;

            });

            // $(".filter_dept_no_<?php echo e(request()->dept_no); ?>").trigger('click');

            $('.filter_section_no').on('click', function() {
                $(".people-list li").removeClass("selected");
                $(this).addClass("selected");
                updateTitle($(this).data('title'), $(this).data('class'));

                let dept_no = $(this).data('dept_no');
                let section_no = $(this).data('section_no');
                let user_id = "";

                window.filter["filter_dept_no"] = dept_no;
                window.filter["filter_section_no"] = section_no;
                window.filter["filter_user_id"] = user_id;

                var query = buildQueryString(window.filter).replaceAll("filter_", "");
                resetPageDataTable();
                window.location.href = window.location.origin + window.location.pathname + "?" + query;
                // reloadDataTable();
                // window.location.href = window.location.origin + window.location.pathname + "?dept_no=" + dept_no + "&section_no=" + section_no + "&user_id=" + user_id;
            });

            $('.filter_user').on('click', function() {
                $(".people-list li").removeClass("selected");
                $(this).addClass("selected");
                updateTitle($(this).data('title'), $(this).data('class'));

                let dept_no = $(this).data('dept_no');
                let section_no = $(this).data('section_no');
                let user_id = $(this).data('user_id');

                window.filter["filter_dept_no"] = dept_no;
                window.filter["filter_section_no"] = section_no;
                window.filter["filter_user_id"] = user_id;
                var query = buildQueryString(window.filter).replaceAll("filter_", "");
                resetPageDataTable();
                window.location.href = window.location.origin + window.location.pathname + "?" + query;

                // reloadDataTable();
                // window.location.href = window.location.origin + window.location.pathname + "?dept_no=" + dept_no + "&section_no=" + section_no + "&user_id=" + user_id;
            });

            $('.filter_customer').on('click', function() {
                $(".customer-list li").removeClass("selected");
                $(this).addClass("selected");
                updateTitle($(this).data('title'), $(this).data('class'));

                let customer = $(this).data('customer');

                window.filter["filter_customer"] = customer || null;

                var query = buildQueryString(window.filter).replaceAll("filter_", "");
                resetPageDataTable();
                window.location.href = window.location.origin + window.location.pathname + "?" + query;

                // reloadDataTable();
            });


        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Projects\Web\htdocs\ssp4-v11\Modules/SaleMarketing/Resources/views/supervisor/sales/index.blade.php ENDPATH**/ ?>