<?php

namespace App\Library\Line\API\v20;

//use App\Infra\Http\Client;
use GuzzleHttp\Client;
use App\Library\Line\API\v20\Response\AccessToken;
use App\Library\Line\API\v20\Response\Profile;
use App\Library\Line\API\v20\Response\Verify;

use Illuminate\Support\Facades\Log;

class LineAPIService
{
    /** @var string|null The Line Channel ID */
    private $channelID;

    /** @var string|null The Line Channel Secret */
    private $channelSecret;

    /** @var string|null The Line web login callback */
    private $callbackURL;

    /**
     * Constructor
     *
     * @param string $channelID The Line Channel ID
     * @param string $channelSecret The Line Channel Secret
     */
    public function __construct($channelID = null, $channelSecret = null)
    {
        $this->channelID = $channelID;
        $this->channelSecret = $channelSecret;
    }

    protected function getChannelID()
    {
        return $this->channelID ??  getenv('LINE_CHANNEL_ID');
    }

    protected function getChannelSecret()
    {
        return $this->channelSecret ?? getenv('LINE_CHANNEL_SECRET');
    }

    protected function getCallbackURL()
    {
        return $this->callbackURL ??  getenv('LINE_CALLBACK_URL');
    }

    protected function setCallbackURL($callbackURL)
    {
        $this->callbackURL = $callbackURL;
    }

    public function accessToken($code)
    {
        $lineAPI = new LineAPI;
        $json_result = json_decode($this->getClient($lineAPI->accessToken($code, $this->getChannelID(), $this->getChannelSecret(), $this->getCallbackURL())), true);
        return array(
            $json_result['scope'],
            $json_result['access_token'],
            $json_result['token_type'],
            $json_result['expires_in'],
            $json_result['refresh_token']);
    }

    public function refreshToken($refresh_token)
    {
        $lineAPI = new LineAPI;
        $json_result = json_decode($this->getClient($lineAPI->refreshToken($refresh_token, $this->getChannelID(), $this->getChannelSecret())), true);
        return array(
            $json_result['scope'],
            $json_result['access_token'],
            $json_result['token_type'],
            $json_result['expires_in'],
            $json_result['refresh_token']);
    }

    public function verify($accessToken)
    {
        $lineAPI = new LineAPI;
        $json_result = json_decode($this->getClient($lineAPI->verify($accessToken)), true);
        return array(
            $json_result['scope'],
            $json_result['client_id'],
            $json_result['expires_in']);
    }

    public function revoke($refresh_token)
    {
        $lineAPI = new LineAPI;
        $json_result = json_decode($this->getClient($lineAPI->revoke($refresh_token)), true);
        return "Ok";
    }

    public function profile($accessToken)
    {
        $lineAPI = new LineAPI;
        $json_result = json_decode($this->getClient($lineAPI->profile(self::addBearer($accessToken))), true);
        if (!array_key_exists('statusMessage', $json_result)) {
            $json_result['statusMessage'] = " ";
        }
        return array(
            $json_result['displayName'],
            $json_result['userId'],
            $json_result['pictureUrl'],
            $json_result['statusMessage']);
    }

    public function getLineWebLoginUrl($state)
    {
        $encodedCallbackUrl = urlencode($this->getCallbackURL());
        return "https://access.line.me/dialog/oauth/weblogin?response_type=code" . "&client_id=" . $this->getChannelID() . "&redirect_uri=" . $encodedCallbackUrl . "&state=" . $state;
    }

    private function getClient($data)
    {
        Log::info("LineAPIService.getClient: " . json_encode($data));
        $output = '';
        $client = new Client;
        if ($data['Method'] == 'post') {
            $output = $client->post($data['Url'], ['headers' => $data['Header'], 'body' => $data['Body']]);
            // $output = $client->httpPost($data['Url'], $data['Header'], $data['Body']);
        } elseif ($data['Method'] == 'get') {
            $output = $client->post($data['Url'], ['headers' => $data['Header']]);
            // $output = $client->httpGet($data['Url'], $data['Header']);
        }
        Log::info("LineAPIService.getClient: " . $output);
        return $output;
    }

    private function addBearer($accessToken)
    {
        return "Bearer " . $accessToken;
    }
}
