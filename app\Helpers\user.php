<?php

use Cartalyst\Sentinel\Laravel\Facades\Sentinel as SentinelFacade;
use Illuminate\Support\Arr;
use Modules\Media\ValueObjects\MediaPath;
use Mo<PERSON>les\Setting\Entities\Geo\GeoProvince;
use Modules\Setting\Entities\Geo\GeoRegion;

if (!function_exists('_get_require_user_career')) {
    function _get_require_user_career($field)
    {
        return config("asgard.user.config.require_{$field}");
    }
}
if (!function_exists('_check_require_user_career')) {
    function _check_require_user_career($field, $user = null)
    { 
        return in_array($user->profile['career'], _get_require_user_career($field));
    }
}

if (!function_exists('_global_user_id')) {
    function _global_user_id()
    {
        return 1;
    }
}

if (!function_exists('_is_admin')) {
    function _is_admin()
    {
        return _in_role('dev') || _in_role('admin');
    }
}
if (!function_exists('_is_dev')) {
    function _is_dev()
    {
        return _in_role('dev') || $_GET['dev'];
    }
}
if (!function_exists('_is_logged_in')) {
    function _is_logged_in()
    {
        return SentinelFacade::check();
    }
}
if (!function_exists('_is_verified_pass')) {
    function _is_verified_pass($user = null)
    {
        if(!$user)
            $user = auth()->user();
        return $user->verify_status == 1;
    }
}
if (!function_exists('_is_verified_not_pass')) {
    function _is_verified_not_pass($user = null)
    {
        if(!$user)
            $user = auth()->user();
        return in_array($user->verify_status, config('asgard.user.data.verify_no_pass')); 
    }
}
if (!function_exists('_has_access')) {
    function _has_access($permission)
    {
        if (!_is_logged_in())
            return false;
        return SentinelFacade::hasAccess($permission); // || _is_dev();
    }
}
if (!function_exists('_has_any_access')) {
    function _has_any_access($permission)
    {
        if (!_is_logged_in())
            return false;
        return SentinelFacade::hasAnyAccess($permission);
    }
}
if (!function_exists('_in_role')) {
    function _in_role($role)
    {
        if (!_is_logged_in())
            return false;
        return SentinelFacade::inRole($role);
    }
}
if (!function_exists('_in_roles')) {
    function _in_roles($roles)
    {
        if (!_is_logged_in())
            return false;
        return in_array(auth()->user()->rolename, Arr::wrap($roles));
    }
}
if (!function_exists('_in_any_role')) {
    function _in_any_role($roles = [], $field = "slug")
    {
        if (!_is_logged_in()) {
            return false;
        }
        foreach (_get_user()->roles as $r)
            if (in_array($r->$field, Arr::wrap($roles)))
                return true;
        return false;
    }
}
if (!function_exists('_get_roles')) {
    function _get_roles()
    {
        if (!_is_logged_in())
            return false;
        return SentinelFacade::getRoles();
    }
}
if (!function_exists('_get_user')) {
    function _get_user($check = true)
    {
        if (!_is_logged_in())
            return false;
        return SentinelFacade::getUser($check);
    }
}
if (!function_exists('_get_api_key')) {
    function _get_api_key()
    {
        if (!_is_logged_in())
            return false;
        return optional(auth()->user())->getFirstApiKey();
    }
}
if (!function_exists('_get_filter_for_user_profile')) {
    function _get_filter_for_user_profile()
    {
        $province = GeoProvince::orderBy('province_th', 'asc')->get();
        return  [
            'province' => $province->pluck("province_th", "province_th")->toArray(),
            'province_id' => $province->pluck("province_th", "id")->toArray()
        ];
    }
}
if (!function_exists('_get_select_for_user_profile')) {
    function _get_select_for_user_profile()
    {
        $data = config("asgard.training.data");
        return array_merge($data, [
            'region' => GeoRegion::all()->pluck("region_th", "id")->toArray(),
            'province' => GeoProvince::all()->pluck("province_th", "id")->toArray(),
        ]);
    }
}

if (!function_exists('_get_avatar_user')) {
    /**
     * Return the avatar link for the users
     * @param  string $avatar
     * @param  int $size
     * @return string
     */
    function _get_avatar_user($avatar = "1:5", $size = 64)
    {
        if (preg_match('#^(https?:)?//#', $avatar) && @file_exists($avatar))
            return $avatar;
        if (is_numeric($size)) {
            $a = @explode(":", $avatar);
            return _get_avatar_system($a[1], $size, $a[0]);
        }
        return _thumbnail_v2($avatar, $size).'?t='.time();
    }
}
if (!function_exists('_get_avatar_system')) {
    /**
     * Return the avatar link
     * @param  int $code
     * @param  int $size
     * @param  int|array $pack_properties
     * @return string
     */
    function _get_avatar_system($code = 1, $size = 64, $pack_properties = null)
    {
        if (!is_array($pack_properties)) {
            if (empty($pack_properties)) $pack_properties = 1;
            $pack_properties = config("istyles.media.avatars.pack_{$pack_properties}");
        }
        $pack = $pack_properties["value"];
        // fill size
        if (isset($pack_properties["available-size"][$size])) {
            $size = $pack_properties["available-size"][$size];
        } else if (empty($size) || !in_array($size, $pack_properties["available-size"]))
            $size = $pack_properties["default-size"];

        // fill code
        if (empty($code) || !in_array($code, $pack_properties["available-code"]))
            $code = $pack_properties["default-code"];

        // check file exist
        $media = "images/avatars/pack_{$pack}/{$size}/{$size}_{$code}.png";
        if (file_exists(public_path("assets/" . $media)))
            return asset($media);

        //  if not, return default
        $code = $pack_properties["default-code"];
        return asset("images/avatars/pack_1/{$size}/{$size}_{$code}.png");
    }
}

if (!function_exists('_split_address')) {
    function _split_address($source)
    {
        $addr_source = trim(preg_replace("/\s+/", " ", $source));
        preg_match("/(.*)(แขวง|ตำบล|ต\.)\s*([^\s]+)\s+(เขต|อำเภอ|อ\.)\s*([^\s]+)\s?(จังหวัด|จ\.|\s+)([^\s]*)\s*(\d{5})?/", $addr_source, $matched);
        return [
            "original" => $addr_source,
            "address" => trim($matched[1]),
            "addr_subdistrict" => $matched[3],
            "addr_district" => $matched[5],
            "addr_province" => $matched[7],
            "addr_postcode" => $matched[8],
        ];
    }
}
if (!function_exists('_format_address')) {
    function _format_address($a)
    {
        $txt = "";
        $txt .= ($a->address_1 ?? "");
        $txt .= " " . ($a->address_2 ?? "");
        $txt .= " " . ($a->subdistrict_name ?? "");
        $txt .= " " . ($a->district_name ?? "");
        $txt .= " " . ($a->province_name ?? "");
        $txt .= " " . ($a->postcode ?? "");
        if ($a->region_name)
            $txt .= ", " . $a->region_name;
        return $txt;
    }
}
if (!function_exists('_format_name')) {

    /*
     * Split fullname [ Mykhoylo Ilyashevych ] to firstname middlename lastname
     * @return [1] => Array
                            (
                                [firstname] => Mykhoylo
                                [middlename] =>
                                [lastname] => Ilyashevych
                            )
     * */
    function _format_name($namestrs, $index = false)
    {
        $allnames = array();
        $firstname = $middlename = $lastname = "";
        $namestrs = trim($namestrs);
        $namestrs = str_replace(array(',', ' and ', ' & ', '&amp;', '/'), '|', $namestrs);
        $namestrs = explode('|', $namestrs);

        foreach ($namestrs as $key => $namestr) {
            $namestr = explode(' ', trim($namestr));

            if (count($namestr) == 1 || (count($namestr) == 2 && strlen(trim($namestr[1])) < 3)) {
                $firstname = $namestr[0];
                if (isset($namestr[1])) {
                    $middlename = $namestr[1];
                } else {
                    $middlename = '';
                }
                $lastname = '';
                $thenames = $namestrs; //print_r($thenames); //echo $key;
                $thenames = array_slice($thenames, $key + 1, NULL, TRUE);  //print_r($thenames);

                foreach ($thenames as $c => $a) {
                    $a = explode(' ', trim($a)); // print_r( $a);

                    if (count($a) > 1 && trim($lastname) == '') {
                        $lastname = $a[count($a) - 1];
                    }
                }
            } else if (count($namestr) == 2) {
                $firstname = $namestr[0];
                $middlename = '';
                $lastname = $namestr[1];
            } else if (count($namestr) == 3) {
                $firstname = $namestr[0];
                $middlename = $namestr[1];
                $lastname = $namestr[2];
            } else if (count($namestr) > 3) {
                $firstname = $namestr[0];
                $middlename = $namestr[1];
                $lastname = str_replace(array($firstname, $middlename), "", implode(' ', $namestr));
                $lastname = trim($lastname);
            }

            if ($lastname == '3rd') {
                $lastname = trim($middlename) . " " . trim($lastname);
                $middlename = '';
            }

            $allnames[] = array('first_name' => $firstname, 'middle_name' => $middlename, 'last_name' => $lastname);
        }

        return $index !== false && isset($allnames[$index]) ? $allnames[$index] : $allnames;
    }
}

if (!function_exists('_make_new_expiration')) {
    function _make_new_expiration($old_expiration, $add_number, $add_unit = 'month')
    {
        if (is_a($old_expiration, 'Carbon\Carbon')) {
            $expire = $old_expiration->format("U");
        } else
            $expire = is_string($old_expiration) ? strtotime($old_expiration) : $old_expiration;
        if ($expire > strtotime("now"))
            return strtotime("{$old_expiration} + {$add_number} {$add_unit}");
        return strtotime("+ {$add_number} {$add_unit}");
    }
}
