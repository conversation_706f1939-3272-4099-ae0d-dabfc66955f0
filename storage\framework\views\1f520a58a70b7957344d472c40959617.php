<?php $__env->startSection('content-datatable'); ?>
    
    <div class="col-12">
        <div class="box box-<?php echo e($skin_bootstrap or 'primary'); ?>">
            <div class="box-header">
            </div>
            <!-- /.box-header -->
            <div class="box-body">
                <div id="controlPanel" class="pb-3 row">
                    <div class="col-sm-12">
                        <div id="leftControlPanel" class="pull-left ">
                            <?php $__env->startSection('control-panel-left'); ?>
                                <div class=" action-menu btn-group hidden">
                                    <button class="btn btn-danger btn-flat btn-sm dropdown-toggle" type="button"
                                        data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                        Action &nbsp;
                                    </button>
                                    <ul class="dropdown-menu">
                                        <?php if(Route::has("{$_route['api']}.mark-status")): ?>
                                            <li><a data-action="mark-online" data-col="status" data-val="1" class="btn-bulk-mark-status dropdown-item"
                                                    href="#">Online</a>
                                            </li>
                                            <li><a data-action="mark-offline" data-col="status" data-val="0" class="btn-bulk-mark-status dropdown-item"
                                                    href="#">Offline</a>
                                            </li>
                                        <?php endif; ?>
                                        <li role="separator" class="divider"></li>
                                        <li><a data-action="delete" class="btn-bulk-destroy dropdown-item text-danger"
                                                href="#">Delete</a></li>
                                    </ul>
                                </div>
                            <?php echo $__env->yieldSection(); ?>
                            <?php echo $__env->yieldPushContent('control-panel-left'); ?>
                        </div>
                        <div id="rightControlPanel" class="pull-right">
                            <?php echo $__env->yieldContent('control-panel-right'); ?>
                        </div>
                    </div>
                </div>
                <div class="sc-table">
                    <div
                        class="el-table el-table--fit el-table--striped el-table--enable-row-hover el-table--enable-row-transition">
                        <div class="table-responsive el-table__header-wrapper">
                            <table class="data-table zd-data-table table table-bordered table-hover el-table__header">
                                <thead>
                                    <tr>
                                        <?php if($_action_left): ?>
                                            <th width="20" class="print-invisible pl-2 pr-3">
                                                <label class="zd-icheck zd-icheck-checkbox pl-0">
                                                    <span class="text-hide">Select All</span>
                                                    <input type="checkbox" id="check-items" name="check-items"
                                                        class="check-items">
                                                    <span class="checkmark"></span>
                                                </label>
                                            </th>
                                        <?php endif; ?>
                                        <?php
                                            $row = Arr::where($columns, function ($v, $k) {
                                                return !in_array($v['data'], ['_action_left', '_action_right']);
                                            });
                                        ?>
                                        <?php $__currentLoopData = $row; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $col): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <th class="print-visible"><?php echo e($col['name']); ?></th>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <?php if($_action_right): ?>
                                            <th width="100" class="print-invisible" data-sortable="false">&nbsp;
                                                
                                            </th>
                                        <?php endif; ?>
                                    </tr>
                                </thead>
                            </table>
                            <!-- /.box-body -->
                        </div>
                    </div>
                </div>
            </div>
            <!-- /.box -->
        </div>
    </div>
    
    
<?php $__env->stopSection(); ?>











<?php $__env->startPush('css-stack'); ?>

    <link href="<?php echo e(Module::asset('core:vendor/datatables.net-bs/css/dataTables.bootstrap.min.css')); ?>" media="all"
        rel="stylesheet" type="text/css">
    <link
        href="<?php echo e(Module::asset('core:vendor/datatables.net/extensions/FixedHeader-3.1.3/css/fixedHeader.bootstrap.min.css')); ?>"
        media="all" rel="stylesheet" type="text/css">
    <style type="text/css">
        .modal-body .zd-data-table,
        .sc-table .zd-data-table {
            width: 100% !important;
        }

        .sc-table .zd-data-table .table-action-box {
            word-wrap: break-word;
            white-space: nowrap;
        }

        .sc-table .zd-data-table .dtr-details {
            list-style: none
        }

        .data-table .table-action-box>.btn-group>a,
        .data-table .table-action-box>.btn-group>button {
            display: inline-block !important;
            float: none !important;
        }

        .dataTables_paginate.paging_input .paginate_page,
        .dataTables_paginate.paging_input .paginate_of {
            padding: 0 10px;
        }

        .dataTables_paginate.paging_input .paginate_input {
            text-align: center;
            width: 50px;
            height: 34px;
            padding: 6px 12px;
            font-size: 14px;
            line-height: 1.42857143;
            color: #555;
            background-color: #fff;
            background-image: none;
            border: 1px solid #ccc;
            border-radius: 4px;
            -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
            box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
            -webkit-transition: border-color ease-in-out .15s, -webkit-box-shadow ease-in-out .15s;
            -o-transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
            transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
        }

        .dataTables_paginate.paging_input .paginate_button.disabled {
            cursor: not-allowed;
            filter: alpha(opacity=65);
            -webkit-box-shadow: none;
            box-shadow: none;
            opacity: .65;
        }

        .dataTables_paginate.paging_input .paginate_button {
            background-color: #f4f4f4;
            color: #444;
            border-color: #ddd;

            display: inline-block;
            padding: 6px 12px;
            margin-bottom: 0;
            font-size: 14px;
            font-weight: 400;
            line-height: 1.42857143;
            text-align: center;
            white-space: nowrap;
            vertical-align: middle;
            -ms-touch-action: manipulation;
            touch-action: manipulation;
            cursor: pointer;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            background-image: none;
            border: 1px solid transparent;
            border-radius: 4px;
        }

        .el-table,
        .el-table__footer-wrapper,
        .el-table__header-wrapper {
            overflow: visible !important;
        }

        #controlPanel {
            padding-bottom: 20px;
        }

        .act-click {
            cursor: pointer;
        }

        .float-none {
            float: none !important;
        }

    </style>
<?php $__env->stopPush(); ?>
<?php $__env->startPush('js-stack'); ?>
    <script type="text/javascript" src="<?php echo e(Module::asset('core:vendor/datatables.net/js/jquery.dataTables.min.js')); ?>">
    </script>
    <script type="text/javascript"
        src="<?php echo e(Module::asset('core:vendor/datatables.net/js/istyles.dataTables.extensions.js?v=1.0.1')); ?>"></script>
    <script type="text/javascript"
        src="<?php echo e(Module::asset('core:vendor/datatables.net/js/istyles.dataTables.config.js?v=1.0.1')); ?>"></script>

    <script id="script-datatable" type="text/javascript">
        var hasActionLeft = ('<?php echo e((int) !!$_action_left); ?>' == '1') ? 1 : 0;
        var hasActionRight = ('<?php echo e((int) !!$_action_right); ?>' == '1') ? 1 : 0;
        var searchDelay = 500;
        var cur_path = "<?php echo e($cur_path . '/'); ?>";
        window.oTable = [];
        window.filter = [];

        // $('.custom-filter').each(function() {
        //     let name = $(this).attr("name");
        //     let val = $(this).val();
        //     if (val != "")
        //         window.filter[name] = val;
        // });
        var table_option = {
            "columns": <?php echo json_encode($columns, 15, 512) ?>,
            "paginate": true,
            // numbers simple simple_numbers full full_numbers first_last_numbers 
            "pagingType": "input",
            // Extend your responsive design to HTML tables
            "responsive": true,
            "lengthChange": true,
            "lengthMenu": [
                [5, 10, 25, 50, 100, 250, 500, -1],
                [' 5 ', ' 10 ', ' 25 ', ' 50 ', ' 100 ', ' 250 ', ' 500 ', ' - All - ']
            ],
            // Server-side source code
            "ajax": {
                "url": "<?php echo e(_route($_route['serverSide'] ?? $_route['api'] . '.indexServerSide', $_route['param'])); ?>",
                "data": function(d) {
                    $('.custom-filter').each(function() {
                        let name = $(this).attr("name");
                        let val = $(this).val();
                        if (val != "") 
                            window.filter[name] = val;
                    });
                    for (var i in window.filter)
                        d[i] = window.filter[i];
                    // console.log("ajax data");

                }
            },
            // Use server-side processing
            "serverSide": true,
            // Enable or disable filter
            "filter": true,
            // Enable or disable sorting of columns
            "sort": true,
            // Display processing text
            "processing": true,
            // Save current page,  display length,  filtering and sorting in Cookie
            "stateSave": true,
            "stateSaveParams": function (settings, d) {
                // d.filter = [];
                // for (var i in window.filter)
                //     d[i] = window.filter[i];

                // let elem = $("#title_table");
                // if(elem.length) {
                //     d['title_table'] = elem.html();
                //     d['title_table_class'] = elem.attr("class");
                // } 
                // console.log("stateSaveParams");
            },
            "stateLoadParams": function (settings, d) {
                // for (var i in d){
                //     if(i.startsWith('filter_')){
                //         window.filter[i] = d[i];
                //         let txt = d[i];
                //         if(txt != ""){
                //             let labelElem = $(".custom-filter-label.current_"+i);
                //             let btnElem = $(".btn_"+i+"[data-val='"+txt+"']");
                //             if(labelElem.length){
                //                 if(btnElem.length)
                //                     txt = btnElem.text();
                //                 labelElem.text(txt);
                //             }
                //         }
                //     }
                // }
                // let elem = $("#title_table");
                // if(elem.length && typeof d.title_table != 'undefined') {
                //     elem.html(d.title_table)
                //     if(typeof d.title_table_class != 'undefined')
                //         elem.attr("class", d.title_table_class);
                // }
                // console.log("stateLoadParams");
                // d.search.search = "";
            },
            // Cookie time out (seconds)
            "cookieDuration": 60 * 60 * 24,
            // the search call frequency in Ms unit
            "searchDelay": searchDelay,
            // create the nodes (rows and cells in the table body) only when they are needed for a draw.
            "deferRender": true,
            // display length per page
            "length": 10,
            // set display table, l=length per page, f=filter, t=table, i=info, p=paginate, r=process text
            "language": {
                "url": '<?php echo Module::asset('core:vendor/datatables.net/lang/' . locale() . '.json'); ?>'
            },
            "order": [
                [hasActionLeft, "desc"]
            ],

            "colVis": {
                "restore": true
            },
            "buttons": true,

            "fixedHeader": true,

            "dom": "<'col-sm-12'" +
                "<'row dt-t dt-tt'<'col-sm-6 dt-ttl'l><'col-sm-6 dt-ttb'f>>" +
                "<'row dt-t dt-tm'<'col-sm-12'tr>>" +
                "<'row dt-t dt-tb'<'col-sm-5 dt-tbl'i><'col-sm-7 dt-tbb'p>>" +
                ">",

            "drawCallback": function() {
                toggleActionMenu();
                $('[data-toggle="popover"]').popover({
                    container: '.sc-table',
                    html: true
                });

                //------------- Check all Checkboxes -------------//
                var checkboxFlat = $('input[type="checkbox"].check-item');
                var checkboxFlatAll = $('input[type="checkbox"].check-items');
                checkboxFlat.on("change", function() {
                    if (this.checked) {
                        $(this).parents('tr').addClass('selected');
                    } else
                        $(this).parents('tr').removeClass('selected');
                    toggleActionMenu();
                });
                checkboxFlatAll.on("change", function() {
                    if (this.checked) {
                        checkboxFlat.each(function() {
                            this.checked = true;
                            $(this).parents('tr').addClass('selected');
                        });
                    } else {
                        checkboxFlat.each(function() {
                            this.checked = false;
                            $(this).parents('tr').removeClass('selected');
                        });
                    }
                    toggleActionMenu();
                });
            }

        };
        var toggleActionMenu = function() {
            if ($('.data-table tr.selected').length)
                $('.action-menu').removeClass('hidden');
            else
                $('.action-menu').addClass('hidden');
        };
        var resetPageDataTable = function(index, page) {
            if (index) {
                window.oTable[index].page( page || 'first' ).draw( 'page' );
            } else
                for (var i in window.oTable)
                    window.oTable[i].page( page || 'first' ).draw( 'page' );
        };
        var saveStateDataTable = function(index) {
            if (index) {
                window.oTable[index].state.save();
            } else
                for (var i in window.oTable)
                    window.oTable[i].state.save();
        };
        var clearStateDataTable = function(index) {
            if (index) {
                window.oTable[index].state.clear();
            } else
                for (var i in window.oTable)
                    window.oTable[i].state.clear();
        };
        var reloadDataTable = function(index) {
            if (index) {
                window.oTable[index].ajax.reload();
            } else
                for (var i in window.oTable)
                    window.oTable[i].ajax.reload();
        };
        var redrawDataTable = function(index) {
            if (index) {
                window.oTable[index].draw();
            } else
                for (var i in window.oTable)
                    window.oTable[i].draw();
        };
        var successCallback = function(successText) {
            console.log('successCallback');
            if (typeof successText == 'string')
                swal("Complete!", successText, "success");
            else if ($(".swal2-show").length)
                closeLoadingModal(); // clickConfirmม clickCancel, showLoading
            reloadDataTable();
        };
        var errorCallback = function(errorText) {
            swal("Whoops!", typeof errorText == 'string' ? errorText : "looks like something went wrong.", "error");
            reloadDataTable();
        };
        var initDataTable = function(container, option) {
            var oContainer = container || ".data-table";
            var tOption = option || table_option;
            $(oContainer).each(function(i) {
                // console.log("initDataTable", oContainer, i, $(this).hasClass("dataTable"));
                if ($(this).hasClass("dataTable") && window.oTable[i]) {
                    window.oTable[i].ajax.reload();
                    return;
                }
                $(this).attr('data-index', i);
                if (tOption.ajax.url == '' || tOption.ajax.url == '#') {
                    tOption.ajax.url = $(this).data('ajaxurl');
                }
                if (tOption.columns == null) {
                    tOption.columns = $(this).data('columns'); // JSON.parse($(this).data('columns'));
                }
                var tTable = $(this).DataTable(tOption);

                var buttons_container = $(this).data("buttons_container") || "#rightControlPanel";
                if ($(buttons_container).length) {
                    new $.fn.dataTable.Buttons(tTable);
                    tTable.buttons(0, null).containers().appendTo(buttons_container);
                }
                window.oTable[i] = tTable;
            });
        };

        $(document).ready(function() {
            initDataTable();

            // Add event listener for opening and closing details
            $(document).on('click', '.btn-row-details', function(e) {
                e.preventDefault();
                var tr = $(this).closest('tr');
                var i = $(this).closest('table').data('index');
                var row = window.oTable[i].row(tr);

                if (row.child.isShown()) {
                    // This row is already open - close it
                    row.child.hide();
                    tr.removeClass('shown');
                } else {
                    var d = row.data();
                    // Open this row // function format(d) { return d.name; }
                    // row.child( format(row.data()) ).show();
                    <?php if(Route::has("{$_route['api']}.detailsControl")): ?>
                        $.ajax({
                        url: '<?php echo e(_route("{$_route['api']}.detailsControl", $_route['param'])); ?>',
                        method: "POST",
                        data: d,
                        success: function (html) {
                        row.child(html).show();
                        tr.addClass('shown');
                        }
                        });
                    <?php else: ?>
                        if (d.DT_RowDetails) {
                        row.child(d.DT_RowDetails).show();
                        tr.addClass('shown');
                        }
                    <?php endif; ?>

                }
            });

            <?php if(Route::has($_route['api'] . '.mark-status')): ?>
                $(document).on('click', '.btn-bulk-mark-status', function (e) {
                e.preventDefault();
                var data = $(this).data();
                data.ids = getVal(".check-item:checked");
                // data.val = $(this).val();
                showLoadingModal();
                $.ajax({
                url: '<?php echo e(_route("{$_route['api']}.mark-status", $_route['param'])); ?>',
                data: data,
                method: "POST",
                success: successCallback
                });
                });
                $(document).on('click change', '.btn-row-mark-status', function (e) {
                e.preventDefault();
                var data = $(this).data();
                data.ids = [ data.id ];
                data.val = $(this).val();
                showLoadingModal();
                $.ajax({
                url: '<?php echo e(_route("{$_route['api']}.mark-status", $_route['param'])); ?>',
                data: data,
                method: "POST",
                success: successCallback
                });
                });
            <?php endif; ?>
            <?php if(Route::has($_route['api'] . '.bulk-destroy')): ?>
                $(document).on('click', '.btn-bulk-destroy', function (e) {
                e.preventDefault();
                var data = $(this).data();
                data.ids = getVal(".check-item:checked");
                swalConfirm(0, 0, function () {
                showLoadingModal();
                $.ajax({
                url: '<?php echo e(_route("{$_route['api']}.bulk-destroy", $_route['param'])); ?>',
                method: "DELETE",
                data: data,
                success: successCallback
                });
                });
                });
            <?php endif; ?>
            $(document).on('click', '.btn-bulk-update', function(e) {
                e.preventDefault();
                var data = $(this).data();
                data.ids = getVal(".check-item:checked");
                showLoadingModal();
                $.ajax({
                    url: data['href'],
                    data: data,
                    success: successCallback
                });
            });
            $(document).on('click', '.btn-row-update', function(e) {
                e.preventDefault();
                var data = $(this).data();
                showLoadingModal();
                $.ajax({
                    url: data['href'],
                    data: data,
                    success: successCallback
                });
            });
            $(document).on('click', '.btn-row-del', function(e) {
                e.preventDefault();
                var data = $(this).data();
                swalConfirm(0, 0, function() {
                    showLoadingModal();
                    $.ajax({
                        url: data['href'],
                        data: data,
                        success: successCallback
                    });
                });
            });
        });
    </script>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\Projects\Web\htdocs\ssp4-v11\Modules/Core/Resources/views/datatable.blade.php ENDPATH**/ ?>