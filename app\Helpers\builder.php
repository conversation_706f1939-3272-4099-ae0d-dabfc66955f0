<?php


if (!function_exists('_get_page_title')) {
    function _get_page_title($content)
    {
        if (!$content) return null;

        $res = preg_match("/<title>(.*)<\/title>/siU", $content, $title_matches);
        if (!$res) return null;

        // Clean up title: remove EOL's and excessive whitespace.
        $title = preg_replace('/\s+/', ' ', $title_matches[1]);
        $title = preg_replace('/\,/', '', $title);
        $title = trim($title);
        return $title;
    }
}
if (!function_exists('_remove_html_comments')) {
// Remove HTML comments
    function _remove_html_comments($content = '')
    {
        return preg_replace('/<!--(?!\s*(?:\[if [^\]]+]|<!|>))(?:(?!-->).|\n)*-->/', '', $content);
    }
}
if (!function_exists('_autoload')) {
    function _autoload($className)
    {
        if (file_exists($className . '.php')) {
            require_once $className . '.php';
            return true;
        }
        return false;
    }
}
if (!function_exists('_base64_to_jpeg')) {

    function _base64_to_jpeg($base64_string, $output_file)
    {

        $decoded = base64_decode($base64_string);

        $ifp = fopen($output_file, "wb");

        $data = explode(',', $decoded);

        fwrite($ifp, base64_decode($data[1]));
        fclose($ifp);

        return $output_file;
    }

}
if (!function_exists('_return_bytes')) {
    function _return_bytes($val)
    {
        $ival = (int)trim($val);
        $last = strtolower($val[strlen($val) - 1]);
        switch ($last) {
            case 'g':
                $ival *= 1024 * 1024 * 1024;
                break;
            case 'm':
                $ival *= 1024 * 1024;
                break;
            case 'k':
                $ival *= 1024;
                break;
        }
        return $ival;
    }
}
if (!function_exists('_recurse_copy')) {
    function _recurse_copy($src, $dst, $permissions = 0755, $exceptDir = [])
    {
        Log::info("src: " . $src . "; dst: " . $dst);
        $dir = opendir($src);
        @mkdir($dst, $permissions);
        while (false !== ($file = readdir($dir))) {
            if (($file != '.') && ($file != '..')) {
                if (is_dir($src . '/' . $file)) {
                    if (in_array($file, $exceptDir))
                        continue;
                    _recurse_copy($src . '/' . $file, $dst . '/' . $file);
                } else {
                    copy($src . '/' . $file, $dst . '/' . $file);
                }
            }
        }
        closedir($dir);
        return true;
    }
}
if (!function_exists('_xcopy')) {
    function _xcopy($source, $dest, $permissions = 0755, $exceptDir = [])
    {
        if (is_dir($source)) {
            foreach (_array_wrap($exceptDir) as $xdir)
                if (strpos($source, $xdir) !== false)
                    return;
        }
        // Check for symlinks
        if (is_link($source)) {
            return symlink(readlink($source), $dest);
        }

        // Simple copy for a file
        if (is_file($source)) {
            if (is_readable($source)) {
                return copy($source, $dest);
            } else {
                return;
            }
        }

        // Make destination directory
        if (!is_dir($dest)) {
            mkdir($dest, $permissions);
        }

        // Loop through the folder
        $dir = dir($source);
        while (false !== $entry = $dir->read()) {
            // Skip pointers
            if ($entry == '.' || $entry == '..') {
                continue;
            }

            // Deep copy directories
            _xcopy("$source/$entry", "$dest/$entry", $permissions);
        }

        // Clean up
        $dir->close();
        return true;
    }
}
if (!function_exists('_delete_dir')) {
    function _delete_dir($dirPath)
    {
        if (!is_dir($dirPath)) {
            throw new \InvalidArgumentException("$dirPath must be a directory");
        }
        if (substr($dirPath, strlen($dirPath) - 1, 1) != '/') {
            $dirPath .= '/';
        }
        $files = glob($dirPath . '*', GLOB_MARK);
        foreach ($files as $file) {
            if (is_dir($file)) {
                _delete_dir($file);
            } else {
                unlink($file);
            }
        }
        rmdir($dirPath);
        return true;
    }
}
if (!function_exists('_delete_dir_alt')) {
    function _delete_dir_alt($dir)
    {
        if (is_dir($dir)) {
            $objects = scandir($dir);
            foreach ($objects as $object) {
                if ($object != "." && $object != "..") {
                    if (filetype($dir . "/" . $object) == "dir") _delete_dir_alt($dir . "/" . $object); else unlink($dir . "/" . $object);
                }
            }
            reset($objects);
            rmdir($dir);
        }
    }
}
if (!function_exists('_sanitize_filename')) {
    /**
     * Sanitizes a filename replacing whitespace with dashes
     *
     * Removes special characters that are illegal in filenames on certain
     * operating systems and special characters requiring special escaping
     * to manipulate at the command line. Replaces spaces and consecutive
     * dashes with a single dash. Trim period, dash and underscore from beginning
     * and end of filename.
     *
     * @param string $filename The filename to be sanitized
     * @return string The sanitized filename
     */
    function _sanitize_filename($filename)
    {
        // Remove special accented characters - ie. sí.
        $clean_name = strtr($filename, array('Š' => 'S', 'Ž' => 'Z', 'š' => 's', 'ž' => 'z', 'Ÿ' => 'Y', 'À' => 'A', 'Á' => 'A', 'Â' => 'A', 'Ã' => 'A', 'Ä' => 'A', 'Å' => 'A', 'Ç' => 'C', 'È' => 'E', 'É' => 'E', 'Ê' => 'E', 'Ë' => 'E', 'Ì' => 'I', 'Í' => 'I', 'Î' => 'I', 'Ï' => 'I', 'Ñ' => 'N', 'Ò' => 'O', 'Ó' => 'O', 'Ô' => 'O', 'Õ' => 'O', 'Ö' => 'O', 'Ø' => 'O', 'Ù' => 'U', 'Ú' => 'U', 'Û' => 'U', 'Ü' => 'U', 'Ý' => 'Y', 'à' => 'a', 'á' => 'a', 'â' => 'a', 'ã' => 'a', 'ä' => 'a', 'å' => 'a', 'ç' => 'c', 'è' => 'e', 'é' => 'e', 'ê' => 'e', 'ë' => 'e', 'ì' => 'i', 'í' => 'i', 'î' => 'i', 'ï' => 'i', 'ñ' => 'n', 'ò' => 'o', 'ó' => 'o', 'ô' => 'o', 'õ' => 'o', 'ö' => 'o', 'ø' => 'o', 'ù' => 'u', 'ú' => 'u', 'û' => 'u', 'ü' => 'u', 'ý' => 'y', 'ÿ' => 'y'));
        $clean_name = strtr($clean_name, array('Þ' => 'TH', 'þ' => 'th', 'Ð' => 'DH', 'ð' => 'dh', 'ß' => 'ss', 'Œ' => 'OE', 'œ' => 'oe', 'Æ' => 'AE', 'æ' => 'ae', 'µ' => 'u'));

        // Enforce ASCII-only & no special characters
//	$clean_name = preg_replace(array('/\s+/', '/[^a-zA-Z0-9_\.\-]/'), array('.', ''), $clean_name);
//	$clean_name = preg_replace(array('/--+/', '/__+/', '/\.\.+/'), array('-', '_', '.'), $clean_name);
//	$clean_name = trim($clean_name, '-_.');
//
//	// Some file systems are case-sensitive (e.g. EXT4), some are not (e.g. NTFS).
//	// We simply assume the latter to prevent confusion later.
//	//
//	// Note 1: camelCased file names are converted to dotted all-lowercase: `camel.case`
//	// Note 2: we assume all file systems can handle filenames with multiple dots
//	//         (after all only vintage file systems cannot, e.g. VMS/RMS, FAT/MSDOS)
//	$clean_name = preg_replace('/([a-z])([A-Z]+)/', '$1.$2', $clean_name);
//	$clean_name = strtolower($clean_name);
//
//	// And for operating systems which don't like large paths / filenames, clip the filename to the last 64 characters:
//	$clean_name = substr($clean_name, -64);
//	$clean_name = ltrim($clean_name, '-_.');

        $clean_name = preg_replace("/\s+/", "-", mb_convert_case($clean_name, MB_CASE_LOWER, "UTF-8"));
        return $clean_name;
    }
}
if (!function_exists('_dom_inner_html')) {
    function _dom_inner_html($element)
    {
        $innerHTML = "";
        $children = $element->childNodes;
        foreach ($children as $child) {
            $tmp_dom = new DOMDocument();
            $tmp_dom->appendChild($tmp_dom->importNode($child, true));
            $innerHTML .= trim($tmp_dom->saveHTML());
        }
        return $innerHTML;
    }
}
if (!function_exists('_dom_append_html')) {
    function _dom_append_html(DOMNode $parent, $source)
    {
        $tmpDoc = new DOMDocument();
        $tmpDoc->loadHTML($source);
        foreach ($tmpDoc->getElementsByTagName('body')->item(0)->childNodes as $node) {
            $node = $parent->ownerDocument->importNode($node, true);
            $parent->appendChild($node);
        }
    }
}