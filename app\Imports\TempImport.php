<?php
namespace App\Imports;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithCalculatedFormulas;
use Modules\Training\Entities\Member;

class TempImport implements ToCollection, WithCalculatedFormulas
{
    public function collection(Collection $rows)
    {
        
        // ob_end_clean();  // fix corrupted excel
        // ob_start();

        // return collect($rows)->storeExcel('temp\\tempimport.xlsx', 'storage');  
        // return $rows->downloadExcel('school.xls');  

        DB::beginTransaction();
        
        $updated = false;
        $notfound = [];
        try {

            foreach ($rows as $r => $row) 
            {
                $cert_no = $row[3] . $row[4];
                $m = Member::where("training_id","=",119)->whereHas('user', function($q) use ($row) {
                    $q->whereRaw("CONCAT(first_name,' ',last_name) = '".trim($row[1])."'");
                })->first();

                if($m) {
                    $updated = $m->update(["cert_preset_no" => $cert_no, "checkname_at" => date("Y-m-d H:i:s")]);
                    // $updated = Member::whereId($m->id)->update(["cert_preset_no" => $cert_no]);
                } else
                    $notfound[] = $row[1];
            }

            DB::commit();

        } catch (\Exception $ex) {

            DB::rollback();

            Log::error($ex->getMessage()); 
            dd( $notfound, $ex->getMessage() );
            
        }
    }
}