

<div class="row">
    <div
        class="form-group col-sm-4 <?php echo e($errors->has('res_r2.contact2_pre_name') ? ' has-error has-feedback' : ''); ?>">
        <select name="res_r2[contact2_pre_name]" id="contact2_pre_name" class="form-control"
            placeholder="<?php echo e(_trans('user::users.form.pre_name')); ?>">
            <option value="">- <?php echo e(_trans('user::users.form.pre_name')); ?> -
            </option>
            <?php $__currentLoopData = Arr::get($selects, 'pre_name'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $p => $pre_name): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <option <?php echo e(old('res_r2.contact2_pre_name') == $pre_name ? 'selected' : ''); ?>

                    value="<?php echo e($pre_name); ?>"><?php echo e($pre_name); ?>

                </option>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </select>
        <?php echo $errors->first('res_r2.contact2_pre_name', '<span class="help-block">:message</span>'); ?>

    </div>
    
    <div
        class="form-group col-sm-4 <?php echo e($errors->has('res_r2.contact2_first_name') ? ' has-error has-feedback' : ''); ?>">
        <input type="text" name="res_r2[contact2_first_name]" class="form-control"
            placeholder="<?php echo e(_trans('user::users.form.first_name')); ?>"
            value="<?php echo e(old('res_r2.contact2_first_name')); ?>">
        <?php echo $errors->first('res_r2.contact2_first_name', '<span class="help-block">:message</span>'); ?>

    </div>
    <div
        class="form-group col-sm-4 <?php echo e($errors->has('res_r2.contact2_last_name') ? ' has-error has-feedback' : ''); ?>">
        <input type="text" name="res_r2[contact2_last_name]" class="form-control"
            placeholder="<?php echo e(_trans('user::users.form.last_name')); ?>"
            value="<?php echo e(old('res_r2.contact2_last_name')); ?>">
        <?php echo $errors->first('res_r2.contact2_last_name', '<span class="help-block">:message</span>'); ?>

    </div>
</div>
<div class="row">
    <div
        class="form-group col-sm-4 <?php echo e($errors->has('res_r2.contact2_phone') ? ' has-error has-feedback' : ''); ?>">
        <input type="text" name="res_r2[contact2_phone]" class="form-control"
            placeholder="<?php echo e(_trans('user::users.form.phone')); ?>"
            value="<?php echo e(old('res_r2.contact2_phone')); ?>">
        <?php echo $errors->first('res_r2.contact2_phone', '<span class="help-block">:message</span>'); ?>

    </div>
    <div
        class="form-group col-sm-4 <?php echo e($errors->has('res_r2.contact2_email') ? ' has-error has-feedback' : ''); ?>">
        <input type="text" name="res_r2[contact2_email]" class="form-control"
            placeholder="<?php echo e(_trans('user::users.form.email')); ?>"
            value="<?php echo e(old('res_r2.contact2_email')); ?>">
        <?php echo $errors->first('res_r2.contact2_email', '<span class="help-block">:message</span>'); ?>

    </div>
    <div
        class="form-group col-sm-4 <?php echo e($errors->has('res_r2.contact2_position') ? ' has-error has-feedback' : ''); ?>">
        <input type="text" name="res_r2[contact2_position]" class="form-control"
            placeholder="<?php echo e(_trans('user::users.form.position')); ?>"
            value="<?php echo e(old('res_r2.contact2_position')); ?>">
        <?php echo $errors->first('res_r2.contact2_position', '<span class="help-block">:message</span>'); ?>

    </div>
</div><?php /**PATH C:\Projects\Web\htdocs\ssp4-v11\Modules/SalePrinting/Resources/views/member/sales/partials/create-fields/customer-data-2.blade.php ENDPATH**/ ?>