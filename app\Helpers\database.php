<?php

use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;


if (!function_exists('_get_count_supquery')) {
    /**
     * Having Cause can't count rows correctly.
     * Use supquery can fix this problem.
     * 
     * need to set config database.php 
     * strict => false
     *
     * @return int
     */
    function _get_count_supquery($sub, $where = null)
    {
//        dd($sub->getTable(), $sub->getQuery());
        $q = DB::table(DB::raw("({$sub->toSql()}) as sub"));
        $q->mergeBindings($sub->getQuery()); // you need to get underlying Query Builder
        if (is_callable($where))
            $q = $where($q);
        return $q->count();
    }
}


if (!function_exists('_statistics_select_fixed')) {
    /**
     * Get query string of for statistics  weekly, monthly, yearly, etc.
     * fixed is.. for example  ( Format: d / m / Y )
     *    today: 3 / 10 / 19
     *    result monthly: data from 1 / 10 / 19  to  30 / 10 / 19
     *    result yearly: data from 1 / 1 / 19  to  30 / 12 / 19
     *
     * @return array
     */
    function _statistics_select_fixed($column_date = "`updated_at`", $column_sum = 1, $key_prefix = "")
    {
        list($minute, $hour, $day, $week, $month, $year) = Arr::values(config("istyles.core.core.sql-format"));
        $select_interval = "SUM(IF(DATE_FORMAT({$column_date}, '{$day}') = DATE_FORMAT(CURRENT_DATE, '{$day}'), {$column_sum}, 0)) AS {$key_prefix}_daily,
                            SUM(IF(DATE_FORMAT({$column_date}, '{$week}') = DATE_FORMAT(CURRENT_DATE, '{$week}'), {$column_sum}, 0)) AS {$key_prefix}_weekly,
                            SUM(IF(DATE_FORMAT({$column_date}, '{$month}') = DATE_FORMAT(CURRENT_DATE, '{$month}'), {$column_sum}, 0)) AS {$key_prefix}_monthly,
                            SUM(IF(DATE_FORMAT({$column_date}, '{$year}') = DATE_FORMAT(CURRENT_DATE, '{$year}'), {$column_sum}, 0)) AS {$key_prefix}_yearly";
        $where_interval = "DATE_FORMAT({$column_date}, '{$year}') = DATE_FORMAT(CURRENT_DATE, '{$year}')";

        return [$select_interval, $where_interval];
    }
}


if (!function_exists('_statistics_select_previous')) {
    /**
     * Get query string of for statistics  weekly, monthly, yearly, etc.
     * interval is.. for example  ( Format: d / m / Y )
     *    today: 3 / 10 / 19
     *    result monthly::( previous month )  data from 1 / 9 / 19  to  30 / 9 / 19
     *    result yearly:( previous year )  data from 1 / 1 / 18  to  30 / 12 / 18
     *
     * @return array
     */
    function _statistics_select_previous($column_date = "`updated_at`", $column_sum = 1, $key_prefix = "")
    {
        list($minute, $hour, $day, $week, $month, $year) = Arr::values(config("istyles.core.core.sql-format"));
        $select_interval = "SUM(IF(DATE_FORMAT({$column_date}, '{$day}') = DATE_FORMAT(CURRENT_DATE - INTERVAL 1 DAY, '{$day}'), {$column_sum}, 0)) AS {$key_prefix}_daily_prev,
                            SUM(IF(DATE_FORMAT({$column_date}, '{$week}') = DATE_FORMAT(CURRENT_DATE - INTERVAL 1 WEEK, '{$week}'), {$column_sum}, 0)) AS {$key_prefix}_weekly_prev,
                            SUM(IF(DATE_FORMAT({$column_date}, '{$month}') = DATE_FORMAT(CURRENT_DATE - INTERVAL 1 MONTH, '{$month}'), {$column_sum}, 0)) AS {$key_prefix}_monthly_prev,
                            SUM(IF(DATE_FORMAT({$column_date}, '{$year}') = DATE_FORMAT(CURRENT_DATE - INTERVAL 1 YEAR, '{$year}'), {$column_sum}, 0)) AS {$key_prefix}_yearly_prev";

        $where_interval = "DATE_FORMAT({$column_date}, '{$year}') >= DATE_FORMAT(CURRENT_DATE - INTERVAL 1 YEAR, '{$year}')";

        return [$select_interval, $where_interval];
    }
}


if (!function_exists('_statistics_select_interval')) {
    /**
     * Get query string of for statistics  weekly, monthly, yearly, etc.
     * interval is.. for example  ( Format: d / m / Y )
     *    today: 3 / 10 / 19
     *    result monthly: data from 3 / 9 / 19  to  30 / 10 / 19
     *    result yearly: data from 3 / 10 / 18  to  30 / 10 / 19
     *
     * @return array
     */
    function _statistics_select_interval($column_date = "`updated_at`", $column_sum = 1, $key_prefix = "")
    {
        $select_interval = "SUM(IF({$column_date} > CURRENT_DATE, {$column_sum}, 0)) AS {$key_prefix}_daily,
                           SUM(IF({$column_date} > (CURRENT_DATE - INTERVAL 1 WEEK), {$column_sum}, 0)) AS {$key_prefix}_weekly,
                           SUM(IF({$column_date} > (CURRENT_DATE - INTERVAL 1 MONTH), {$column_sum}, 0)) AS {$key_prefix}_monthly,
                           SUM(IF({$column_date} > (CURRENT_DATE - INTERVAL 1 YEAR), {$column_sum}, 0)) AS {$key_prefix}_yearly";
        $where_interval = "{$column_date} > (CURRENT_DATE - INTERVAL 1 YEAR)";
        return [$select_interval, $where_interval];
    }
}


function _statistics_select_last_N_months($column_date = "`updated_at`", $column_sum = 1, $key_prefix = "", $interval = 7, $haft_month = false)
{
    $select_interval = '';
    list($minute, $hour, $day, $week, $month, $year) = Arr::values(config("istyles.core.core.sql-format"));

    $dates = [];
    for ($m = $interval - 1; $m >= 0; $m--) {
        $strtotime = strtotime("- {$m} MONTH");
        $ifmonth_last = date("Y-m", $strtotime);
//        $last_day = date("j", strtotime("last day of - 11 month"));
        $last_day = date("j", strtotime("last day of - {$m} month"));

        $mnt = date("M", $strtotime);
        $key = ["start_day", "last_day", "count_day", "month"];

        $ifdatefull_last = $ifmonth_last . "-" . $last_day;
        $dates["{$key_prefix}_monthly_{$m}"] = array_combine($key, [1, $last_day, $last_day, $mnt]);
        $select_interval .= " SUM(IF(DATE_FORMAT({$column_date}, '{$day}') <= '{$ifdatefull_last}' AND DATE_FORMAT({$column_date}, '{$month}') = '{$ifmonth_last}', {$column_sum}, 0)) AS {$key_prefix}_monthly_{$m},";

        if ($haft_month) {
            $ifdatehaft_last = $ifmonth_last . "-" . 15;
            $dates["{$key_prefix}_monthly_{$m}_1haft"] = array_combine($key, [1, 15, 15, $mnt]);
            $dates["{$key_prefix}_monthly_{$m}_2haft"] = array_combine($key, [16, $last_day, $last_day - 15, $mnt]); // start_day last_day count_day
            $select_interval .= " SUM(IF(DATE_FORMAT({$column_date}, '{$day}') <= '{$ifdatehaft_last}' AND DATE_FORMAT({$column_date}, '{$month}') = '{$ifmonth_last}', {$column_sum}, 0)) AS {$key_prefix}_monthly_{$m}_1haft,";
            $select_interval .= " SUM(IF(DATE_FORMAT({$column_date}, '{$day}') > '{$ifdatehaft_last}' AND DATE_FORMAT({$column_date}, '{$month}') = '{$ifmonth_last}', {$column_sum}, 0)) AS {$key_prefix}_monthly_{$m}_2haft,";
        }
    }
    $where_interval = "DATE_FORMAT({$column_date}, '{$year}') >= DATE_FORMAT(CURRENT_DATE - INTERVAL 1 YEAR, '{$year}')";

    return [rtrim($select_interval, ","), $where_interval, $dates];
}

function _statistics_select_last_N_days($column_date = "`updated_at`", $column_sum = 1, $key_prefix = "", $interval = 7)
{
    list($minute, $hour, $day, $week, $month, $year) = Arr::values(config("istyles.core.core.sql-format"));
    $select_interval = "";
    $dates = [];
//        for ($d = 1; $d <= $interval; $d++) {
    for ($d = $interval - 1; $d >= 0; $d--) {
//        for ($d = $interval; $d >= 1; $d--) {
        $strtotime = strtotime("- {$d} days");
        $dates["{$key_prefix}_day{$d}"] = date("d M", $strtotime);
        $date = date("Y-m-d", $strtotime);
        $select_interval .= "SUM(IF(DATE_FORMAT({$column_date}, '{$day}') = '{$date}', {$column_sum}, 0)) AS {$key_prefix}_day{$d},";
    }

    $where_interval = "DATE_FORMAT({$column_date}, '{$day}') >= DATE_FORMAT(CURRENT_DATE - INTERVAL {$interval} DAY, '{$day}')";

    return [rtrim($select_interval, ","), $where_interval, $dates];
}

if (!function_exists('_calculate_diff')) {
    /**
     * Calculate up and down of this week and previous week, this month and previous month, this year and previous year, etc.
     *
     * @return void
     */
    function _calculate_diff(&$stats, $prefix, $stat_current, $stat_prev)
    {
        $stats[$prefix . '_down'] = $stat_current < $stat_prev;

        if ($stats[$prefix . '_down']) {
            $stats[$prefix . '_diff'] = ($stat_prev - $stat_current) / $stat_prev;
        } else if ($stat_current != 0) {
            $stats[$prefix . '_diff'] = ($stat_current - $stat_prev) / $stat_current;
        } else
            $stats[$prefix . '_diff'] = 0;

        $stats[$prefix . '_diff'] = round($stats[$prefix . '_diff'] * 100);
        $stats[$prefix . '_goal'] = $stats[$prefix . '_down'] ? 100 - $stats[$prefix . '_diff'] : 100;
    }
}
if (!function_exists('_calculate_diff_interval')) {
    /**
     * Calculate up and down of this week and previous week, this month and previous month, this year and previous year, etc.
     *
     * @return array
     */
    function _calculate_diff_interval($cols, $stat_current, $stat_prev)
    {
        if (!is_array($cols)) $cols = ['_daily', '_weekly', '_monthly', '_yearly'];
        $stats = [];
        foreach ($cols as $col) {
//            $stats = array_merge($stats, _calculate_diff($col, $stat_current[$col], $stat_prev[$col]));
            _calculate_diff($stats, $col, $stat_current[$col], $stat_prev[$col]);
//            $stats[$col . '_down'] = $stat_current[$col] < $stat_prev[$col];
//
//            if ($stats[$col . '_down']) {
//                $stats[$col . '_diff'] = ($stat_prev[$col] - $stat_current[$col]) / $stat_prev[$col];
//            } else if ($stat_current[$col] != 0) {
//                $stats[$col . '_diff'] = ($stat_current[$col] - $stat_prev[$col]) / $stat_current[$col];
//            } else
//                $stats[$col . '_diff'] = 0;
//
//            $stats[$col . '_diff'] = round($stats[$col . '_diff'] * 100);
//            $stats[$col . '_goal'] = $stats[$col . '_down'] ? 100 - $stats[$col . '_diff'] : 100;
        }
        return $stats;
    }
}