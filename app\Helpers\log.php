<?php

if (!function_exists('_log_published')) {
    function _log_published($target, $account_id = null, $post_id = null, $data = [], $items = [], $result = [])
    {
        \Modules\Core\Entities\LogPublished::create([
            'target' => $target,                  // facebook, twitter, lazada, shopee, etc.
            'target_id' => $account_id,     // shopID, pageID, accountID, etc.
            'post_id' => $post_id,              // posts.id on our database that make published
            'published_data' => $data,      // formatted posts that make published
            'published_items' => $items,  // list of itemsID on target that just published
            'published_result' => $result, // response of the target. success, error, errors message, etc.
        ]);
    }
}