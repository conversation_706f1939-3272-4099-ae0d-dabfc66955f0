<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Transcoder;

class AudioChannelInput extends \Google\Model
{
  /**
   * @var int
   */
  public $channel;
  public $gainDb;
  /**
   * @var string
   */
  public $key;
  /**
   * @var int
   */
  public $track;

  /**
   * @param int
   */
  public function setChannel($channel)
  {
    $this->channel = $channel;
  }
  /**
   * @return int
   */
  public function getChannel()
  {
    return $this->channel;
  }
  public function setGainDb($gainDb)
  {
    $this->gainDb = $gainDb;
  }
  public function getGainDb()
  {
    return $this->gainDb;
  }
  /**
   * @param string
   */
  public function setKey($key)
  {
    $this->key = $key;
  }
  /**
   * @return string
   */
  public function getKey()
  {
    return $this->key;
  }
  /**
   * @param int
   */
  public function setTrack($track)
  {
    $this->track = $track;
  }
  /**
   * @return int
   */
  public function getTrack()
  {
    return $this->track;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(AudioChannelInput::class, 'Google_Service_Transcoder_AudioChannelInput');
