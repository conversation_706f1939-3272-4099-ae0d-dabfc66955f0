<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Str;
use Illuminate\Support\Arr;

if (!function_exists('_call')) {
    function _call($func)
    {
        if (is_callable($func))
            $func();
        return '';
    }
}

if (!function_exists('_dd')) {
    function _dd()
    {
        if (_is_dev())
            dd(func_get_args());
        return '';
    }
}

if (!function_exists('_bb')) {
    /**
     * For debug
     *
     * @param mixed $var
     */
    function _bb($var)
    {
        echo '<pre>';
        var_dump($var);
        echo '</pre>';
        exit();
    }
}

if (!function_exists('_route')) {
    /**
     * Generate the URL to a named route.
     *
     * @param  array|string $name
     * @param  array $parameters
     * @param  bool $absolute
     * @return string
     */
    function _route($name, $parameters = [], $absolute = true, $default = '#')
    {
        if (Route::has($name))
            return app('url')->route($name, $parameters, $absolute);
        return $default;
    }
}

if (!function_exists('_type_segment')) {
    function _type_segment($module = null)
    {
        if (is_null($module) || request()->segment(2) === $module) {
            if (preg_match("/^\/[^\/]{2}\/.*$/", request()->getPathInfo()))
                return request()->segment(4);
            return request()->segment(3);
        }
        return null;
    }
}

if (!function_exists('_request_all')) {
    function _request_all()
    {
        return request()->except(['_site', '_host', '_skin', '_lang']);
    }
}
if (!function_exists('_option')) {
    function _option($config)
    {
        return Arr::pluck(_config($config, null, true), 'name', 'code');
    }
}
if (!function_exists('_config')) {
    function _config($key = null, $default = null, $activeOnly = false)
    {
        if ($activeOnly)
            return collect(config($key, $default))->where('active', true)->toArray();
        return config($key, $default);
    }
}
if (!function_exists('_config_trans')) {
    function _config_trans($key = null, $locale = null)
    {
        $config = config($key);
        if (is_array($config)) {
            $formatted = [];
            foreach ($config as $key => $trans_key) {
                if (Lang::has($trans_key))
                    $formatted[$key] = app('translator')->trans($trans_key, [], $locale);
                else
                    $formatted[$key] = str_replace("-", " ", Str::title(end(explode('.', $trans_key))));
            }
            return $formatted;
        }
        if (Lang::has($config))
            return app('translator')->trans($config, [], $locale);
        return str_replace("-", " ", Str::title(end(explode('.', $config))));
    }
}

if (!function_exists('_setting')) {
    function _setting($name = null, $locale = null, $default = null, $array_get = null)
    {
        // Modules\Setting\Contracts\Setting;
        // Modules\Setting\Support\Setting;
        if (!$name)
            return app('setting.settings');
        $setting = app('setting.settings')->get($name, $locale);
        $result  = $setting && $setting != '' ? $setting : ($default ?? config("istyles.setting.config.default.{$name}"));
        if ($array_get) {
            $result = json_decode($result, true);
            return is_string($array_get) ? Arr::get($result, $array_get, $result) : $result;
        }
        return $result;
    }
}
if (!function_exists('_setting_uid')) {
    function _setting_uid($name = null, $locale = null, $default = null, $array_get = null)
    {
        $uid = auth()->id();
        return _setting("uid{$uid}@" . $name, $locale, $default, $array_get);
    }
}
if (!function_exists('_settings_uid')) {
    function _settings_uid($name = null, $locale = null, $default = null, $array_get = null)
    {
        $uid      = auth()->id();
        $_name    = "uid{$uid}@" . $name . "%";
        $settings = app('setting.settings')->gets($_name, $locale);
        $result   = [];
        foreach ($settings as $sname => $svalue) {
            $key = end(explode("::", $sname));
            if ($array_get) {
                $svalue       = _json_decode($svalue);
                $result[$key] = is_string($array_get) ? Arr::get($svalue, $array_get, $svalue) : $svalue;
            } else
                $result[$key] = $svalue;
        }
        return $result;
    }
}
if (!function_exists('_setting_transformed')) {
    function _setting_transformed($name = null, $group = null, $array_get = null)
    {
        return app('setting.settings')->findByModuleGroup($name, $group)->transform(function ($item) use ($array_get) {
            preg_match("/(.+)::([^\.]+)\.(.+)/", $item->name, $matched);
            if ($array_get) {
                $plainValue = _json_decode($item->plainValue);
                $result     = is_string($array_get) ? Arr::get($plainValue, $array_get, $plainValue) : $plainValue;
            } else
                $result = $item->plainValue;
            return ['name' => $item->name, 'value' => $result, 'module' => $matched[1], 'group' => $matched[2], 'field' => $matched[3]];
        })->keyBy("field");
    }
}

if (!function_exists('_view_or_abort')) {
    /**
     * Get the evaluated view contents for the given view.
     *
     * @param  string $view
     * @param  array $data
     * @param  array $mergeData
     * @return \Illuminate\View\View|\Illuminate\Contracts\View\Factory
     */
    function _view_or_abort($view = null, $data = [], $mergeData = [])
    {
        if (!view()->exists($view)) {
            abort(404);
        }

        return view($view, $data, $mergeData);
    }
}

if (!function_exists('_view')) {
    function _view($html_blade = null, $data_for_compile = [], $cache_key = null, $ref_key = null)
    {
        return view([
            // this actual blade template
            'template'                    => $html_blade,
            // this is the cache file key, converted to md5
            'cache_key'                   => $cache_key ?? ("autokey-" . date("Y-m-d-H")),
            // number of seconds needed in order to recompile, 0 is always recompile
            'secondsTemplateCacheExpires' => 1391973007,
            // sets the PATH comment value in the compiled file
            'templateRefKey'              => $ref_key ?? (__CLASS__ . __METHOD__)
        ], $data_for_compile);
    }
}
if (!function_exists('_controller')) {
    /**
     * Call Controllers method.
     *
     * @param String $controller
     * @param String $method
     * @param String|Array $param
     * @return \Illuminate\Http\JsonResponse
     */
    function _controller($controller, $method, $param)
    {
        return app("App\\Http\\Controllers\\" . ucfirst($controller) . 'Controller')->$method(is_array($param) ? $param : [$param]);
    }
}
if (!function_exists('_render')) {
    /*
     * Blade compile String
     * */
    function _render($__php, $__data)
    {
        $obLevel = ob_get_level();
        ob_start();
        extract($__data, EXTR_SKIP);
        try {
            eval('?' . '>' . $__php);
        } catch (\Exception $e) {
            while (ob_get_level() > $obLevel)
                ob_end_clean();
            throw $e;
        }
        return ob_get_clean();
    }
}

if (!function_exists('_date')) {
    function _date($data, $format = 'j M Y')
    {
        // j M Y                 => 03 Sep 2019
        // d M y                 => 3 Sep 19
        // F j, Y                 => September 30, 2019
        // d/m/Y g:i A      => 30/09/2019 3:57 AM
        if (!$format)
            $format = config('post.date-format') . ' ' . config('post.time-format');
        return is_a($data, 'Carbon\Carbon') ? $data->format($format) : date($format, strtotime($data));
    }
}

if (!function_exists('_date_thai')) {
    function _date_thai($format = null, $strDate = null)
    {
        $month_long  = array('มกราคม', 'กุมภาพันธ์', 'มีนาคม', 'เมษายน', 'พฤษภาคม', 'มิถุนายน', 'กรกฎาคม', 'สิงหาคม', 'กันยายน', 'ตุลาคม', 'พฤศจิกายน', 'ธันวาคม');
        $month_short = array('ม.ค.', 'ก.พ.', 'มี.ค.', 'เม.ย.', 'พ.ค.', 'มิ.ย.', 'ก.ค.', 'ส.ค.', 'ก.ย.', 'ต.ค.', 'พ.ย.', 'ธ.ค.');

        $day_long  = array('อาทิตย์', 'จันทร์', 'อังคาร', 'พุธ', 'พฤหัสบดี', 'ศุกร์', 'เสาร์');
        $day_short = array('อา.', 'จ.', 'อ.', 'พ.', 'พฤ.', 'ศ.', 'ส.');

        // $format
        //Day	---	--- d	D	 j	 l  N	 S	 w	 z
        //Week	---	--- W
        //Month	---	--- F	 m	 M	 n	 t
        //Year	---	--- L	 o	 Y	 y
        //Time	---	--- a	 A	 B	 g	 G	 h	 H	 i	 s	 u	 v
        //Timezone	---	---  e	 I   O	 P   T	 Z
        //Full Date/Time	---	---  c   r	 U
        $timestamp = !$strDate ? time() : (is_string($strDate) ? strtotime($strDate) : $strDate);

        // if use Buddhist era, convert the year.
        if (mb_strpos($format, 'o') !== false) {
            $year   = (date('o', $timestamp) + 543);
            $format = str_replace('o', $year, $format);
        } elseif (mb_strpos($format, 'Y') !== false) {
            $year   = (date('Y', $timestamp) + 543);
            $format = str_replace('Y', $year, $format);
        } elseif (mb_strpos($format, 'y') !== false) {
            $year   = (date('y', $timestamp) + 43);
            $format = str_replace('y', $year, $format);
        }
        unset($year);

        // replace format that will be convert month into name (F, M) to Thai.
        if (mb_strpos($format, 'F') !== false || mb_strpos($format, 'M') !== false) {
            $month_num = (date('n', $timestamp) - 1);
            if (mb_strpos($format, 'F') !== false && array_key_exists($month_num, $month_long)) {
                $format = str_replace('F', $month_long[$month_num], $format);
            } elseif (mb_strpos($format, 'M') !== false && array_key_exists($month_num, $month_short)) {
                $format = str_replace('M', $month_short[$month_num], $format);
            }
            unset($month_num);
        }

        // replace format that will be convert day into name (D, l) to Thai.
        if (mb_strpos($format, 'l') !== false || mb_strpos($format, 'D') !== false) {
            $day_num = date('w', $timestamp);
            if (mb_strpos($format, 'l') !== false && array_key_exists($day_num, $day_long)) {
                $format = str_replace('l', $day_long[$day_num], $format);
            } elseif (mb_strpos($format, 'D') !== false && array_key_exists($day_num, $day_short)) {
                $format = str_replace('D', $day_short[$day_num], $format);
            }
            unset($day_num);
        }

        return date($format, $timestamp);
    }
}
if (!function_exists('_date_db_to_input')) {
    function _date_db_to_input($item, $column)
    {
        return optional($item)->getDateFormatedForInput($column);
    }
}
if (!function_exists('_date_input_to_db')) {
    function _date_input_to_db($input)
    {
        if ($input)
            if ($input == "Invalid date" || $input == "0000-00-00 00:00:00" || $input == "") {
                $input = null;
            } else {
                $datetime = @\Carbon\Carbon::parse($input);
                //                $datetime = @\Carbon\Carbon::createFromFormat('d/m/Y g:i A', $input);
                $input = @$datetime->format('Y-m-d H:i:s');
            }
        return $input;
    }
}
if (!function_exists('_date_diff')) {
    /**
     *
     * //////////////////////////////////////////////////////////////////////
     * //Note: Date Should In YYYY-MM-DD Format
     * //RESULT FORMAT:
     * // '%y Year %m Month %d Day %h Hours %i Minute %s Seconds'          =>  1 Year 3 Month 14 Day 11 Hours 49 Minute 36 Seconds
     * // '%y Year %m Month %d Day'                                        =>  1 Year 3 Month 14 Days
     * // '%m Month %d Day'                                                =>  3 Month 14 Day
     * // '%d Day %h Hours'                                                =>  14 Day 11 Hours
     * // '%d Day'                                                         =>  14 Days
     * // '%h Hours %i Minute %s Seconds'                                  =>  11 Hours 49 Minute 36 Seconds
     * // '%i Minute %s Seconds'                                           =>  49 Minute 36 Seconds
     * // '%h Hours                                                        =>  11 Hours
     * // '%a Days                                                         =>  468 Days
     * //////////////////////////////////////////////////////////////////////
     *
     * @param string $date_1
     * @param string $date_2
     * @param string $differenceFormat
     * @return string
     */
    function _date_diff($date_1, $date_2, $differenceFormat = '%d')
    {
        $datetime1 = date_create($date_1);
        $datetime2 = date_create($date_2);
        $interval  = date_diff($datetime1, $datetime2);
        return $interval->format($differenceFormat);
    }
}
if (!function_exists('_date_diff_array')) {
    function _date_diff_array($date_1, $date_2)
    {
        $differenceFormat = '%y_%m_%d_%h_%i_%s';
        $diffTxt          = _date_diff($date_1, $date_2, $differenceFormat);
        $keys             = explode("_", str_replace("%", '', $differenceFormat));
        $vals             = explode("_", $diffTxt);
        return array_combine($keys, $vals);
    }
}
if (!function_exists('_date_diff_filtered')) {
    function _date_diff_filtered($date_1, $date_2, $units = ['y' => 'Year(s)', 'm' => 'Month(s)', 'd' => 'Day(s)', 'h' => 'Hours', 'i' => 'Minute', 's' => 'Seconds'])
    {
        $result  = [];
        $diffArr = _date_diff_array($date_1, $date_2);
        foreach (_array_wrap($diffArr) as $kd => $vd) {
            if ($vd != '0' && isset($units[$kd])) {
                $result[] = "{$vd} {$units[$kd]}";
            }
        }
        return implode(" ", $result);
    }
}

if (!function_exists('_date_diff_filtered_once')) {
    function _date_diff_filtered_once($date_1, $date_2, $units = ['y' => 'Year(s) ago', 'm' => 'Month(s) ago', 'd' => 'Day(s) ago', 'h' => 'Hours ago', 'i' => 'Minute ago', 's' => 'Seconds ago'])
    {
        $diffArr = _date_diff_array($date_1, $date_2);
        foreach (_array_wrap($diffArr) as $kd => $vd) {
            if ($vd != '0' && isset($units[$kd])) {
                return "{$vd} {$units[$kd]}";
            }
        }
        return null;
    }
}
if (!function_exists('_date_diff_start_end')) {
    function _date_diff_start_end($date_start, $date_end, $units = ['y' => 'Year(s)', 'm' => 'Month(s)', 'd' => 'Day(s)', 'h' => 'Hours', 'i' => 'Minute', 's' => 'Seconds'], $prefix = [], $postfix = [])
    {
        $date_now   = date('Y-m-d H:i:s');
        $time_now   = time();
        $time_start = strtotime($date_start);
        $time_end   = strtotime($date_end);
        $_prefix    = $_postfix = "";

        if ($time_now < $time_start) {
            $date_2   = $date_start;
            $_prefix  = Arr::get($prefix, "before_start", "");
            $_postfix = Arr::get($postfix, "before_start", "");
        } elseif ($time_now > $time_start && $time_now < $time_end) {
            $date_2   = $date_end;
            $_prefix  = Arr::get($prefix, "before_end", Arr::get($prefix, "after_start", ""));
            $_postfix = Arr::get($postfix, "before_end", Arr::get($prefix, "after_start", ""));
        } elseif ($time_now > $time_end) {
            $date_2   = $date_end;
            $_prefix  = Arr::get($prefix, "after_end", "");
            $_postfix = Arr::get($postfix, "after_end", "");
        }

        $diffArr = _date_diff_array($date_now, $date_2);
        foreach (_array_wrap($diffArr) as $kd => $vd) {
            if ($vd != '0' && isset($units[$kd])) {
                return "{$_prefix} {$vd} {$units[$kd]}{$_postfix}";
            }
        }
        return null;
    }
}

if (!function_exists('_year_fiscal')) {
    /* ปีงบประมาณ */
    function _year_fiscal()
    {
        if (time() <= strtotime(date("Y") . "-09-30"))
            return (int) date("Y");
        return (int) date("Y") + 1;
    }
}


if (!function_exists('_year_fiscal_thai')) {
    /* ปีงบประมาณ */
    function _year_fiscal_thai()
    { 
        return _year_fiscal() + 543;
    }
}


if (!function_exists('_trim')) {
    function _trim($data)
    {
        $data = trim(htmlspecialchars($data));
        return $data;
    }
}
if (!function_exists('_strip_tags')) {
    function _strip_tags($html_txt)
    {
        $data = strip_tags(html_entity_decode($html_txt));
        return $data;
    }
}
if (!function_exists('_str_slug')) {
    /**
     * Generate a URL friendly "slug" from a given string.
     *
     * @param  string $title
     * @param  string $separator
     * @return string
     */
    function _str_slug($title, $separator = '-')
    {

        // Convert all dashes/underscores into separator
        $flip = $separator == '-' ? '_' : '-';

        $title = preg_replace('![' . preg_quote($flip) . ']+!u', $separator, $title);

        // Remove all characters that are not the separator, letters, numbers, or whitespace.
        $title = preg_replace('![^' . preg_quote($separator) . '\pL\pN\sก-๙เแ]+!u', '', mb_strtolower($title));

        // Replace all separator characters and whitespace by a single separator
        $title = preg_replace('![' . preg_quote($separator) . '\s]+!u', $separator, $title);

        return trim($title, $separator);
    }
}
if (!function_exists('_str_options')) {
    /**
     * Generate a Options string from a given array.
     *
     * @param  array $options
     * @param  string $default
     * @param  string $separator
     * @return string
     */
    function _str_options($options = [], $default = "", $separator = " - ")
    {
        $_item = array_filter($options, function ($value) {
            return $value !== '' && !is_null($value);
        });
        $str   = implode($separator, $_item);
        if ($default && $str == "")
            return $default;
        return $str;
    }
}
if (!function_exists('_str_title')) {
    /**
     * Generate a Title from a given slug.
     *
     * @param  string $slug
     * @return string
     */
    function _str_title($slug)
    {

        return ucfirst(str_replace(['_', '-'], ' ', $slug));
    }
}
if (!function_exists('_substr')) {
    function _substr($input, $sub_length = 70)
    {
        if ($sub_length < 0)
            return $input;
        $_substr = iconv_substr($input, 0, $sub_length, "UTF-8");
        return $_substr . (strlen($input) > strlen($_substr) ? '...' : '');
    }
}
if (!function_exists('_substr_pad')) {
    /*
     *
     * @param string $input
     * @param int $pad_length
     * @param string $pad_string
     * @param int $pad_type ( STR_PAD_RIGHT (1), STR_PAD_LEFT (0), STR_PAD_BOTH (2))
     * @param int $sub_type ( STR_PAD_RIGHT (1), STR_PAD_LEFT (0), STR_PAD_BOTH (2))
     * */
    function _substr_pad($input, $pad_length = 3, $pad_string = "0", $pad_type = STR_PAD_LEFT, $sub_type = null)
    {
        $input_len = strlen($input);

        if ($input_len === $pad_length)
            return $input;

        if ($input_len < $pad_length)
            return str_pad($input, $pad_length, $pad_string, $pad_type);

        if (is_null($sub_type))
            $sub_type = $pad_type;

        // pad left mean the most priority number is at the right, example: pad 34 => 034, sub 1234 => 234
        if ($sub_type == STR_PAD_LEFT)
            return iconv_substr($input, $input_len - $pad_length, $pad_length, "UTF-8");

        // example: pad 34 => 340, sub 1234 => 123
        if ($sub_type == STR_PAD_RIGHT)
            return iconv_substr($input, 0, $pad_length, "UTF-8");

        // example: pad 34 => 0340, sub 1234 => 23
        return iconv_substr($input, round(($input_len - $pad_length) / 2), $pad_length, "UTF-8");
    }
}
if (!function_exists('_utf8_strlen')) {
    /* Or just:  mb_strlen($str,'UTF-8') */
    function _utf8_strlen($s)
    {
        $c = strlen($s);
        $l = 0;
        for ($i = 0; $i < $c; ++$i)
            if ((ord($s[$i]) & 0xC0) != 0x80)
                ++$l;
        return $l;
    }
}
if (!function_exists('_thai_wordwrap')) {
    /**
     * wordwrap for utf8 encoded strings
     *
     * @param string $str
     * @param integer $width
     * @param string $break
     * @return string
     */
    function _thai_wordwrap($str, $width, $break = false, $cut = true)
    {
        $result         = [];
        $is_ended       = false;
        $remain         = $str;
        $nextRoundWidth = $width;
        $i              = 0;
        while (!$is_ended) {
            $thisRoundWidth = $nextRoundWidth;
            $cut            = iconv_substr($str, 0, $thisRoundWidth, "utf-8");
            $remain         = iconv_substr($str, $thisRoundWidth, 2147483647, "utf-8");
            if ($remain) {
                //                $regexp = '/([\x{0E00}-\x{0E7F}])/u'; // ก-ฮ และ สระ ทั้งหมด
                //                $regexp = '/([\x{0E2B}-\x{0E7F}])/u'; // สระ ทั้งหมด
                $regexp = '/([\x{0E30}-\x{0E3A}]|[\x{0E47}-\x{0E4E}])/u'; //    สระ บน/ล่าง เช่น ไม้เอก ไม้โท สระอิ สระอี ฯลฯ
                $check  = iconv_substr($remain, 0, 1, "utf-8");
                if ($cut && preg_match($regexp, $check)) {
                    $nextRoundWidth = $thisRoundWidth + 1;
                } else {
                    $str            = $remain;
                    $nextRoundWidth = $width;
                    $result[]       = trim($cut);
                }
            } else {
                $is_ended = true;
                $result[] = trim($cut);
            }
            $i++;
        }
        return $break ? implode($break, $result) : $result;
    }
}
if (!function_exists('_chunk_split_unicode')) {
    function _chunk_split_unicode($str, $l = 76, $e = "\r\n")
    {
        $tmp = Arr::chunk(
            preg_split("//u", $str, -1, PREG_SPLIT_NO_EMPTY),
            $l
        );
        $str = "";
        foreach ($tmp as $t) {
            $str .= join("", $t) . $e;
        }
        return $str;
    }
}
if (!function_exists('_chunk_split_unicode_to_array')) {
    function _chunk_split_unicode_to_array($str, $l = 28, $e = "__SEP__")
    {
        $chunk = _chunk_split_unicode($str, $l, $e);
        return explode($e, trim($chunk, $e));
    }
}

if (!function_exists('_conv_bytes')) {
    /**
     * Convert bytes to other prefix
     *
     * @return string
     */
    function _conv_bytes($bytes)
    {
        $si_prefix = array('B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB');
        $base      = 1024;
        $class     = min((int) log($bytes, $base), count($si_prefix) - 1);
        //        echo $bytes . '<br />';
        return sprintf('%1.0f', $bytes / pow($base, $class)) . ' ' . $si_prefix[$class];
    }
}
if (!function_exists('_conv_datetime')) {
    function _conv_datetime($datetime, $key = false)
    {
        if (!$datetime || $datetime == '')
            return '';
        if (!is_array($datetime)) {
            if (is_a($datetime, 'Carbon\Carbon')) {
                $date = $datetime->format('Y-m-d');
                $time = $datetime->format('h:i A');
            } else {
                $times = strtotime($datetime);
                $date  = date('Y-m-d', $times);
                $time  = date('h:i A', $times);
            }
            $datetime = ['date' => $date, 'time' => $time];
            if (!$key)
                return $datetime;
            return $datetime[$key];
        } else {
            return date('Y-m-d H:i:s', strtotime($datetime['date'] . ' ' . $datetime['time']));
        }
    }
}
if (!function_exists('_conv_currency')) {
    /**
     * Convert number to thai currency
     *
     * @param  string $path
     * @param  string $path_thumbs
     */
    function _conv_currency($number)
    {
        $txtnum1 = array('ศูนย์', 'หนึ่ง', 'สอง', 'สาม', 'สี่', 'ห้า', 'หก', 'เจ็ด', 'แปด', 'เก้า', 'สิบ');
        $txtnum2 = array('', 'สิบ', 'ร้อย', 'พัน', 'หมื่น', 'แสน', 'ล้าน', 'สิบ', 'ร้อย', 'พัน', 'หมื่น', 'แสน', 'ล้าน');
        $number  = str_replace(",", "", $number);
        $number  = str_replace(" ", "", $number);
        $number  = str_replace("บาท", "", $number);
        $number  = explode(".", $number);
        if (sizeof($number) > 2) {
            return 'ทศนิยมหลายตัวนะจ๊ะ';
            exit;
        }
        $strlen  = strlen($number[0]);
        $convert = '';
        for ($i = 0; $i < $strlen; $i++) {
            $n = substr($number[0], $i, 1);
            if ($n != 0) {
                if ($i == ($strlen - 1) and $n == 1) {
                    $convert .= 'เอ็ด';
                } elseif ($i == ($strlen - 2) and $n == 2) {
                    $convert .= 'ยี่';
                } elseif ($i == ($strlen - 2) and $n == 1) {
                    $convert .= '';
                } else {
                    $convert .= $txtnum1[$n];
                }
                $convert .= $txtnum2[$strlen - $i - 1];
            }
        }

        $convert .= 'บาท';
        if (
            $number[1] == '0' or $number[1] == '00' or
            $number[1] == ''
        ) {
            $convert .= 'ถ้วน';
        } else {
            $strlen = strlen($number[1]);
            for ($i = 0; $i < $strlen; $i++) {
                $n = substr($number[1], $i, 1);
                if ($n != 0) {
                    if ($i == ($strlen - 1) and $n == 1) {
                        $convert
                            .= 'เอ็ด';
                    } elseif (
                        $i == ($strlen - 2) and
                        $n == 2
                    ) {
                        $convert .= 'ยี่';
                    } elseif (
                        $i == ($strlen - 2) and
                        $n == 1
                    ) {
                        $convert .= '';
                    } else {
                        $convert .= $txtnum1[$n];
                    }
                    $convert .= $txtnum2[$strlen - $i - 1];
                }
            }
            $convert .= 'สตางค์';
        }
        return $convert;
    }
}
if (!function_exists('_hex_to_rgba')) {

    function _hex_to_rgba($hex, $opacity = 1, &$result = [])
    {
        $result          = sscanf($hex, "#%02x%02x%02x");
        list($r, $g, $b) = $result;
        return "rgba($r, $g, $b, $opacity)";
    }
}

if (!function_exists('_save_history')) {
    /**
     * Save History
     *
     * @return string
     */
    function _save_history($title, $link)
    {
        $history = json_decode(cookie('history'), true);
        if (!is_array($history))
            $history = [];
        $history = array_merge([$title => $link], $history);
        array_unique($history);
        if (count($history) > 5)
            Arr::pop($history);
        return cookie('history', _json_encode($history));
    }
}

if (!function_exists('_get_range_shipping_fee')) {
    function _get_range_shipping_fee($kg = 0, $provider = 'ems', $default_provider = 'tnt')
    {
        $g                = $kg * 1000;
        $default_shipping = config("istyles.order.shipping.{$default_provider}");
        if ($g > $default_shipping['price_limit']) {
            return end($default_shipping['prices']) + (($g - $default_shipping['price_limit']) / 1000) * $default_shipping['price_additional'];
        } else {
            $shipping = config("istyles.order.shipping.{$provider}");
            $_range   = ($g <= $shipping['price_limit']) ? $shipping['prices'] : $default_shipping['prices'];
            foreach ($_range as $w => $price)
                if ($g <= $w)
                    return $price;
        }
        return false;
    }
}
if (!function_exists('_calculate_price')) {
    /*
     * @return array
     * */
    function _calculate_price($market, $data, $option = [])
    {
        $rounded_value  = $option['rounded_value'] ? $data['rounded_value'] : PHP_ROUND_HALF_UP;
        $base_price     = !$option['inc_vat'] ? $data['base_price'] : ($data['base_price'] + ($data['base_price'] * ($data['inc_vat'] / 100))); // profit_local != or > price_percent because vat
        $promotion      = $data['promotion'] ?? 0;
        $pricing_market = config("istyles.order.pricing.{$market}");

        if (!$data['profit_percent']) {
            $chk_range_key  = _get_range_key($base_price, $pricing_market['price_range'], $pricing_market['price_limit']);
            $profit_percent = Arr::get($pricing_market['prices'], $chk_range_key);
        } else
            $profit_percent = $data['profit_percent'];
        $profit_amount      = round((int) $base_price * $profit_percent);
        $commission_percent = $option['inc_commission'] ? ($data['commission_percent'] ?? $pricing_market['commission']) : 0;
        $shipping_fee       = $option['inc_shipping_fee'] ? ($data['inc_shipping_fee'] ?? _get_range_shipping_fee($data['weight'])) : 0;
        $price              = round((((100 * $profit_amount) + (100 * $base_price)) / (100 - $commission_percent)) + $shipping_fee, $rounded_value);
        $commission_amount  = $option['inc_commission'] ? ($price * $commission_percent / 100) : 0;
        $profit_result      = round($price - $base_price - $commission_amount - $shipping_fee - $promotion);

        return compact('profit_percent', 'profit_amount', 'shipping_fee', 'price', 'profit_result', 'commission_percent', 'commission_amount');
    }
}
if (!function_exists('_get_range_key')) {
    function _get_range_key($calculate, $range_length = 5000, $limit = 45001)
    {
        if ($limit && (($range_length * floor(($calculate - 1) / $range_length) + 1) >= $limit))
            return $limit . "+";
        return ($range_length * floor(($calculate - 1) / $range_length) + 1) . '-' . ($range_length * floor(($calculate - 1) / $range_length) + $range_length);
    }
}

if (!function_exists('_math_growth')) {
    function _math_growth($current_data, $last_data)
    {
        if ($current_data > 0 && $last_data == 0)
            return 100;
        else if ($current_data < 0 && $last_data == 0)
            return -100;
        else if ($current_data == 0 && $last_data == 0)
            return 0;
        return round(($current_data - $last_data) / abs($last_data) * 100, 1);
    }
}