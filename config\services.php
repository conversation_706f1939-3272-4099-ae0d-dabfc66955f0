<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    // @ https://www.google.com/recaptcha/admin
    'recaptcha' => [
        'key' => env('RECAPTCHA_KEY', '6Ld_TNMUAAAAACjtn_H4VGcZYiJkTbvrGJRx_TtB'),
        'secret' => env('RECAPTCHA_SECRET', '6Ld_TNMUAAAAAPcjnKXWaSHnEwGYHlP6aXiMLoAR'),
    ],

    // @ https://developers.facebook.com/apps/
    'facebook' => [
        'mode' => 'live', // test, live

        'live_client_id' => env('FACEBOOK_CLIENT_ID', "1709907899207414"),         // Your FACEBOOK Client ID // 380017579534974
        'live_client_secret' => env('FACEBOOK_CLIENT_SECRET', "********************************"), // Your FACEBOOK Client Secret // ********************************

        'client_id' => env('FACEBOOK_CLIENT_ID', "395347988324571"),         // Your FACEBOOK Client ID // 380017579534974
        'client_secret' => env('FACEBOOK_CLIENT_SECRET', "********************************"), // Your FACEBOOK Client Secret // ********************************
        'redirect' => 'https://ssp3.i-styles.co/oauth-facebook/login-callback',

        'name' => "Facebook",
        'icon' => "fab fa-facebook",
        'color' => "#ccc",
        'type' => "social",
        'active' => true,
        'module_login' => true,
        'module_post' => true,
        'module_comment' => true,

        'admins' => '100057894992373',
        // 'pages' => '101591171775530', // ศึกษาภัณฑ์พาณิชย์
        'pages' => '110087807845632', // ลูกน้องเต็มใจ
        'test_pages' => '334195894686651',
    ],

    // @ https://developer.twitter.com/apps
    // Consumer Key  (API Key) & Consumer Secret (API Secret)
    'twitter' => [
        'client_id' => env('TWITTER_CLIENT_ID', "*************************"),         // Your TWITTER Client ID
        'client_secret' => env('TWITTER_CLIENT_SECRET', "kmHUFTvZhcaccfTAtmRl2BHSCd75ye7ZAItmDlhizJNlV8HBzY"), // Your TWITTER Client Secret
        'redirect' => 'https://ssp3.i-styles.co/oauth-twitter/login-callback',

        'name' => "Twitter",
        'icon' => "fab fa-twitter",
        'color' => "#ccc",
        'type' => "social",
        'active' => true,
        'module_login' => false,
        'module_post' => true,

        'creator' => "@istylesltd",
    ],

    // @ https://console.developers.google.com/apis/credentials/oauthclient/
    'google' => [
        'client_id' => env('GOOGLE_CLIENT_ID', "574599924967-hrrt9ve66rq1hlqjt50vfrv52ldo0g2e.apps.googleusercontent.com"),         // Your GOOGLE Client ID
        'client_secret' => env('GOOGLE_CLIENT_SECRET', "ZY0U2MsF9wJk5sGn5uZA6ryN"), // Your GOOGLE Client Secret
        'redirect' => 'https://ssp3.i-styles.co/oauth-google/login-callback',

        'name' => "Google+",
        'icon' => "fab fa-google-plus",
        'color' => "#ccc",
        'type' => "social",
        'active' => true,
        'module_login' => true,
        'module_post' => false,
    ],

    // @ https://developers.line.biz/console/
    'line' => [
        'mode' => 'live', // test, live

        // 'test_client_id' => env('LINE_CHANNEL_ID', "1656526229"),
        // 'test_client_secret' => env('LINE_CHANNEL_SECRET', "4d5fd77070dde000883a6c9cb8ad6a31"), // colsole: dfef122646afea8d9988150d1c75077e , msg: b90e0d46f44236181e94ebde8e27c649

        'client_id' => env('LINE_CHANNEL_ID', '1656526229'),         // Your LINE_CHANNEL Client ID
        'client_secret' => env('LINE_CHANNEL_SECRET', '4d5fd77070dde000883a6c9cb8ad6a31'), // Your LINE_CHANNEL Client Secret
        'redirect' => 'https://ssp3.i-styles.co/oauth-line/login-callback',

        'name' => "Line",
        'icon' => "fab fa-line",
        'color' => "#00be00",
        'type' => "social",
        'active' => false,
        'module_login' => false,
        'module_post' => false,

        'admins' => 'Ubcb4718a7a8a05fe53bb86cedf0d0974'
    ],

    // @ https://marketplace.zoom.us/user/build
    'zoom' => [
        'mode' => 'test', // test, live

        'client_id' => env('ZOOM_CLIENT_ID', 'Bvp_mcr4Tbm4wuX6VLqdsw'),                     // Your Zoom Client ID
        'client_secret' => env('ZOOM_CLIENT_SECRET', 'ngUACILmdC8buzxv7pistiDXH5lJPyOa'),   // Your Zoom Client Secret
         
        'redirect' => 'https://ssp3.i-styles.co/oauth-zoom/login-callback',

        'test_client_id' => env('ZOOM_TEST_CLIENT_ID', 'YuGnA0NFQ3K7OU056CPwvg'),         // Your Zoom TEST Client ID
        'test_client_secret' => env('ZOOM_TEST_CLIENT_SECRET', 'aTQBA0JjUT79V42x3ZhOkOJiCIvl6ula'), // Your Zoom TEST Client Secret

        'sdk_client_id' => env('ZOOM_SDK_CLIENT_ID', ''),         // Your Zoom SDK Client ID
        'sdk_client_secret' => env('ZOOM_SDK_CLIENT_SECRET', ''), // Your Zoom SDK Client Secret

        'jwt_client_id' => env('ZOOM_JWT_CLIENT_ID', ''),                         // Your Zoom JWT Client ID
        'jwt_client_secret' => env('ZOOM_JWT_CLIENT_SECRET', ''),   // Your Zoom JWT Client Secret
        'jwt_im_token' => env('ZOOM_JWT_IM_TOKEN', ''), // Your Zoom IM Chat History Token
        'jwt_token' => env('ZOOM_JWT_TOKEN', ''), // Your Zoom JWT Token 
        'jwt_expire' => '11:12 10/14/2100', // H:m mm/dd/YY

        'name' => "Zoom",
        'icon' => "fas fa-video",
        'color' => "#ccc",
        'type' => "social",
        'active' => true,
        'module_login' => true,
        'module_post' => false,
    ], 
    
    'linkedin' => [
        'client_id' => env('LINKEDIN_CLIENT_ID'),         // Your LINKEDIN Client ID
        'client_secret' => env('LINKEDIN_CLIENT_SECRET'), // Your LINKEDIN Client Secret
        'redirect' => 'https://ssp3.i-styles.co/oauth-linkedin/login-callback',

        'name' => "LinkedIn",
        'icon' => "fab fa-linkedin",
        'color' => "#ccc",
        'type' => "social",
        'active' => false,
        'module_login' => false,
        'module_post' => false,
    ],

 
    'github' => [
        'client_id' => env('GITHUB_CLIENT_ID'),         // Your GitHub Client ID
        'client_secret' => env('GITHUB_CLIENT_SECRET'), // Your GitHub Client Secret
        'redirect' => 'https://ssp3.i-styles.co/oauth-github/login-callback',

        'name' => "GitHub",
        'icon' => "fab fa-github",
        'color' => "#ccc",
        'type' => "social",
        'active' => false,
        'module_login' => false,
        'module_post' => false,
    ],


    // @ https://open.lazada.com/app/index.htm
    // commission: https://sellercenter.lazada.co.th/seller/helpcenter/ลาซาด้ามีการเก็บค่าคอมมิชชั่นกับร้านค้า-LazMall-หรือไม่.html
    'lazada' => [
        'mode' => 'live', // test, live

        'test_client_id' => env('LAZADA_CLIENT_ID', "116387"),         // Your LAZADA Client ID 116295
        'test_client_secret' => env('LAZADA_CLIENT_SECRET', "wdXegGGFOZQpmi3Gl37O2w26NHBBnkyo"), // Your LAZADA Client Secret eBkFEtkeApxYvlgs3EzH7i24dfbXCHiW

        'client_id' => env('LAZADA_CLIENT_ID', "116389"),         // Your LAZADA Client ID
        'client_secret' => env('LAZADA_CLIENT_SECRET', "vd4DbBaMEtCrcQhDYIpkmWZEXZuEzlCk"), // Your LAZADA Client Secret
        'redirect' => 'https://ssp3.i-styles.co/oauth-lazada/get-token',

        'name' => "Lazada",
        'color' => "#ef7026",
        'icon' => "fas fa-shopping-cart",
        'logo' => '/media/.global/icons/logo-lazada3.png',
        'sample_link' => 'https://www.lazada.co.th/-i717708893-s1373980703.html',

        'type' => "marketplace",
        'active' => true,
        'module_login' => false,
        'module_stock' => true,
    ],


    // @ https://open.shopee.com/console
    'shopee' => [
        'mode' => 'live', // test, live

        'test_client_id' => env('LAZADA_CLIENT_ID', "842173"),         // Your Shopee Client ID
        'test_client_secret' => env('LAZADA_CLIENT_SECRET', "5d374a3c4cf255a799f73ec6abecd9800a72b1d95ad521fbb3e3db8c02cd5037"), // Your Shopee Client Secret

        'client_id' => env('LAZADA_CLIENT_ID', "844914"),         // Your Shopee Client ID
        'client_secret' => env('LAZADA_CLIENT_SECRET', "7e4413debbfbfcb903ede7f42348aac471f19cbe9660dbeeb9be6f9263b70999"), // Your Shopee Client Secret
        'redirect' => 'https://ssp3.i-styles.co/oauth-shopee/get-token',

        'name' => "Shopee",
        'color' => "#ee4d2d",
        'icon' => "fas fa-shopping-cart",
        'logo' => '/media/.global/icons/logo-shopee3.png',
        'sample_link' => 'https://shopee.co.th/product/237289339/4141846069',

        'type' => "marketplace",
        'active' => true,
        'module_login' => false,
        'module_stock' => true,
    ],


    // @ https://istylesltd.disqus.com/admin/
    'disqus' => [
        'client_id' => env('DISQUS_CLIENT_ID', ""),         // Your DISQUS Client ID
        'client_secret' => env('DISQUS_CLIENT_SECRET', ""), // Your DISQUS Client Secret
        'redirect' => '',

        'name' => "Disqus",
        'icon' => "fas fa-comments",
        'color' => "#ccc",
        'type' => "comment",
        'active' => true,
        'module_login' => false,
        'module_post' => false,
        'module_comment' => true,

        'shortname' => "istylesltd",
    ],

    // @ https://track.thailandpost.co.th/dashboard
    'thailandpost' => [
        // 'client_id' => env('THAILANDPOST_CLIENT_ID', ""),         // Your THAILANDPOST Client ID
        // 'client_secret' => env('THAILANDPOST_CLIENT_SECRET', ""), // Your THAILANDPOST Client Secret
        'api_token' => env('THAILANDPOST_API_TOKEN', "TnR8GXOgQ2IRWVZ8VMP7AiZACQFcA7RtZRXmKaFANuE&NJYvDPEhOCH9AWX&CaU-UtHJRURrWBB4GiB6D3TzQsRUROJzJ*VnX@P5"), // Your THAILANDPOST Client Secret
        'redirect' => 'https://ssp3.i-styles.co/oauth-thailandpost/get-token',
        'webhooks' => 'https://ssp3.i-styles.co/oauth-thailandpost/callback-subscription',

        'name' => "ThailandPost",
        'icon' => "fas fa-send",
        'color' => "#ccc",
        'type' => "shipping",
        'active' => true,
        'module_login' => false,
        'module_post' => false,
        'module_comment' => false,

        'bearer' => "51a7699f-1eb6-4372-86c1-136887d26e9b",
    ],
];
