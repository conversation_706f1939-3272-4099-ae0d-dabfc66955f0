<?php

namespace App\Providers;
define('GET_DEV', isset($_GET['dev']) && $_GET['dev']);

use Carbon\Carbon;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        Schema::defaultStringLength(191);

        Carbon::setLocale(config('app.locale'));
        Carbon::serializeUsing(function (Carbon $carbon) {
            // return $carbon->format('d/m/y H:i:s');
            return $carbon->format('d M Y h:i A');
        });
 

        // disable error type NOTICE DEPRECATED and STRICT
        if (!GET_DEV)
            error_reporting(E_ALL & ~E_NOTICE & ~E_DEPRECATED & ~E_STRICT & ~E_WARNING);

        // Turn On PHP Error Reporting
        // ini_set('display_errors', 'on');
        // error_reporting(-1);

        // if exceed limit it will error time out, default 60
        set_time_limit(0);
        ini_set('mysql.connect_timeout', '0');
        ini_set('max_execution_time', '0');
        ini_set('memory_limit', '-1');
    }

    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }
}
