{
    "workbench.colorTheme": "Default Dark+",
    "projectManager.git.baseFolders": [
        "D:\\xampp\\htdocs"
    ],
    "projectManager.git.ignoredFolders": [
        "node_modules",
        "out",
        "typings",
        "test",
        ".haxelib",
        "vendor",
        ".env",
        ".idea",
        ".temp",
        ".backup",
        ".vscode",
        ".local",
        "composer.lock",
        "Note.txt",
        "storage/*.key"
    ],
    "openInGitHub.providerType": "github",
    "window.zoomLevel": -1,
    "editor.mouseWheelZoom": true,
    "editor.accessibilitySupport": "off",
    "php.format.rules.alignConsecutiveAssignments": true,
    "php.format.rules.arrayInitializersAlignKeyValuePairs": true,
    "php.docblock.functionSnippet": {
        
    },
    "php.problems.exclude" : {
        "/" : [416],
        "vendor/" : true,
    },
    "git.ignoreLimitWarning": true
}