
<?php $__env->startPush('css-stack'); ?>
<link rel="stylesheet" type="text/css" href="<?php echo e(Module::asset('core:vendor/select2/4.1.0-rc.0/select2.min.css')); ?>">


<style>
    .custom-checkbox .custom-control-input:checked~.custom-control-label::before {
        background-color: #ff8084 !important;
    }
 
    .authentication-box .container .form-group {
        position: relative;
    }

    .swal2-popup {
        width: 36em !important;
    }

    input[type="file"] {
        display: none;
    }

    .select2-results__options li {
        display: block !important;
    }
    
    .repeat-item td{
        padding: 0!important;
        /* vertical-align: middle; */
    }

</style>
<?php $__env->stopPush(); ?>
<?php $__env->startPush('js-stack'); ?>
<script id="template-demand-repeat" type="text/html">
    <tr class="repeat-item">
        <td class="text-center"> 
            <input type="number" name="customer_demand_new[__NO__][no]" 
                class="form-control customer_demand_new_no input-no text-center" value="">
        </td> 
        <td>
            <select class="form-control customer_demand_product" name="customer_demand_new[__NO__][product]" >
                <?php $__currentLoopData = Arr::get($saleprinting_data , 'customer_demand'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $n => $customer_demand): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <option value="<?php echo e($customer_demand['label']); ?>"><?php echo e($customer_demand['label']); ?></option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>
            <input type="text" name="customer_demand_new[__NO__][product_other]" placeholder="ระบุ.."
                class="form-control customer_demand_product_other d-none" value="">
        </td>
        <td><input type="number" name="customer_demand_new[__NO__][amount]" id="demand-amount" class="form-control demand-amount text-right pl-0">
        </td> 
        <td>
            <button type="button" class="btn btn-repeat-remove btn-danger float-right"><i class="fa fa-times"></i>
            </button>
        </td>
    </tr>
</script> 
<script src="<?php echo e(Module::asset('core:vendor/select2/4.1.0-rc.0/select2.min.js')); ?>"></script>
<script type="text/javascript">

    var loadFile = function(event, parent) {
        if (!event.target.files.length)
            return false;
        let src = URL.createObjectURL(event.target.files[0]);

        parent.addClass('has-file');
        parent.find('label').css({
            'background-image': 'url("' + src + '")'
        });
        // console.log(parent.find('input')[0].files);
    };

    $(document).ready(function() {

        $(".selectize").select2(); 

        var html = $("#template-demand-repeat").html();
        $(document).on("click", ".btn-repeat-add", function() {
            var section = $(this).data("repeat");
            var latest = $(this).data("latest");
            var result = html.replaceAll("__NO__", latest + 1);
            $(this).data("latest", latest + 1);
            $("#section-" + section).append(result);
            $(".text-no").each(function(ind) {
                $(this).text(ind + 1);
            });
            $(".input-no").each(function(ind) {
                $(this).val(ind + 1);
            });
        });
        $(document).on("click", ".btn-repeat-remove", function() {
            $(this).parents(".repeat-item").remove();
            $(".text-no").each(function(ind) {
                $(this).text(ind + 1);
            });
            $(".input-no").each(function(ind) {
                $(this).val(ind + 1);
            });
        });
        
        $(document).on("change", ".customer_demand_product", function() {
            let val = $(this).val();
            let parent = $(this).parent();
            if ('อื่นๆ' === val || '0' == val) {
                parent.find(".customer_demand_product_other").removeClass("d-none");
            } else {
                parent.find(".customer_demand_product_other").addClass("d-none");
            }
        });
        
        $(document).on("submit", "form", function(e) {
            Swal.fire("กำลังบันทึก..");
            Swal.showLoading();
        });

        $(document).on("click", ".custom-file-upload .remove-file", function(e) {
            let parent = $(this).parent();
            let id = $(this).data("id");
            if(id)
                parent.append('<input type="hidden" name="del_images[]" value="'+id+'">');
            parent.removeClass('has-file');
            parent.find('input.input-file-upload').val(null);
            parent.find('label').css({
                'background-image': 'unset'
            });
        });

        $(document).on("change", ".input-file-upload", function(e) {
            loadFile(e, $(this).parent());
        });

        $(document).on("change", "#customer_type", function(e) {
            let val = $(this).val();
            if ('สถานศึกษา' === val || '1' == val) {
                $("#customer_school").removeClass("d-none");
                $("#customer_other").addClass("d-none");
                $("#customer_name").attr("disabled", "");
            } else {
                $("#customer_school").addClass("d-none");
                $("#customer_other").removeClass("d-none");
                $("#customer_name").removeAttr("disabled");
                $("#o_school_id").val("");
                $("#o_school_no").val("");
                $("#o_school_type").val("");
                $("#o_school_size").val("");
            }
        });
        $(document).on("change", "#presentation", function(e) {
            let val = $(this).val();
            if ('อื่นๆ' === val || '0' == val) {
                $("#row_presentation_other").removeClass("d-none");
            } else {
                $("#row_presentation_other").addClass("d-none");
            }
        });

        $(document).on("click", ".btn-row-school-select", function() {
            var o_school_id = $(this).data("id");
            var o_school_no = $(this).data("school_no");
            var o_school_name = $(this).data("school_name");
            var o_school_province = $(this).data("school_province");
            var o_school_district = $(this).data("school_district");
            var o_school_subdistrict = $(this).data("school_subdistrict");
            var o_school_postcode = $(this).data("school_postcode");
 
            $("#o_school_id").val(o_school_id);
            $("#o_school_no").val(o_school_no);
            $("#o_school_name").val(o_school_name); 

            $("#customer_postcode").val(o_school_postcode).trigger("change", {
                callbackAfter: function() {
                    $("#customer_subdistrict").val(o_school_subdistrict);
                    $("#customer_district").val(o_school_district).trigger(
                    'change.select2');
                    $("#customer_province").val(o_school_province).trigger(
                    'change.select2');
                }
            });

            $("#modal-school-selection").modal("hide");
        });


        /*
                    $(document).on("change", "#customer_region", function() {
                        var field = $(this).attr("name"),
                            search = $(this).val();
                        $.ajax({
                            url: "<?php echo _route('api.setting.address.getProvince'); ?>",
                            method: 'post',
                            data: {
                                region: search,
                                select: 'id'
                            },
                            success: function(result) {
                                var option = "<option value='0'>- เลือกจังหวัด -</option>";
                                for (var i in result.data)
                                    option += "<option value='" + i + "'>" + result.data[i] +
                                    "</option>";
                                $("#customer_region").val(result.region_id);
                                $("#customer_province").removeAttr("disabled").html(option);
                                $("#customer_district").attr("disabled", "").html("");
                                $("#customer_subdistrict").attr("disabled", "").html("");
                            },
                            error: function() {}
                        });
                        return false;
                    });
        */
        $(document).on("change", "#customer_province", function() {
            var field = $(this).attr("name"),
                search = $(this).val();
            $.ajax({
                url: "<?php echo _route('api.setting.address.getDistrict'); ?>",
                method: 'post',
                data: {
                    province: search,
                    select: 'id'
                },
                success: function(result) {
                    var option = "<option value='0'>- เลือกอำเภอ/เขต -</option>";
                    for (var i in result.data)
                        option += "<option value='" + i + "'>" + result.data[i] +
                        "</option>";
                    $("#customer_district").removeAttr("disabled").html(option);
                    $("#customer_subdistrict").attr("disabled", "").html("");
                    // $("#customer_province").val(result.province_id);
                    $("#customer_region").val(result.region_id);
                    $("#customer_postcode").val("");
                },
                error: function() {}
            });
            return false;
        });
        var customer_postcode = [];
        var customer_subdistricts = [];
        $(document).on("change", "#customer_district", function() {
            var field = $(this).attr("name"),
                search = $(this).val();
            $.ajax({
                url: "<?php echo _route('api.setting.address.getSubdistrict'); ?>",
                method: 'post',
                data: {
                    district: search,
                    select: 'id'
                },
                success: function(result) {
                    var option = "<option value='0'>- เลือกตำบล/แขวง -</option>";
                    for (var i in result.data)
                        option += "<option value='" + i + "'>" + result.data[i] +
                        "</option>";
                    $("#customer_subdistrict").removeAttr("disabled").html(option);
                    $("#customer_province").val(result.province_id).trigger(
                        'change.select2');
                    $("#customer_region").val(result.region_id);
                    $("#customer_postcode").val("");
                    customer_subdistricts = result.subdistricts;
                    // customer_postcode = result.postcodes;
                },
                error: function() {}
            });
            return false;
        });
        $(document).on("change", "#customer_subdistrict", function() {
            var search = $(this).val();
            $("#customer_postcode").val(customer_subdistricts[search].postcode);
            $("#customer_district").val(customer_subdistricts[search].district_id).trigger(
                'change.select2');
            return false;
            // $("#customer_postcode").val(customer_postcode[search]); 
        });
        $(document).on("change", "#customer_postcode", function(e, param) {
            var field = $(this).attr("name"),
                search = $(this).val();
            $.ajax({
                url: "<?php echo _route('api.setting.address.getAddressFromPostcode'); ?>",
                method: 'post',
                data: {
                    postcode: search,
                    select: 'id'
                },
                success: function(result) {
                    var option1 = "<option value='0'>- เลือกตำบล/แขวง -</option>";
                    for (var i in result.subdistricts) {
                        option1 += "<option " + (i == result.subdistrict_id ?
                                "selected" : "") + " value='" + i +
                            "'>" + result.subdistricts[i].subdistrict_th +
                            "</option>";
                    }
                    $("#customer_subdistrict").removeAttr("disabled").html(option1);

                    var option2 = "<option value='0'>- เลือกอำเภอ/เขต -</option>";
                    for (var i in result.districts) {
                        option2 += "<option " + (i == result.district_id ?
                                "selected" : "") + " value='" + i +
                            "'>" + result.districts[i].district_th +
                            "</option>";
                    }
                    $("#customer_district").removeAttr("disabled").html(option2);
                    $("#customer_province").val(result.province_id).trigger(
                        'change.select2');
                    $("#customer_region").val(result.region_id);
                    // $("#customer_province").find("option[value="+result.selected_province+"]").attr("selected","");
                    // customer_postcode = result.postcodes;
                    customer_subdistricts = result.subdistricts;

                    if (param && typeof param.callbackAfter === 'function') {
                        param.callbackAfter();
                    }
                },
                error: function() {}
            });
            return false;
        });
    });
</script>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\Projects\Web\htdocs\ssp4-v11\Modules/SalePrinting/Resources/views/member/sales/partials/scripts.blade.php ENDPATH**/ ?>