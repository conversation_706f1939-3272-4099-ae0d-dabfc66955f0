<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
use Illuminate\Support\Facades\DB;
use Modules\User\Http\Controllers\Api\PremiumExpirationController;
// use Modules\User\Jobs\PremiumExpirationCheck;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        //
    ];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {

        // Test __invoke
        $schedule->call(new PremiumExpirationController())
        ->name('PremiumExpirationController')
        // ->withoutOverlapping()
        ->daily();

        // $schedule->job(new PremiumExpirationCheck(''))->everyFiveMinutes()->withoutOverlapping();

        // $schedule->command('inspire')->hourly();

        // $schedule->call(function () { 
        //     ...
        // })->name('test_call')
        // ->withoutOverlapping()
        // ->everyFiveMinutes();


        // })->daily();
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
