<?php

namespace Modules\Core\Composers;

use Illuminate\Contracts\View\View;
use Modules\Core\Foundation\AsgardCms;

class ApplicationVersionViewComposer
{
    public function compose(View $view)
    {
        if (app('asgard.onBackend') === false) {
            return;
        }
        $view->with('version',  env('APP_VERSION', '1.0.0'));
        // $view->with('version', AsgardCms::VERSION);
    }
}
