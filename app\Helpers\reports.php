<?php
use Illuminate\Support\Arr;

if (!function_exists('_stat_quarterly')) {
    function _stat_quarterly($stats, $backY = 3)
    {
        if (!is_array($stats))
            $stats = $stats::selectRaw("DATE_FORMAT(`created_at`,'%Y-%m') as _date,
            DATE_FORMAT(`created_at`,'%Y') as _y,
            DATE_FORMAT(`created_at`,'%m') as _m,
            COUNT(id) as _monthly
            ")
                ->whereRaw("`created_at` > (CURRENT_DATE - INTERVAL {$backY} YEAR)")
                ->groupBy("_date")
                ->get()
                ->keyBy("_date")
                ->toArray();

        $yearly = $quarterly = $monthly = [];
        // for ($y = date('Y'); $y > date('Y') - 3; $y--) {
        for ($y = date('Y') - ($backY-1); $y <= date('Y'); $y++) {
            $q             = 0;
            $quarterly[$y] = [];
            $fiscals       = [$y - 1 . '-10', $y - 1 . '-11', $y - 1 . '-12', $y . '-01', $y . '-02', $y . '-03', $y . '-04', $y . '-05', $y . '-06', $y . '-07', $y . '-08', $y . '-09'];
            foreach ($fiscals as $m => $ym) {
                // to next quarter
                if ($m % 3 == 0) {
                    $q += 1;
                }
                $monthly[$y][$q][$ym] = Arr::get($stats, $ym . "._monthly");
                $quarterly[$y][$q] += $monthly[$y][$q][$ym];

            }
            $yearly[$y] = array_sum($quarterly[$y]);

        }
        return collect(compact("stats", "monthly", "quarterly", "yearly"));
    }
}