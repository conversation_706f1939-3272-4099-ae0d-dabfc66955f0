<div class="input-group-prepend">
    <div class="dropdown">
        <a class="btn custom-bg-outline-gray custom-filter-label current_filter_result dropdown-toggle" href="#"
            role="button" id="dropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"
            data-class="primary">
            <?php echo e(Arr::get($current_filter_result, 'label', '- เลือกผลการตอบรับ -')); ?>

        </a>
        <div class="dropdown-menu" aria-labelledby="dropdownMenuLink">
            <?php $__currentLoopData = Arr::get($salemarketing_data, 'results'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $result): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <a class="dropdown-item btn_filter_result custom-bg-<?php echo e(Arr::get($current_filter_result, 'code', 'all') == $result['code'] ? '' : 'outline-'); ?><?php echo e($result['class']); ?>"
                    data-val="<?php echo e($result['code']); ?>" data-color="<?php echo e($result['color']); ?>"
                    data-class="<?php echo e($result['class']); ?>"><?php echo e($result['label']); ?></a>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
        <input type="hidden" name="filter_result" class="custom-filter"
            value="<?php echo e(Arr::get($current_filter_result, 'code', 'all')); ?>">
    </div>
</div>
<div class="input-group-prepend">
    <div class="dropdown">
        <a class="btn custom-bg-outline-gray custom-filter-label current_filter_result_cond dropdown-toggle"
            href="#" role="button" id="dropdownMenuLink" data-toggle="dropdown" aria-haspopup="true"
            aria-expanded="false">
            <?php echo e(request()->has('result_cond') ? Arr::get($salemarketing_data, 'result_cond.'.request()->result_cond.'.label') : '- เลือกเงื่อนไข -'); ?>

        </a>
        <div class="dropdown-menu" aria-labelledby="dropdownMenuLink">
            <?php $__currentLoopData = Arr::get($salemarketing_data, 'result_cond'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $result): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <a class="dropdown-item btn_filter_result_cond"
                    data-val="<?php echo e($result['value']); ?>" data-color="<?php echo e($result['color']); ?>"
                    data-class="<?php echo e($result['class']); ?>"><?php echo e($result['label']); ?></a>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            
        </div>
        <input type="hidden" name="filter_result_cond" class="custom-filter"
            value="<?php echo e(request()->has('result_cond') ? ( request()->result_cond ? '1' : '0') : ''); ?>">
    </div>
</div>
 
<?php $__env->startPush('js-stack'); ?>

    <script id="script-filter-input-result" type="text/javascript">

        $(document).ready(function() {
            $(document).on("click", ".btn_filter_result", function() {
                let card = $(this).parents('.card'); 
                card.find('.btn_filter_result').each(function() {
                    let _class = $(this).data("class");
                    $(this).removeClass("custom-bg-" + _class).addClass("custom-bg-outline-" + _class);
                });
                let txt = $(this).text();
                let val = $(this).data("val");
                let _class = $(this).data("class");
                let _color = $(this).data("color");
                $(this).addClass("custom-bg-" + _class).removeClass("custom-bg-outline-" + _class);
                card.find('.current_filter_result').text(txt);

                window.filter["filter_result"] = val;
                var query = buildQueryString(window.filter).replaceAll("filter_", "");
                if(typeof window.resetPageDataTable === 'function')
                    resetPageDataTable();
                // window.location.href = "<?php echo e(route('supervisor.salemarketing.sales.index')); ?>" + "?" + query;
                window.location.href = window.location.origin + window.location.pathname + "?" + query;
       
                // reloadDataTable();
            });
            $(document).on("click", ".btn_filter_result_cond", function() {
                let card = $(this).parents('.card');  
                let txt = $(this).text();
                let val = $(this).data("val");
                card.find('.current_filter_result_cond').text(txt);
                
                $(".custom-filter[name=filter_result_cond]").val(val);
                window.filter["filter_result_cond"] = val;
                var query = buildQueryString(window.filter).replaceAll("filter_", "");
                if(typeof window.resetPageDataTable === 'function')
                    resetPageDataTable();
                window.location.href = window.location.origin + window.location.pathname + "?" + query;
       
                // reloadDataTable();
            });

        });
    </script>

<?php $__env->stopPush(); ?><?php /**PATH C:\Projects\Web\htdocs\ssp4-v11\Modules/SaleMarketing/Resources/views/member/report/partials/filter-input-result.blade.php ENDPATH**/ ?>