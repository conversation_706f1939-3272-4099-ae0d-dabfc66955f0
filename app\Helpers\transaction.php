<?php

use Illuminate\Support\Arr;

if (!function_exists('_generate_billing_number')) {
    function _generate_billing_number($document_type, $format = null)
    {
        // Auto Generate Order No.
        // CCC =  documents type code
        $number_field = "billing_no";
        $number_prefix = config("istyles.core.documents.{$document_type}.code");
        switch ($format) {
            case 1:    // Format (YYMMCCCXXXXXX)
                $sequential_fill_zero = 6;
                $number_prefix .= substr((date('Y') + 543) . date('m'), 2);
                break;
            case 3:    // Format (YYMMDDCCCXXXX)
            default:
                $sequential_fill_zero = 4;
                $number_prefix .= substr((date('Y') + 543) . date('md'), 2);
                break;
        }
        // $number = \Modules\Core\Entities\Transaction::currentSite()->whereType($document_type)->where($number_field, 'LIKE', $number_prefix . '%')->max($number_field);
        // if ($number) {
        //     $number += 1;
        // } else {
        //     $number_postfix = \Modules\Core\Entities\Transaction::currentSite()->whereType($document_type)->where($number_field, 'LIKE', $number_prefix . '%')->count() + 1;
        //     $number = $number_prefix . str_pad($number_postfix, $sequential_fill_zero, 0, STR_PAD_LEFT);
        // }
        // return $number . " "; // make string
    }
}

if (!function_exists('_paypal_to_order_array')) {
    function _paypal_to_order_array($order_type = 'package', $purchase_units = [], $payment_detail = [], $package_detail = [])
    {
        $payment_method = 'paypal';

        _make_order_array($payment_method,
            $order_type,
            [
                'shipping_fullname' => Arr::get($purchase_units, "shipping.name.full_name"),
                'billing_fullname' => Arr::get($purchase_units, "shipping.name.full_name"),
                'items' => Arr::get($purchase_units, "items", []),

                'title' => $package_detail['name'],
                'body' => nl2br($package_detail['text']),
                'price' => Arr::get($purchase_units, "unit_amount.value"),
            ], [
                'first_name' => Arr::get($payment_detail, "payer.name.given_name"),
                'last_name' => Arr::get($payment_detail, "payer.name.surname"),
                'email' => Arr::get($payment_detail, "payer.email_address"),
            ]);

        return compact('order', 'orderitems');
    }
}

if (!function_exists('_paypal_to_transaction_array')) {
    function _paypal_to_transaction_array($purchase_units = [], $payment_detail = [], $action = null, $doc_type = null)
    {
        $payment_method = 'paypal';

        return _make_transaction_array($payment_method,
            [
                'order_id' => Arr::get($purchase_units, "order_id"),
                'name' => Arr::get($purchase_units, "items.0.name"),
                'descriptions' => Arr::get($purchase_units, "items.0.description"),
                'amount' => Arr::get($purchase_units, "items.0.unit_amount.value"),
            ],
            $payment_detail, null, $action, $doc_type);
    }
}

if (!function_exists('_make_order_array')) {
    function _make_order_array($payment_method = 'coin', $order_type = 'package', $purchase_units = [], $payment_detail = [])
    {
        $order = [
            'user_id' => auth()->id(), // customer user id
            'type' => $order_type,
            'payment_method' => $payment_method,
            // 'order_no' => _generate_order_number($order_type, 1),
            'order_date' => date("Y-m-d H:i:s"),
            'first_name' => Arr::get($payment_detail, "first_name"),
            'last_name' => Arr::get($payment_detail, "last_name"),
            'email' => Arr::get($payment_detail, "email"),
            'shipping_fullname' => Arr::get($purchase_units, "shipping_fullname"),
            'billing_fullname' => Arr::get($purchase_units, "billing_fullname"),
        ];

        $orderitems = [];
        foreach (Arr::get($purchase_units, "items", []) as $item) {
            $orderitems[] = [
//                'title' => $package_detail ? $package_detail['name'] : $item['name'],
//                'body' => $package_detail ? nl2br($package_detail['text']) : $item['description'],
                'title' => Arr::get($purchase_units, "title", $item['title'] ?? $item['name']),
                'body' => Arr::get($purchase_units, "body", $item['body'] ?? $item['description']),
                'sku' => $item['sku'],
                'quantity' => $item['quantity'],
                'price' => Arr::get($purchase_units, "price", $item['price']),
            ];
        }
        return compact('order', 'orderitems');
    }
}

if (!function_exists('_make_transaction_array')) {
    function _make_transaction_array($payment_method = 'coin', $purchase_units = [], $payment_detail1 = [], $payment_detail2 = null, $action = null, $doc_type = null)
    {
        $transactionitem = [
            'user_id' => auth()->id(),
            'order_id' => Arr::get($purchase_units, "order_id", null),
            'billing_date' => date("Y-m-d H:i:s"),
            'action' => $action,
            'name' => Arr::get($purchase_units, "name"),
            'descriptions' => Arr::get($purchase_units, "descriptions"),
            'amount' => Arr::get($purchase_units, "amount"),
        ];
        if (!$doc_type || $doc_type === 'invoice') {
            $invoice = array_merge($transactionitem, [
                'type' => 'invoice',
                'billing_no' => _generate_billing_number('invoice', 1),
                'payment_due' => date("Y-m-d H:i:s", strtotime("+14 day")),
            ]);
            if ($payment_method == 'coin')
                $invoice = array_merge($invoice, [
                    'payment_period' => 1, // 1 (full-paid) | 1/3 (installment)
                    'payment_method' => $payment_method,
                    'payment_date' => date("Y-m-d H:i:s"),
                ]);
        }
        if (!$doc_type || $doc_type === 'receipt') {
            $receipt = array_merge($transactionitem, [
                'type' => 'receipt',
                'billing_no' => _generate_billing_number('receipt', 1),
                'payment_period' => 1, // 1 (full-paid) | 1/3 (installment)
                'payment_method' => $payment_method,
                'payment_date' => date("Y-m-d H:i:s"),
                'payment_detail1' => $payment_detail1, // array, returned from payment-services (paypal, strip)
                'payment_detail2' => $payment_detail2, // text, user add manually from backend
            ]);
        }
        return compact('invoice', 'receipt');
    }
}