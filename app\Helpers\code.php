<?php

use Illuminate\Support\Arr;

if (!function_exists('_disable_error_handler')) {
    function _disable_error_handler()
    {
        $screamState = ini_get('xdebug.scream');
        ini_set('xdebug.scream', 0);
        set_error_handler(function () {
            return false;
        }, 0);
        return $screamState;
    }
}

if (!function_exists('_restore_error_handler')) {
    function _restore_error_handler($screamState)
    {
        restore_error_handler();
        ini_set('xdebug.scream', $screamState);
    }
}

if (!function_exists('_base64_url_encode')) {
    function _base64_url_encode($input)
    {
        return strtr(base64_encode($input), '+/=', '-_!');
    }
}

if (!function_exists('_base64_url_decode')) {
    function _base64_url_decode($input)
    {
        return base64_decode(strtr($input, '-_!', '+/='));
    }
}

if (!function_exists('_encrypt')) {
    function _encrypt($str, $pass = false, $base64 = true)
    {
        $pass = str_split(str_pad('', strlen($str), ($pass ? $pass : config('app.key')), STR_PAD_RIGHT));
        $stra = str_split($str);
        foreach ($stra as $k => $v) {
            $tmp = ord($v) + ord($pass[$k]);
            $stra[$k] = chr($tmp > 255 ? ($tmp - 256) : $tmp);
        }
        $encrypted = join('', $stra);
        if ($base64)
            $encrypted = _base64_url_encode($encrypted);
        return $encrypted;
    }
}
if (!function_exists('_decrypt')) {
    function _decrypt($str, $pass = false, $base64 = true)
    {
        if ($base64)
            $str = _base64_url_decode($str);
        $pass = str_split(str_pad('', strlen($str), ($pass ? $pass : config('app.key')), STR_PAD_RIGHT));
        $stra = str_split($str);
        foreach ($stra as $k => $v) {
            $tmp = ord($v) - ord($pass[$k]);
            $stra[$k] = chr($tmp < 0 ? ($tmp + 256) : $tmp);
        }
        return join('', $stra);
    }
}

if (!function_exists('_compress_html')) {

    function _compress_html($buffer)
    {

        return preg_replace([
            '#\s>#s',
            '/\>[^\S ]+/s',     // strip whitespaces after tags, except space
            '/[^\S ]+\</s',     // strip whitespaces before tags, except space
            '/(\s)+/s',         // shorten multiple whitespace sequences
            '/<!--(.|\s)*?-->/', // Remove HTML comments
        ], [
            '>',
            '>',
            '<',
            '\\1',
            '',
        ], $buffer);
    }
}
if (!function_exists('_compress_script')) {

    function _compress_script($buffer, $smallest = false)
    {
        // JavaScript compressor by John Elliot <<EMAIL>>
        $replace = array(
            '#\'([^\n\']*?)/\*([^\n\']*)\'#' => "'\1/'+\'\'+'*\2'", // remove comments from ' strings
            '#\"([^\n\"]*?)/\*([^\n\"]*)\"#' => '"\1/"+\'\'+"*\2"', // remove comments from " strings
            '#/\*.*?\*/#s' => "",      // strip C style comments
            '#[\r\n]+#' => "\n",    // remove blank lines and \r's
            '#\n([ \t]*//.*?\n)*#s' => "\n",    // strip line comments (whole line only)
            '#([^\\])//([^\'"\n]*)\n#s' => "\\1\n",
            // strip line comments
            // (that aren't possibly in strings or regex's)
            '#\n\s+#' => "\n",    // strip excess whitespace
            '#\s+\n#' => "\n",    // strip excess whitespace
            '#(//[^\n]*\n)#s' => "\\1\n", // extra line feed after any comments left
            // (important given later replacements)
            '#/([\'"])\+\'\'\+([\'"])\*#' => "/*" // restore comments in strings
        );

        $search = Arr::keys($replace);
        $script = preg_replace($search, $replace, $buffer);

        $replace = array(
            "&&\n" => "&&",
            "||\n" => "||",
            "(\n" => "(",
            ")\n" => ")",
            "[\n" => "[",
            "]\n" => "]",
            "+\n" => "+",
            ",\n" => ",",
            "?\n" => "?",
            ":\n" => ":",
            ";\n" => ";",
            "{\n" => "{",
            // "}\n" => "}", // (because I forget to put semicolons after function assignments)
            "\n]" => "]",
            "\n)" => ")",
            "\n}" => "}",
            "\n\n" => "\n"
        );

        if ($smallest)
            $replace["}\n"] = "}";

        $search = Arr::keys($replace);
        $script = str_replace($search, $replace, $script);

        return trim($script);

    }

}
if (!function_exists('_fill_absolute_url')) {

    function _fill_absolute_url($html, $base_url = null, $base_asset = null)
    {
        $base_asset = $base_asset??_asset("/");
        $base_url = $base_url??_url("/");
        return preg_replace([
            '#src=([\'"])/#',
            '#href=([\'"])/#'
        ], [
            'src=\\1' . $base_asset,
            'href=\\1' . $base_url
        ], $html);
    }

}

if (!function_exists('_xml_entity_decode')) {
    function _xml_entity_decode($s)
    {
        // illustrating how a (hypothetical) PHP-build-in-function MUST work
        static $XENTITIES = array('&amp;', '&gt;', '&lt;');
        static $XSAFENTITIES = array('#_x_amp#;', '#_x_gt#;', '#_x_lt#;');
        $s = str_replace($XENTITIES, $XSAFENTITIES, $s);
        $s = html_entity_decode($s, ENT_HTML5 | ENT_NOQUOTES, 'UTF-8'); // PHP 5.3+
        $s = str_replace($XSAFENTITIES, $XENTITIES, $s);
        return $s;
    }
}

/**
 * Validates the signature used in a signed request.
 *
 * @param string $knownString
 * @param string $userString
 *
 * @return bool
 */
if (!function_exists('_hash_equals')) {
    function _hash_equals($knownString, $userString)
    {
        if (function_exists('mb_strlen')) {
            $kLen = mb_strlen($knownString, '8bit');
            $uLen = mb_strlen($userString, '8bit');
        } else {
            $kLen = strlen($knownString);
            $uLen = strlen($userString);
        }
        if ($kLen !== $uLen) {
            return false;
        }
        $result = 0;
        for ($i = 0; $i < $kLen; $i++) {
            $result |= (ord($knownString[$i]) ^ ord($userString[$i]));
        }

        // They are only identical strings if $result is exactly 0...
        return 0 === $result;
    }
}


/**
 * Create the signature used in a signed request.
 *
 * @param string $encodedData
 * @param string $secret
 * @param bool $rawOutput
 *
 * @return string
 */
if (!function_exists('_hash_signature')) {
    function _hash_signature($encodedData, $secret, $rawOutput = true)
    {
        $hashedSig = hash_hmac(
            'sha256',
            $encodedData,
            $secret,
            $rawOutput
        );

        if (!$hashedSig) {
            throw new \Exception('Unable to hash signature from encoded payload data.', 602);
        }

        return $hashedSig;
    }
}

