{"name": "idavoll/core-module", "type": "asgard-module", "description": "The core module for AsgardCMS. This is required for every install.", "keywords": ["asgardcms", "core"], "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "support": {"email": "<EMAIL>", "issues": "https://github.com/AsgardCms/Platform/issues", "source": "https://github.com/AsgardCms/Core"}, "suggest": {"asgardcms/notification-module": "Allows notifications to be sent to the user. Optionally real-time notifications."}, "autoload-dev": {"psr-4": {"Modules\\Core\\": ".", "Modules\\": "Mo<PERSON>les/"}}, "extra": {"branch-alias": {"dev-master": "4.0.x-dev"}}, "minimum-stability": "dev", "prefer-stable": true}