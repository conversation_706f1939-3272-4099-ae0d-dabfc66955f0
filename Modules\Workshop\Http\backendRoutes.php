<?php

use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Storage;

/** @var Router $router */
$router->group(
    ['prefix' => '/workshop'],
    function (Router $router) {
        $router->get('modules', [
            'as' => 'admin.workshop.modules.index',
            'uses' => 'ModulesController@index',
            'middleware' => 'can:workshop.modules.index',
        ]);
        $router->get('modules/{module}', [
            'as' => 'admin.workshop.modules.show',
            'uses' => 'ModulesController@show',
            'middleware' => 'can:workshop.modules.show',
        ]);
        $router->post('modules/update', [
            'as' => 'admin.workshop.modules.update',
            'uses' => 'ModulesController@update',
            'middleware' => 'can:workshop.modules.update',
        ]);
        $router->post('modules/disable/{module}', [
            'as' => 'admin.workshop.modules.disable',
            'uses' => 'ModulesController@disable',
            'middleware' => 'can:workshop.modules.disable',
        ]);
        $router->post('modules/enable/{module}', [
            'as' => 'admin.workshop.modules.enable',
            'uses' => 'ModulesController@enable',
            'middleware' => 'can:workshop.modules.enable',
        ]);

        $router->get('themes', [
            'as' => 'admin.workshop.themes.index',
            'uses' => 'ThemesController@index',
            'middleware' => 'can:workshop.themes.index',
        ]);
        $router->get('themes/{theme}', [
            'as' => 'admin.workshop.themes.show',
            'uses' => 'ThemesController@show',
            'middleware' => 'can:workshop.themes.show',
        ]);

        $router->any('tools/base64topdf', function(){
            $url = "https://www.macmillaneducationeverywhere.com/projects/course-levels/resources/getDownloadData";

            if(request()->has('c')){
                $r = _connect_curl($ch, $url, [], [
                    'cookies' => trim(request()->c)
                ]);
                // $d = trim($r, "\r\n");
                // $d = trim($d);
                $d = base64_decode($r);

                ob_end_clean();  // fix corrupted excel 
                ob_start();

                return response($d)
                ->header('Cache-Control', 'no-cache private')
                ->header('Content-Description', 'File Transfer')
                ->header('Content-Type', 'application/pdf')
                ->header('Content-length', strlen($d))
                ->header('Content-Disposition', 'attachment; filename=base64.pdf')
                ->header('Content-Transfer-Encoding', 'binary');  
            } 
            return view("workshop::admin.tools.base64topdf", compact('url'));

        })->name('admin.workshop.tools.base64topdf');
    }
);
