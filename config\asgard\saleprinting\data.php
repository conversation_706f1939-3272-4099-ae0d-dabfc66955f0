<?php
return [
    'long_month' => [1 => 'มกราคม', 'กุมภาพันธ์', 'มีนาคม', 'เมษายน', 'พฤษภาคม', 'มิถุนายน', 'กรกฎาคม', 'สิงหาคม', 'กันยายน', 'ตุลาคม', 'พฤศจิกายน', 'ธันวาคม'],
    'short_month' => [1 => 'ม.ค.', 'ก.พ.', 'มี.ค.', 'เม.ย.', 'พ.ค.', 'มิ.ย.', 'ก.ค.', 'ส.ค.', 'ก.ย.', 'ต.ค.', 'พ.ย.', 'ธ.ค.'],
 
    "unit" =>  [
        0 => "เล่ม", 1 => "กล่อง", 2 => "บาท", 3 => "ชุด", 4 => "เครื่อง", 5 => "License", 6 => "Pcs."
    ],
    "warehouse" =>     [
        0 => "Main Warehouse",
        1 => "01 คลังแบบเรียนเสริม",
        2 => "02 คลังแบบเรียนหลัก",
        6 => "06 คลังแบบพิมพ์",
        8 => "08 คลังสินค้าพิเศษ",
        13 => "13 คลังสินค้าชำรุด",
        14 => "14 Shop E-commerce",
        15 => "15 คลังสินค้า ตชด.",
        17 => "17 คลังค้าปลีก",
        21 => "19 คลังสินค้าเฉพาะกิจ",
    ],
    "branch" => [
        0 => "สำนักงานใหญ่", 1 => "ศึกษาภัณฑ์พาณิชย์ลาดพร้าว", 2 => "ศึกษาภัณฑ์พาณิชย์ สกสค.", 4 => "ศึกษาภัณฑ์พาณิชย์อ้อมน้อย", 5 => "ศึกษาภัณฑ์พาณิชย์อิมพิเรียลเวิลด์สำโรง", 6 => "ศึกษาภัณฑ์พาณิชย์ราชบพิธ", 8 => "ศึกษาภัณฑ์พาณิชย์สนามกีฬา", 9 => "ศึกษาภัณฑ์พาณิชย์สตรีท", 10 => "ศึกษาภัณฑ์พาณิชย์ท้องฟ้าจำลอง"
    ],

    "group_keyword" => [
        // "อื่นๆ",
        "การงานอาชีพ" => ["อาชีพ"],
        "คณิตศาสตร์" => ["คณิต"],
        "ภาษต่างประเทศ" => ["ภาษต่างประเทศ", "ภาษาศาสตร์"],
        "ภาษาอังกฤษ" => ["อังกฤษ"],
        "ภาษาไทย" => ["ไทย"],
        "วิทยาศาสตร์" => ["วิทยาศาสตร์", "วิทย์"],
        "ศิลป" => ["ศิลป"],
        "สังคมศึกษา" => ["สังคม"],
        "สุขศึกษาและพลศึกษา" => ["สุขศึกษา", "พลศึกษา"],
    ],

    'customer_demand' => [ 
        1 => [
            'label' => "อบรม CEFR",
            'class' => 'info',
            'color' => '#17a2b8',
            'code' => 'training',
            'unit' => 'License',
            'value' => 1,
        ],
        2 => [
            'label' => "D-Book",
            'class' => 'teal',
            'color' => '#20c997',
            'code' => 'dbook',
            'unit' => 'License',
            'value' => 2,
        ],
        3 => [
            'label' => "คู่มือ & แผนการสอน",
            'class' => 'warning',
            'color' => '#ffbc58',
            'code' => 'manual',
            'unit' => 'ชุด',
            'value' => 3,
        ],
        4 => [
            'label' => "Teacher Club",
            'class' => 'orange',
            'color' => '#fd7e14',
            'code' => 'teacherclub',
            'unit' => 'Member',
            'value' => 4,
        ],
        5 => [
            'label' => "Apple iPad",
            'class' => 'gray-dark',
            'color' => '#343a40',
            'code' => 'ipad',
            'unit' => 'เครื่อง',
            'value' => 5,
        ],
        6 => [
            'label' => "เงินสนับสนุน",
            'class' => 'success',
            'color' => '#44ab5c',
            'code' => 'financial',
            'unit' => 'บาท',
            'value' => 6,
        ],
        7 => [
            'label' => "กระดาน Active Board",
            'class' => 'purple',
            'color' => '#6f42c1',
            'code' => 'activeboard',
            'unit' => 'ชุด',
            'value' => 7,
        ],
        0 => [
            'label' => "อื่นๆ",
            'class' => 'gray',
            'color' => '#6c757d',
            'code' => 'other',
            'value' => 0,
        ]
    ],
    'customer_type' => [ 
        1 => [
            'label' => "สถานศึกษา",
            'class' => 'info',
            'color' => '#17a2b8',
            'code' => 'school',
            'value' => 1,
        ],
        2 => [
            'label' => "ร้านค้าตัวแทน",
            'class' => 'teal',
            'color' => '#20c997',
            'code' => 'dealer',
            'value' => 2,
        ],
        3 => [
            'label' => "ร้านค้าทั่วไป",
            'class' => 'warning',
            'color' => '#ffbc58',
            'code' => 'shop',
            'value' => 3,
        ],
        0 => [
            'label' => "หน่วยงานอื่นๆ",
            'class' => 'gray',
            'color' => '#6c757d',
            'code' => 'other',
            'value' => 0,
        ]
    ],
    'presentation' => [ 
        1 => [
            'label' => "โทรศัพท์",
            'class' => 'info',
            'color' => '#17a2b8',
            'code' => 'phone',
            'value' => 1,
        ],  
        2 => [
            'label' => "Line",
            'class' => 'teal',
            'color' => '#20c997',
            'code' => 'line',
            'value' => 2,
        ],   
        3 => [
            'label' => "Email",
            'class' => 'warning',
            'color' => '#ffbc58',
            'code' => 'email',
            'value' => 3,
        ],  
        4 => [
            'label' => "เข้าพบ",
            'class' => 'primary',
            'color' => '#ff8084',
            'code' => 'meet',
            'value' => 4,
        ], 
        0 => [
            'label' => "อื่นๆ",
            'class' => 'gray',
            'color' => '#6c757d',
            'code' => 'other',
            'value' => 0,
        ]
    ],
    'results' => [
        -1 => [
            'label' => 'ทั้งหมด',
            'class' => 'primary',
            'color' => '#ff8084',
            'code' => 'all',
            'value' => -1,
        ],
        3 => [
            'label' => 'ตกลงซื้อ',
            'class' => 'success',
            'color' => '#44ab5c',
            'code' => 'success',
            'value' => 3,
        ],
        2 => [
            'label' => 'รอตัดสินใจ',
            'class' => 'warning',
            'color' => '#ffbc58',
            'code' => 'wait',
            'value' => 2,
        ],
        4 => [
            'label' => 'นัดหมายใหม่',
            'class' => 'info',
            'color' => '#17a2b8',
            'code' => 'appointment',
            'value' => 4,
        ],
        1 => [
            'label' => 'ไม่สนใจ',
            'class' => 'danger',
            'color' => '#dc3545',
            'code' => 'reject',
            'value' => 1,
        ],
        0 => [
            'label' => 'อื่นๆ',
            'class' => 'gray',
            'color' => '#6c757d',
            'code' => 'other',
            'value' => 0,
        ],
    ],
    'result_cond' => [
        "" => [
            'label' => 'ทั้งหมด',
            'class' => 'primary',
            'color' => '#ff8084',
            'code' => 'all',
            'value' => "",
        ], 
        1 => [
            'label' => 'มีเงื่อนไขเพิ่มเติม',
            'class' => 'danger',
            'color' => '#dc3545',
            'code' => 'yes',
            'value' => 1,
        ],
        0 => [
            'label' => 'ไม่มีเงื่อนไขเพิ่มเติม',
            'class' => 'gray',
            'color' => '#6c757d',
            'code' => 'no',
            'value' => 0,
        ],
    ],
    'department' => [
        41 => [
            "name" => "ฝ่ายการผลิต",
            "class" => "secondary",
            "value" => 41,
            "catalog" => [
                // "qr" => "QR Catalog-ฝ่ายค้าส่ง.png",
                // "link" => "https://suksapanpanit.com/crm/extra/catalogs/2021-pro/"
            ],
            "section" => [
                4101 => [
                    "name" => "ส่วนเอกสารการผลิต",
                    "codename" => "สอผ.",
                    "value" => 4101,
                ],
                4102 => [
                    "name" => "ส่วนวางแผนการผลิต",
                    "codename" => "สวผ.",
                    "value" => 4102,
                ]
            ], 
            "teams" => [ 
            ]

        ], 42 =>  [
            "name" => "ฝ่ายเตรียมการผลิต",
            "class" => "warning",
            "value" => 42,
            "catalog" => [
                // "qr" => "QR Catalog-ฝ่ายค้าปลีก.png",
                // "link" => "https://suksapanpanit.com/crm/extra/catalogs/2021/"
            ],
            "section" => [
                4201 => [
                    "name" => "ส่วนออกแบบสื่อสิ่งพิมพ์ 1",
                    "codename" => "สอพ.1",
                    "value" => 4201,
                ],
                4202 => [
                    "name" => "ส่วนออกแบบสื่อสิ่งพิมพ์ 2",
                    "codename" => "สอพ.2",
                    "value" => 4202,
                ]
            ],
            "teams" => [ 
            ]
        ], 43 =>  [
            "name" => "ฝ่ายงานวิชาการ",
            "class" => "warning",
            "value" => 43,
            "catalog" => [
                // "qr" => "QR Catalog-ฝ่ายค้าปลีก.png",
                // "link" => "https://suksapanpanit.com/crm/extra/catalogs/2021/"
            ],
            "section" => [
                4301 => [
                    "name" => "ส่วนงานวิชาการ",
                    "codename" => "สวช",
                    "value" => 4301,
                ], 
            ],
            "teams" => [ 
            ]
        ]
    ],

    'area_type' => [ 
        1 => [
            'title' => 'สำนักงานเขตพื้นที่การศึกษาประถมศึกษา',
            'label' => 'เขตพื้นที่การศึกษาประถมศึกษา',
            'alias' => 'สพป.',
            'value' => 'สพป',
            'code' => 'spp',
        ],
        2 => [
            'title' => 'สำนักงานเขตพื้นที่การศึกษามัธยมศึกษา',
            'label' => 'เขตพื้นที่การศึกษามัธยมศึกษา',
            'alias' => 'สพม.',
            'value' => 'สพม',
            'code' => 'spm',
        ],
        // 3 => [
        //     'title' => 'สำนักบริหารงานการศึกษาพิเศษ',
        //     'label' => 'สำนักบริหารงานการศึกษาพิเศษ',
        //     'alias' => 'สศศ.',
        //     'value' => 'สศศ',
        //     'code' => 'sss',
        // ],
        4 => [
            'title' => 'ไม่ระบุเขตพื้นที่การศึกษา',
            'label' => 'ไม่ระบุเขตพื้นที่การศึกษา',
            'alias' => 'ไม่ระบุ',
            'value' => 'ไม่ระบุ',
            'code' => 'none',
        ]
    ],

    // add more types, need to add create's form and printing as well
    'certificate_types' => [
        '11' => [
            'code' => '11',
            'label' => 'ปพ.1 : ป',
            'active' => true,
            'color' => 'navy',
            'prefix0' => 'ประถมศึกษา',
            'prefix1' => 'ประถมศึกษา',
            'prefix2' => 'ป.',
            'unit1' => 'เล่ม',
            'unit2' => 'ล.',
            'title' => 'ระเบียนแสดงผลการเรียนหลักสูตรแกนกลางการศึกษาขั้นพื้นฐาน ระดับประถมศึกษา',
            'style' => 'size: A4 portrait; margin: 10%;',
            'paper_width' => '1240',
            'paper_height' => '1754',
        ],
        '12' => [
            'code' => '12',
            'label' => 'ปพ.1 : บ',
            'active' => true,
            'asset' => '11', // use printing and creating template as type 11
            'color' => 'teal',
            'prefix0' => 'มัธยมศึกษาตอนต้น',
            'prefix1' => 'มัธยมศึกษา',
            'prefix2' => 'ม.', // ตอนต้น (การศึกษาภาคบังคับ) 
            'unit1' => 'เล่ม',
            'unit2' => 'ล.',
            'title' => 'ระเบียนแสดงผลการเรียนหลักสูตรแกนกลางการศึกษาขั้นพื้นฐาน ระดับมัธยมศึกษาตอนต้น',
            'style' => 'size: A4 portrait; margin: 10%;',
            'paper_width' => '1240',
            'paper_height' => '1754',
        ],
        '13' => [
            'code' => '13',
            'label' => 'ปพ.1 : พ',
            'active' => true,
            'asset' => '11', // use printing and creating template as type 11
            'color' => 'blue',
            'prefix0' => 'มัธยมศึกษาตอนปลาย',
            'prefix1' => 'มัธยมศึกษา',
            'prefix2' => 'ม.', // ตอนปลาย (การศึกษาขั้นพื้นฐาน)
            'unit1' => 'เล่ม',
            'unit2' => 'ล.',
            'title' => 'ระเบียนแสดงผลการเรียนหลักสูตรแกนกลางการศึกษาขั้นพื้นฐาน ระดับมัธยมศึกษาตอนปลาย',
            'style' => 'size: A4 portrait; margin: 10%;',
            'paper_width' => '1240',
            'paper_height' => '1754',
        ],
        '21' => [
            'code' => '21',
            'label' => 'ปพ.2 : บ',
            'active' => true,
            'color' => 'orange',
            'prefix0' => 'มัธยมศึกษาตอนต้น',
            'prefix1' => 'มัธยมศึกษา',
            'prefix2' => 'ม.', // ตอนต้น (การศึกษาภาคบังคับ) 
            'unit1' => 'แผ่น',
            'unit2' => 'ผ.',
            'title' => 'ใบประกาศนียบัตร ระดับมัธยมศึกษาตอนต้น',
            'style' => 'size: A5 landscape; margin: 10%;',
            'paper_width' => '1240',
            'paper_height' => '874',

        ],
        '22' => [
            'code' => '22',
            'label' => 'ปพ.2 : พ',
            'active' => true,
            'color' => 'maroon',
            'prefix0' => 'มัธยมศึกษาตอนปลาย',
            'prefix1' => 'มัธยมศึกษา',
            'prefix2' => 'ม.', // ตอนปลาย (การศึกษาขั้นพื้นฐาน)
            'unit1' => 'แผ่น',
            'unit2' => 'ผ.',
            'title' => 'ใบประกาศนียบัตร ระดับมัธยมศึกษาตอนปลาย',
            'style' => 'size: A5 landscape; margin: 10%;',
            'paper_width' => '1240',
            'paper_height' => '874',

        ],
        '31' => [
            'code' => '31',
            'label' => 'ปพ.3 : ป',
            'active' => false,
            'color' => '',
            'prefix0' => 'ประถมศึกษา',
            'prefix1' => 'ประถมศึกษา',
            'prefix2' => 'ป.',
            'unit1' => 'แผ่น',
            'unit2' => 'ผ.',
            'title' => 'แบบรายงานผู้สำเร็จการศึกษา ระดับประถมศึกษา',
            'style' => 'size: A5 landscape; margin: 10%;',
            'paper_width' => '1240',
            'paper_height' => '874',
        ],
        '32' => [
            'code' => '32',
            'label' => 'ปพ.3 : บ',
            'active' => false,
            'color' => '',
            'prefix0' => 'มัธยมศึกษาตอนต้น',
            'prefix1' => 'มัธยมศึกษา',
            'prefix2' => 'ม.', // ตอนต้น (การศึกษาภาคบังคับ) 
            'unit1' => 'แผ่น',
            'unit2' => 'ผ.',
            'title' => 'แบบรายงานผู้สำเร็จการศึกษา ระดับมัธยมศึกษาตอนต้น',
            'style' => 'size: A5 landscape; margin: 10%;',
            'paper_width' => '1240',
            'paper_height' => '874',
        ],
        '33' => [
            'code' => '33',
            'label' => 'ปพ.3 : พ',
            'active' => false,
            'color' => '',
            'prefix0' => 'มัธยมศึกษาตอนปลาย',
            'prefix1' => 'มัธยมศึกษา',
            'prefix2' => 'ม.', // ตอนปลาย (การศึกษาขั้นพื้นฐาน)
            'unit1' => 'แผ่น',
            'unit2' => 'ผ.',
            'title' => 'แบบรายงานผู้สำเร็จการศึกษา ระดับมัธยมศึกษาตอนปลาย',
            'style' => 'size: A5 landscape; margin: 10%;',
            'paper_width' => '1240',
            'paper_height' => '874',
        ],
    ],
];
