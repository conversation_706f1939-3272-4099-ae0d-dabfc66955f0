<?php

use Illuminate\Support\Arr;

if (!function_exists('_scan_image')) {
    function _scan_image($dir, $respond = 'json')
    {
        $root = public_path();
        $img = [];
        if (!is_null($dir) && $dir != '')
            foreach (scandir($root . '/' .  $dir) as $k => $file) {
                if (!is_dir($file) && !in_array($file, ['.', '..'])) {
                    $file_ext = strtolower(substr(strrchr($file, '.'), 1));
                    $ext_img = array('jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff', 'svg');
                    if (in_array($file_ext, $ext_img))
                        $img[] = $file;
                }
            }
        if ($respond != 'array')
            return response()->json(['img' => $img, 'path' => $dir]);
        return $img;
    }
}

if (!function_exists('_scan_file')) {
    function _scan_file($dir, &$output = [], $callback = '', $recursive = true)
    {
        if (!is_null($dir) && $dir != '' && @file_exists(urldecode($dir))) {
            foreach (scandir($dir) as $k => $inner) {
                $is_dot = in_array($inner, ['.', '..']);
                $full_path = str_replace('\\', '/', $dir . '/' . $inner);
                if ((!is_dir($full_path) || !$recursive) && !$is_dot) {
                    if (is_callable($callback))
                        $callback($output, $full_path);
                    else if (is_array($output))
                        $output[] = $full_path;
                } else if (!$is_dot && $recursive) {
                    _scan_file($full_path, $output, $callback, $recursive);
                }
            }
        }
    }
}
if (!function_exists('_is_script_loaded')) {
    function _is_script_loaded(&$name, $default_name = null)
    {
        global $script_loaded;
        $script_loaded = is_array($script_loaded) ? $script_loaded : [];
        if (!$name || !is_string($name))
            $name = $default_name;
        if (in_array($name, $script_loaded))
            return true;
        $script_loaded[] = $name;
        return false;
    }
}
if (!function_exists('_base64_to_image')) {
    function _base64_to_image($base64_string, $output_file, $type = 'jpg')
    {
        $imageData = @base64_decode(@preg_replace('#^data:image/\w+;base64,#i', '', $base64_string));
        if ($type == 'jpg') {
            $source = @imagecreatefromstring($imageData);
            $rotate = @imagerotate($source, 0, 0); // if want to rotate the image
            $imageSave = @imagepng($rotate, $output_file, 100);
            imagedestroy($source);
            return $imageSave;
        } else if ($type == 'png')
            return @file_put_contents($output_file, $imageData);
    }
}
if (!function_exists('_create_folder')) {
    /**
     * Create directory for images and/or thumbnails
     *
     * @param  string $path
     * @param  string $path_thumbs
     */
    function _create_folder($path = null, $path_thumbs = null, $auto_public_path = true)
    {
        $oldumask = umask(0);
        if ($path) {
            if ($auto_public_path && str_contains($path, public_path()) === false)
                $path = public_path($path);
            if (!@file_exists(urldecode($path)))
                mkdir($path, 0755, true);
        } // or even 01777 so you get the sticky bit set
        if ($path_thumbs) {
            $path_thumbs = public_path($path_thumbs);
            if (!@file_exists(urldecode($path_thumbs)))
                mkdir($path_thumbs, 0755, true) or die("$path_thumbs cannot be found");
        } // or even 01777 so you get the sticky bit set
        umask($oldumask);
        return $path;
    }
}
if (!function_exists('_remove_folder')) {
    /**
     * Remove directory and all file inside it
     *
     * @param  string $dir
     */
    function _remove_folder($dir)
    {
        if (is_dir($dir)) {
            $objects = scandir($dir);
            foreach ($objects as $object) {
                if ($object != "." && $object != "..") {
                    if (filetype($dir . "/" . $object) == "dir") rmdir($dir . "/" . $object);
                    else unlink($dir . "/" . $object);
                }
            }
            reset($objects);
            rmdir($dir);
        }
    }
}

if (!function_exists('_thumbnail')) {
    /**
     * Get thumbnail url of original Image
     *
     * @param  string|Modules\Media\Entities\File $originalImage
     * @param  string $thumbnail (smallThumb/mediumThumb)
     *
     * @return string
     */
    function _thumbnail($originalImage, $thumbnail, $relativeUrl = true)
    {
        if (!(is_string($originalImage) || is_a($originalImage, "Modules\\Media\\Entities\\File")))
            return "";
        $imagy = app(\Modules\Media\Image\Imagy::class);
        $url = $imagy->getThumbnail($originalImage, $thumbnail);
        if ($relativeUrl)
            $url = str_replace(_url("", null, true, false), "/", $url);
        return $url;
    }
}

if (!function_exists('_file')) {
    function _file($file, $return = true)
    {
        $context = stream_context_create([
            "http" => array(
                'header' => 'Connection: close\r\n',
                //                'timeout' => .5
            ),
            "ssl" => array(
                "verify_peer" => false,
                "verify_peer_name" => false,
            )
        ]);
        if ($return)
            return file_get_contents($file, false, $context);

        readfile($file, false, $context);
        exit;
    }
}

if (!function_exists('_readfile')) {
    function _readfile($realpath)
    {
        $context = stream_context_create([
            "http" => array(
                'header' => 'Connection: close\r\n',
                //                    'timeout' => .5
            ),
            "ssl" => array(
                "verify_peer" => false,
                "verify_peer_name" => false,
            )
        ]);
        return readfile($realpath, false, $context);
    }
}

if (!function_exists('_file_get_contents')) {
    function _file_get_contents($realpath)
    {
        $context = stream_context_create([
            "http" => array(
                'header' => 'Connection: close\r\n',
                //                    'timeout' => .5
            ),
            "ssl" => array(
                "verify_peer" => false,
                "verify_peer_name" => false,
            )
        ]);
        return file_get_contents($realpath, false, $context);
    }
}

if (!function_exists('_get_folder_size')) {
    function _get_folder_size($dir)
    {
        $dir = rtrim(str_replace('\\', '/', $dir), '/');

        if (is_dir($dir) === true) {
            $totalSize = 0;
            $os = strtoupper(substr(PHP_OS, 0, 3));
            // If on a Unix Host (Linux, Mac OS)
            if ($os !== 'WIN') {
                $io = popen('/usr/bin/du -sb ' . $dir, 'r');
                if ($io !== false) {
                    $totalSize = intval(fgets($io, 80));
                    pclose($io);
                    return $totalSize;
                }
            }
            // If on a Windows Host (WIN32, WINNT, Windows)
            if ($os === 'WIN' && extension_loaded('com_dotnet')) {
                $obj = new \COM('scripting.filesystemobject');
                if (is_object($obj)) {
                    $ref = $obj->getfolder($dir);
                    $totalSize = $ref->size;
                    $obj = null;
                    return $totalSize;
                }
            }
            // If System calls did't work, use slower PHP 5
            $files = new \RecursiveIteratorIterator(new \RecursiveDirectoryIterator($dir));
            foreach ($files as $file) {
                $totalSize += $file->getSize();
            }
            return $totalSize;
        } else if (is_file($dir) === true) {
            return filesize($dir);
        }
        return 0;
    }
}

if (!function_exists('_correct_image_orientation')) {
    /*
     * Fixing photo orientation on images uploaded via PHP
     *
     * the camera would add a small piece of data to the file, noting which orientation the image should be in. It adds this information to the Exif data that all photos have (which includes the model of camera you took it with, the orientation, and possibly even the GPS location where the photo was taken).
     * The image data is saved in its original, unrotated form, but the Exif tag allows applications to correct it.
     *
     * https://obrienmedia.co.uk/blog/fixing-photo-orientation-on-images-uploaded-via-php-from-ios-devices
     * https://www.howtogeek.com/254830/why-your-photos-dont-always-appear-correctly-rotated/
     * */
    function _correct_image_orientation($filename)
    {
        if (function_exists('exif_read_data')) {
            $exif = exif_read_data($filename);
            if ($exif && isset($exif['Orientation'])) {
                $orientation = $exif['Orientation'];
                if ($orientation != 1) {
                    $img = imagecreatefromjpeg($filename);
                    $deg = 0;
                    switch ($orientation) {
                        case 3:
                            $deg = 180;
                            break;
                        case 6:
                            $deg = 270;
                            break;
                        case 8:
                            $deg = 90;
                            break;
                    }
                    if ($deg) {
                        $img = imagerotate($img, $deg, 0);
                    }
                    // then rewrite the rotated image back to the disk as $filename
                    imagejpeg($img, $filename, 95);
                } // if there is some rotation necessary
            } // if have the exif orientation info
        } // if function exists
    }
}

if (!function_exists('_force_download')) {
    /**
     * Force Download
     *
     * Generates headers that force a download to happen
     * Example usage:
     * force_download( 'screenshot.png', './images/screenshot.png' );
     *
     * @access public
     * @param string $filename
     * @param string $data
     * @return void/bool
     */
    function _force_download($filename = '', $data = '')
    {
        if ($filename == '' || $data == '') {
            return false;
        }

        if (!file_exists($data)) {
            return false;
        }

        // Try to determine if the filename includes a file extension.
        // We need it in order to set the MIME type
        if (false === strpos($filename, '.')) {
            return false;
        }

        // Grab the file extension
        $extension = strtolower(pathinfo(basename($filename), PATHINFO_EXTENSION));

        // our list of mime types
        $mime_types = array(

            'txt' => 'text/plain',
            'htm' => 'text/html',
            'html' => 'text/html',
            'php' => 'text/html',
            'css' => 'text/css',
            'js' => 'application/javascript',
            'json' => 'application/json',
            'xml' => 'application/xml',
            'swf' => 'application/x-shockwave-flash',
            'flv' => 'video/x-flv',

            // images
            'png' => 'image/png',
            'jpe' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'jpg' => 'image/jpeg',
            'gif' => 'image/gif',
            'bmp' => 'image/bmp',
            'ico' => 'image/vnd.microsoft.icon',
            'tiff' => 'image/tiff',
            'tif' => 'image/tiff',
            'svg' => 'image/svg+xml',
            'svgz' => 'image/svg+xml',

            // archives
            'zip' => 'application/zip',
            'rar' => 'application/x-rar-compressed',
            'exe' => 'application/x-msdownload',
            'msi' => 'application/x-msdownload',
            'cab' => 'application/vnd.ms-cab-compressed',

            // audio/video
            'mp3' => 'audio/mpeg',
            'qt' => 'video/quicktime',
            'mov' => 'video/quicktime',

            // adobe
            'pdf' => 'application/pdf',
            'psd' => 'image/vnd.adobe.photoshop',
            'ai' => 'application/postscript',
            'eps' => 'application/postscript',
            'ps' => 'application/postscript',

            // ms office
            'doc' => 'application/msword',
            'rtf' => 'application/rtf',
            'xls' => 'application/vnd.ms-excel',
            'ppt' => 'application/vnd.ms-powerpoint',

            // open office
            'odt' => 'application/vnd.oasis.opendocument.text',
            'ods' => 'application/vnd.oasis.opendocument.spreadsheet',
        );

        // Set a default mime if we can't find it
        if (!isset($mime_types[$extension])) {
            $mime = 'application/octet-stream';
        } else {
            $mime = (is_array($mime_types[$extension])) ? $mime_types[$extension][0] : $mime_types[$extension];
        }

        // Generate the server headers
        if (strstr($_SERVER['HTTP_USER_AGENT'], "MSIE")) {
            header('Content-Type: "' . $mime . '"');
            header('Content-Disposition: attachment; filename="' . $filename . '"');
            header('Expires: 0');
            header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
            header("Content-Transfer-Encoding: binary");
            header('Pragma: public');
            header("Content-Length: " . filesize($data));
        } else {
            header("Pragma: public");
            header("Expires: 0");
            header("Cache-Control: must-revalidate, post-check=0, pre-check=0");
            header("Cache-Control: private", false);
            header("Content-Type: " . $mime, true, 200);
            header('Content-Length: ' . filesize($data));
            header('Content-Disposition: attachment; filename=' . $filename);
            header("Content-Transfer-Encoding: binary");
        }
        readfile($data);
        exit;
    } //End force_download
}

/**
 * Fix Buffering Bug In Laravel Controller
 * if use @ob_end_clean() and show images is still not working
 */
if (!function_exists('_clean_buffer')) {
    function _clean_buffer()
    {
        ini_set('output_buffering', 'off');
        ini_set('zlib.output_compression', false);
        ini_set('implicit_flush', true);
        ob_implicit_flush(true);
        while (ob_get_level() > 0) {
            $level = ob_get_level();
            ob_end_clean();
            if (ob_get_level() == $level) {
                break;
            }
        }
        if (function_exists('apache_setenv')) {
            apache_setenv('no-gzip', '1');
            apache_setenv('dont-vary', '1');
        }
    }
}

if (!function_exists('_show_image')) {
    /*
    *** How To Fix Buffering Bug In Laravel Controller
    *** (make response > clean buffer > return response)
    *** http://simpledeveloper.com/how-to-fix-laravel-response-image-download-in-laravel/
     *
     * Example
        $stream =  \Intervention\Image\Facades\Image::make($url)->stream();
        $stream =  _file_get_contents('test.jpg');
        $stream =  _text_to_image('test', 200, 200);

        return _show_image($stream);
     *
     * */
    function _show_image(&$fileStreamContent, $type = 'png')
    {
        $response = response()->stream(function () use ($fileStreamContent) {
            echo $fileStreamContent;
        }, 200, [
            'Content-Type' => 'image/' . $type,
        ]);
        @ob_end_clean(); // needed to fix broken image problem
        return $response;
    }
}
if (!function_exists('_text_to_image')) {
    function _text_to_image($title, $subtitle = "", $option = [])
    {
        _array_default([
            'width' => '514', 'height' => '269', 'title_color' => '#FFFFFF', 'text_color' => '#EFEFEF', 'bg_color' => '#000000', 'bg_image' => false
        ], $option);


        // Init;
        ob_start();
        $fontpath = public_path('assets/fonts/prompt/Prompt-Regular.ttf');
        $images = ImageCreate($option['width'], $option['height']);
        $imagesBg = null;

        // hex to rgb (#FFFFFF -> 255,255,255)
        list($bg_r, $bg_g, $bg_b) = sscanf($option['bg_color'], "#%02x%02x%02x");
        list($tt_r, $tt_g, $tt_b) = sscanf($option['title_color'], "#%02x%02x%02x");
        list($tx_r, $tx_g, $tx_b) = sscanf($option['text_color'], "#%02x%02x%02x");

        // Layer 1 :: BG Image/Color
        if ($option['bg_image']) {
            $bgpath = public_path($option['bg_image'] ?? 'assets/images/bg/cool-background-D1.png');
            $oruginalBg = imagecreatefrompng($bgpath);
            $imagesBg = ImageCreateTrueColor($option['width'], $option['height']);
            ImageCopyResampled($imagesBg, $oruginalBg, 0, 0, 0, 0, $option['width'] + 1, $option['height'] + 1, ImagesX($oruginalBg), ImagesY($oruginalBg));
        }
        $bg_color = ImageColorAllocate($images, $bg_r, $bg_g, $bg_b);
        if ($imagesBg)
            imagecolortransparent($images, $bg_color);

        // Layer 2 :: Title/Border Color
        $tt_color = ImageColorAllocate($images, $tt_r, $tt_g, $tt_b);
        // ImageRectangle($images, 2, 2, $option['width'] - 2, $option['height'] - 2, $tt_color); // border
        $pos = 70; // start at
        $line_height = 40;
        foreach (Arr::wrap($title) as $txt) {
            imagettftext($images, 20, 0, 25, $pos, $tt_color, $fontpath, trim($txt));
            $pos += $line_height;
        }

        // Layer 3 ::  Sub-Title/Credit Color
        $line_height = 25;
        $tx_color = ImageColorAllocate($images, $tx_r, $tx_g, $tx_b); // text/border color
        foreach (Arr::wrap($subtitle) as $txt) {
            imagettftext($images, 9, 0, 25, $pos, $tt_color, $fontpath, trim($txt));
            $pos += $line_height;
        }
        imagettftext($images, 7, 90, ImagesX($images) - 10, 190, $tx_color, $fontpath, config("istyles.app.published"));

        if ($imagesBg) {
            $opacity = 100;
            imagecopymerge($imagesBg, $images, 0, 0, 0, 0, $option['width'], $option['height'], $opacity);

            // make stream
            ImagePng($imagesBg);
            ImageDestroy($imagesBg);
        } else {
            ImagePng($images);
            ImageDestroy($images);
        }
        return ob_get_clean();
    }
}


if (!function_exists('_vm_to_real_path')) {
    /**
     * @param string $path
     * @return string
     */
    function _vm_to_real_path($path, $allowCrossDomain = false)
    {
        if ($allowCrossDomain) {
            $configVmPattern = config('istyles.media.config.vm-pattern');
            $configVmReplace = config('istyles.media.config.vm-replace');
            $path = preg_replace("/{$configVmPattern}/", $configVmReplace, $path);
        } else {
            $configVmPath = config('istyles.media.config.vm-url');
            $path = config('istyles.media.config.files-path') . str_replace([
                $configVmPath,
                ltrim($configVmPath, '/'),
            ], '', $path);
        }
        if (config('istyles.media.config.filesystem') === 'local') {
            return basename(public_path()) . $path;
        }

        return $path;
    }
}
if (!function_exists('_remove_vm_prefix')) {
    /**
     * @param string $path
     * @return string
     */
    function _remove_vm_prefix($path, $allowCrossDomain = false)
    {
        if ($allowCrossDomain) {
            $configVmPattern = config('istyles.media.config.vm-pattern');
            return preg_replace("/{$configVmPattern}\/files\//", "", $path);
        } else {
            $configAssetPath = config('istyles.media.config.vm-url');
            return str_replace([
                $configAssetPath,
                ltrim($configAssetPath, '/'),
            ], '', $path);
        }
    }
}

if (!function_exists('_escape_filename')) {
    /**
     * @param string $filename
     * @return string
     */
    function _escape_filename($filename)
    { 
        return str_replace(['/','\\',':','*','?','"','<','>','|',' '], "_", $filename);
    }
}