<?php

use App\Http\Requests\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Str;
use Mcamara\LaravelLocalization\Facades\LaravelLocalization;

if (!function_exists('_is_home')) {
    function _is_home()
    {
        return is_null(Request::segment(2)) || Request::segment(2) == '';
    }
}


if (!function_exists('_get_host')) {
    function _get_host($host_url = null)
    {
        $host_key = ['domain', 'subdomain', 'hostname', 'name', 'acronym'];
        $host_reg = '([^\.]+).(([^\.]+).(.+))';
        $host_url = $host_url ? $host_url : Request::server('HTTP_HOST');
//        $s = preg_match('/([\w-]+).(([^.]*).(com|net|co.th|in.th))/', Request::server('HTTP_HOST'), $host_data);
        $p = preg_match("/^preview-{$host_reg}$/", $host_url, $host_data);
        if (!$p)
            $s = preg_match("/^{$host_reg}$/", $host_url, $host_data);
        if (count($host_key) != count($host_data) && _is_dev())
            dd('get_host(): array_combine parameter number not equal', $host_key, $host_data);
        return array_combine($host_key, $host_data);
    }
}
if (!function_exists('_get_subdomain')) {
    function _get_subdomain()
    {
        return request()->_site['subdomain'];
    }
}
if (!function_exists('_get_siteid')) {
    function _get_siteid()
    {
        return request()->_site['id'];
    }
}
if (!function_exists('_get_site_type')) {
    function _get_site_type()
    {
        return Arr::pluck(_config('static.site.type', null, true), 'name', 'code');
    }
}


if (!function_exists('_get_supported_locales')) {
    function _get_supported_locales()
    {
        return LaravelLocalization::getSupportedLocales();
    }
}

if (!function_exists('_check_supported_locales')) {
    function _check_supported_locales($locale)
    {
        return app('laravellocalization')->checkLocaleInSupportedLocales($locale);
    }
}


if (!function_exists('_get_current_locales')) {
    function _get_current_locales()
    {
        return app('laravellocalization')->getCurrentLocale();
    }
}

if (!function_exists('_get_default_locales')) {
    function _get_default_locales()
    {
        return app('laravellocalization')->getDefaultLocale();
    }
}


if (!function_exists('_get_localized_url')) {
    function _get_localized_url($locale = null, $url = '')
    {
        return app('laravellocalization')->getLocalizedURL($locale, $url);
    }
}


if (!function_exists('_get_site_snap_url')) {
    function _get_site_snap_url($site= null)
    {
        $domain = "review.i-styles.co";
        $item_purchase_code = "6b361e21-ae40-444c-9791-547df53adf8e";
        $apiLink = "http://api.prothemes.biz/tweb-screen.php?site={$site}&domain={$domain}&code={$item_purchase_code}";

        return$apiLink;
    }
}
if (!function_exists('_get_page_snap_url')) {
    function _get_page_snap_url($url= null, $viewport = "1440x900", $width = "250")
    {
        $access_key = "23b3f775c3f34adf477a63af886ca3d6";
        $apiLink = "http://api.screenshotlayer.com/api/capture?access_key={$access_key}&url={$url}&viewport={$viewport}&width={$width}";

        return$apiLink;
    }
}

if (!function_exists('_remove_localized_url')) {
    function _remove_localized_url($url = '')
    {
        return str_replace("/" . locale() . "/", '/', $url);
    }
}


if (!function_exists('_trans')) {
    function _trans($key = null, $replace = [], $default = null, $locale = null)
    {
        if (is_null($key)) {
            return app('translator');
        }

        if (Lang::has($key))
            return app('translator')->get($key, $replace, $locale);
        if (!$default)
            return Str::title(str_replace("-", " ", end(explode('.', $key))));
        return $default;
    }
}

