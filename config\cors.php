<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Cross-Origin Resource Sharing (CORS) Configuration
    |--------------------------------------------------------------------------
    |
    | Here you may configure your settings for cross-origin resource sharing
    | or "CORS". This determines what cross-origin operations may execute
    | in web browsers. You are free to adjust these settings as needed.
    |
    | To learn more: https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS
    |
    */

    // 'paths' => ['api/*'],
    'paths' => ['*/api/*','assets/*'],
    // 'paths' => ['*'],

    'allowed_methods' => ['*'],
    // 'allowed_methods' => ['GET','POST','PUT','PATCH','DELETE','OPTIONS'],

    // 'allowed_origins' => [],
    'allowed_origins' => ['*'],
    // 'allowed_origins' => ['https://ssp3.i-styles.co', 'https://d3.demo.com', 'https://www.suksapanpanit.com', 'https://www.suksapanmall.com'],
    // 'allowed_origins' => ['https://*.i-styles.co', 'https://*.demo.com', 'https://*.suksapanpanit.com', 'https://*.suksapanmall.com'],
    
    'allowed_origins_patterns' => ['*.i-styles.co', '*.demo.com', '*.suksapanpanit.com', '*.suksapanmall.com'],
    // 'allowed_origins_patterns' =>  ['*'],

    'allowed_headers' => ['*'],
    // 'allowed_headers' => ['Origin', 'X-Requested-With', 'Content-Type'],

    'exposed_headers' => [],

    'max_age' => 0,

    'supports_credentials' => false,

];
