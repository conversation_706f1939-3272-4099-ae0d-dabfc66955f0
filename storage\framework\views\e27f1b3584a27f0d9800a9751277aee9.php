<?php echo $__env->make('core::datatable', $school_table_option, array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

<?php $__env->startPush('css-stack'); ?>
    <style>
        .paginate_button.first,
        .paginate_button.last {
            display: none !important;
        }

    </style>
<?php $__env->stopPush(); ?>
<?php $__env->startPush('js-stack'); ?>
    <div class="modal fade" id="modal-school-selection" tabindex="-1" role="dialog"
        aria-labelledby="school-selection-title" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="school-selection-title">Select School
                    </h5>
                    <button type="button" class="close" data-dismiss="modal"><span
                            aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                </div>
                <div class="modal-body" style="padding-top: 0;">

                    <div class="row form-group" style="padding: 0 15px 15px 15px;">
                        <h4 class="pull-left float-left mb-2" style="color:#ff8084; margin: 25px 0 0 0"><i
                                class="fa fa-filter"></i> Filter
                        </h4>

                        
                        <div class="col-sm-12" style="border: 3px solid #ff8084; padding: 15px;">
                            <div class="row">
                                <div class="col-sm-4">
                                    <label for="filter_province">จังหวัด</label>
                                    <select id="filter_province" name="filter_province" placeholder="จังหวัด"
                                        class="custom-filters form-control">
                                        <option value="">ทั้งหมด</option>
                                        <?php $__currentLoopData = Arr::get($filters, 'province'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $p => $province): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($p); ?>"><?php echo e($province); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                                <div class="col-sm-4">
                                    <label for="filter_district">อำเภอ/เขต</label>
                                    <select id="filter_district" name="filter_district" placeholder="อำเภอ/เขต" disabled
                                        class="custom-filters form-control">
                                    </select>
                                </div>
                                <div class="col-sm-4">
                                    <label for="filter_subdistrict">ตำบล/แขวง</label>
                                    <select id="filter_subdistrict" name="filter_subdistrict" placeholder="ตำบล/แขวง"
                                        disabled class="custom-filters form-control">
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="table-responsive el-table__header-wrapper">
                                <table class="school-datatable zd-data-table table table-bordered table-hover"
                                    data-ajaxurl="<?php echo e(_route('api.school.selection.indexServerSide')); ?>"
                                    data-columns="<?php echo e(json_encode($school_table_option['columns'])); ?>">
                                    <thead>
                                        <tr>
                                            <?php
                                                $row = Arr::where($school_table_option['columns'], function ($v, $k) {
                                                    return !in_array($v['data'], ['_action_left', '_action_right']);
                                                });
                                            ?>
                                            <?php $__currentLoopData = $row; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $col): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <th class="print-visible"><?php echo e($col['name']); ?></th>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            <?php if($school_table_option['_action_right']): ?>
                                                <th width="100" class="print-invisible" data-sortable="false">
                                                </th>
                                            <?php endif; ?>
                                        </tr>
                                    </thead>
                                </table>
                                <!-- /.box-body -->
                            </div>
                        </div>
                    </div>

                </div>
                <div class="modal-footer" style="justify-content: space-between;">
                    <a href="javascript:void(0)" data-toggle="modal" data-target="#modal-school-create"
                        style="color: #ff8084;">
                        ฉันหาโรงเรียนไม่เจอ ทำอย่างไร?
                    </a>
                    <button type="button" class="btn btn-default btn-flat"
                        data-dismiss="modal"><?php echo e(trans('core::core.button.cancel')); ?></button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade modal-info" id="modal-school-create" tabindex="-1" role="dialog"
        aria-labelledby="school-create-title" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="school-create-title">
                        เพิ่มข้อมูลโรงเรียน
                    </h4>
                    <button type="button" class="close" data-dismiss="modal"><span
                            aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                </div>
                <div class="modal-body">
                    <form action="" id="form-school-create" class="" style=" padding: 20px">

                        <div class="row">
                            <div class='col-sm-12 form-group<?php echo e($errors->has('school_name') ? ' has-error' : ''); ?>'>
                                <?php echo Form::label('school_name', _trans('user::users.form.school_name', [], 'ชื่อโรงเรียน')); ?>

                                <?php echo Form::input('text', 'school_name', old('school_name', $model->school_name), ['class' => 'form-control']); ?>

                                <?php echo $errors->first('school_name', '<span class="help-block">:message</span>'); ?>

                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12 form-group">
                                <label for="school_size">จำนวนนักเรียน</label>
                                <select id="school_size" name="school_size" class="form-control">
                                    <?php $__currentLoopData = Arr::get($selects, 'size'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $p => $size): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($p); ?>"><?php echo e($size); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                            <div class="col-sm-12 form-group">
                                <label for="school_type">โรงเรียนของท่านเปิดสอนในระดับใด</label>
                                <select id="school_type" name="school_type" multiple class="selectize form-control"
                                    style="width: 100%">
                                    <?php $__currentLoopData = Arr::get($selects, 'type'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $p => $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($p); ?>"><?php echo e($type); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                        </div>

                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default btn-flat"
                        data-dismiss="modal"><?php echo e(trans('core::core.button.cancel')); ?></button>
                    <button type="submit" id="btn-school-create"
                        class="btn btn-primary btn-flat pull-left"><?php echo e(trans('core::core.button.create')); ?></button>
                </div>
            </div>
        </div>
    </div>
    
    <script id="script-school-datatable">
        $(document).ready(function() {
            $('#modal-school-selection').on('show.bs.modal', function(event) {
                initDataTable(".school-datatable")
            });
            $(document).on("change", "#filter_province", function() {
                var field = $(this).attr("name"),
                    search = $(this).val();
                $.ajax({
                    url: "<?php echo _route('api.setting.address.getDistrict'); ?>",
                    method: 'post',
                    data: {
                        province: search,
                        select: 'id'
                    },
                    success: function(result) {
                        var option = "<option value=''>ทั้งหมด</option>";
                        for (var i in result.data)
                            option += "<option value='" + i + "'>" + result.data[i] +
                            "</option>";
                        $("#filter_district").removeAttr("disabled").html(option);
                        $("#filter_subdistrict").attr("disabled", "").html("");

                        window.filter[field] = search;
                        window.filter["filter_district"] = "";
                        window.filter["filter_subdistrict"] = "";
                        reloadDataTable();
                    },
                    error: function() {}
                });
            });
            $(document).on("change", "#filter_district", function() {
                var field = $(this).attr("name"),
                    search = $(this).val();
                $.ajax({
                    url: "<?php echo _route('api.setting.address.getSubdistrict'); ?>",
                    method: 'post',
                    data: {
                        district: search,
                        select: 'id'
                    },
                    success: function(result) {
                        var option = "<option value=''>ทั้งหมด</option>";
                        for (var i in result.data)
                            option += "<option value='" + i + "'>" + result.data[i] +
                            "</option>";
                        $("#filter_subdistrict").removeAttr("disabled").html(option);

                        window.filter[field] = search;
                        window.filter["filter_subdistrict"] = "";
                        reloadDataTable();
                    },
                    error: function() {}
                });
            });
            $(document).on("change", "#filter_subdistrict", function() {
                var field = $(this).attr("name"),
                    search = $(this).val();
                window.filter[field] = search;
                reloadDataTable();
            });

            $(document).on("click", "#btn-school-create", function() {
                var t = $(this);
                t.attr("disabled", "");
                $form = $("#form-school-create");

                $("#o_school_region_id").val($("#school_region").val());
                $("#o_school_province_id").val($("#school_province").val());
                $("#o_school_district_id").val($("#school_district").val());
                $("#o_school_subdistrict_id").val($("#school_subdistrict").val());
                $("#o_school_postcode").val($("#school_postcode").val());

                $formdata = JSON.stringify($form.serializeArray()); // $form.serialize();
                $("#o_school_id").val("");
                $("#o_school_no").val($("#school_no").val());
                $("#o_school_type").val($("#school_type").val());
                $("#o_school_size").val($("#school_size").val());
                $("#o_school_name").val($("#school_name").val());
                $("#o_school_info").val($formdata);

                $("#modal-school-create").modal("hide");
                $("#modal-school-selection").modal("hide");

                // $.ajax({
                //     url: "<?php echo _route('api.school.school.store'); ?>",
                //     data: $formdata,
                //     method: 'post',
                //     success: function(data) {
                //         $form[0].reset();
                //         $("#modal-school-create").modal("hide");
                //         reloadDataTable();
                //         t.removeAttr("disabled");
                //     },
                //     error: function() {
                //         t.removeAttr("disabled");
                //     }
                // });
            });


            $(document).on("change", "#school_region", function() {
                var field = $(this).attr("name"),
                    search = $(this).val();
                $.ajax({
                    url: "<?php echo _route('api.setting.address.getProvince'); ?>",
                    method: 'post',
                    data: {
                        region: search,
                        select: 'id'
                    },
                    success: function(result) {
                        var option = "<option value='0'>- เลือกจังหวัด -</option>";
                        for (var i in result.data)
                            option += "<option value='" + i + "'>" + result.data[i] +
                            "</option>";
                        $("#school_province").removeAttr("disabled").html(option);
                        $("#school_district").attr("disabled", "").html("");
                        $("#school_subdistrict").attr("disabled", "").html("");
                    },
                    error: function() {}
                });
            });

            $(document).on("change", "#school_province", function() {
                var field = $(this).attr("name"),
                    search = $(this).val();
                $.ajax({
                    url: "<?php echo _route('api.setting.address.getDistrict'); ?>",
                    method: 'post',
                    data: {
                        province: search,
                        select: 'id'
                    },
                    success: function(result) {
                        var option = "<option value='0'>- เลือกอำเภอ/เขต -</option>";
                        for (var i in result.data)
                            option += "<option value='" + i + "'>" + result.data[i] +
                            "</option>";
                        $("#school_district").removeAttr("disabled").html(option);
                        $("#school_subdistrict").attr("disabled", "").html("");
                    },
                    error: function() {}
                });
            });
            var school_postcode = [];
            $(document).on("change", "#school_district", function() {
                var field = $(this).attr("name"),
                    search = $(this).val();
                $.ajax({
                    url: "<?php echo _route('api.setting.address.getSubdistrict'); ?>",
                    method: 'post',
                    data: {
                        district: search,
                        select: 'id'
                    },
                    success: function(result) {
                        var option = "<option value='0'>- เลือกตำบล/แขวง -</option>";
                        for (var i in result.data)
                            option += "<option value='" + i + "'>" + result.data[i] +
                            "</option>";
                        $("#school_subdistrict").removeAttr("disabled").html(option);
                        school_postcode = result.postcodes;
                    },
                    error: function() {}
                });
            });
            $(document).on("change", "#school_subdistrict", function() {
                var search = $(this).val();
                $("#school_postcode").val(school_postcode[search]);
                // var field = $(this).attr("name"),
                //     search = $(this).val();
            });

        });
    </script>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\Projects\Web\htdocs\ssp4-v11\Modules/School/Resources/views/partials/modal-select-school.blade.php ENDPATH**/ ?>