<?php

use Illuminate\Support\Arr;
use Modules\Core\Support\Uri;
use Modules\User\Entities\UserOAuth;

/*
 * GuzzleHttp Client
 * */

if (!function_exists('_make_request')) {

    /**
     * Send a request to any service
     * http://docs.guzzlephp.org/en/stable/quickstart.html
     *
     * Example
     * $res = _make_request('POST', 'https://pos.i-styles.co/api/dev/test-callback'
     * , ['testquerykey' => 'test-query-value']
     * , ['testformdatakey' => 'test-form-data-value']
     * , ['authorization' => 'Bearer 7c50fd6d-5726-4647-8f88-f53cd614f168']
     * );
     *
     * dd(_json_decode($res));
     *
     * In test-callback
     * return response()->json(['errors' => false, "message" => "request api success", 'data' => _request_all()]);
     *
     * @return string
     */
    function _make_request($method, $requestUrl, $queryParams = [], $formParams = [], $headers = [], $hasFile = false, $debug = false)
    {

        $client = new \GuzzleHttp\Client([
            'verify' => false
            // Base URI is used with relative requests
            // 'base_uri' => "https://pos.i-styles.co",
            // You can set any number of default request options.
            // 'timeout'  => 2.0,
        ]);

        // POST method only
        // use "form_params" option to send a 'application/x-www-form-urlencoded' request
        // or the "multipart" request option to send a 'multipart/form-data' request.
        $bodyType = 'form_params';
        $multipart = null;

        if ($hasFile) {
            $bodyType = 'multipart';
            $multipart = [];

            foreach ($formParams as $name => $contents) {
                $multipart[] = [
                    'name' => $name,
                    'contents' => $contents
                ];
            }
        }

        $response = $client->request($method, $requestUrl, [
            'query' => $queryParams,
            $bodyType => $hasFile ? $multipart : $formParams,
            'headers' => $headers,
        ]);

        $responseContents = $response->getBody()->getContents();
        if ($debug)
            dd($response->getStatusCode(), $response->getReasonPhrase(), $responseContents, json_decode($responseContents));
        return $responseContents;
    }
}

if (!function_exists('_make_google_form_request')) {
    function _make_google_form_request($formid, $queryParams, $debug = false)
    {
        $requestUrl = "https://docs.google.com/forms/d/e/{$formid}/formResponse";
        $client = new \GuzzleHttp\Client(['verify' => false]);
        $response = $client->request('GET', $requestUrl, [
            'query' => $queryParams, 
        ]);
        if ($debug)
            dd($response->getStatusCode(), $response->getReasonPhrase(), $response->getBody()->getContents());
 
        return $response->getStatusCode() == 200;
    }
}

if (!function_exists('_make_api_request')) {
    function _make_api_request($method, $requestUrl, $formParams = [], $apiKey = null, $debug = false)
    {

        $res = _make_request(
            $method,
            $requestUrl,
            [],
            $formParams,
            ['authorization' => 'Bearer ' . ($apiKey ?? _get_api_key())]
        );
        return $debug ? $res : json_decode($res, true);
    }
}

if (!function_exists('_oauth_user_sync')) {
    /*
     * OAuth Services Sync User
     *
     * */
    function _oauth_user_sync($oauth_data = [])
    {
        if (!$oauth_data['oauth_id'])
            return false;

        $oauth_login_query = UserOAuth::where('oauth_id', '=', $oauth_data['oauth_id'])->where('provider', '=', $oauth_data['provider']);
        if (auth()->check()) {
            $oauth_data['user_id'] = auth()->id();
            $oauth_login_query->where('user_id', '=', $oauth_data['user_id']);
        }
        $oauth_login = $oauth_login_query->first();

        if ($oauth_login) {
            $result = UserOAuth::where('oauth_id', '=', $oauth_data['oauth_id'])->where('provider', '=', $oauth_data['provider'])
                ->update(Arr::only($oauth_data, ['display_name', 'email', 'access_token', 'access_expires_at', 'refresh_token', 'refresh_expires_at']));
        } else {
            $result = UserOAuth::create($oauth_data);
        }
        // remove other oauth account in the same provider
        if ($result) UserOAuth::where('user_id', '=', $oauth_data['user_id'])->where('oauth_id', '<>', $oauth_data['oauth_id'])
            ->where('provider', '=', $oauth_data['provider'])->delete();
        return $result;
    }
}

if (!function_exists('_oauth_refresh_token')) {
    /*
     * OAuth Services Refresh Access Token
     *
     * */
    function _oauth_refresh_token($provider, $api_key = null, $debug = false)
    {
        $requestUrl = _route("api.oauth.get-token", ['core', $provider, 'refresh']);
        return _make_api_request('GET', $requestUrl, [], $api_key, $debug);
    }
}

if (!function_exists('_oauth_handler')) {
    /*
     * OAuth Services Handler
     *
     * */
    function _oauth_handler($provider, $action, $param, $extra_data = [], $array_get = false, $api_key = null, $debug = false)
    {
        $requestUrl = _route("api.oauth.handle-action", ['core', $provider, $action]);
        $response = _make_api_request('POST', $requestUrl, $param, $api_key, $debug);

        if ($extra_data['log']) {
            _log_published($provider, $extra_data['target_id'], $extra_data['post_id'], $param, $extra_data['published_items'] ?? Arr::pull($response, 'data'), $response);
            app('log')->info("_oauth_handler Executed (No Queue) :: {$provider}, {$action} ", compact('param', 'response'));
        }

        return $array_get ? Arr::get($response, $array_get) : $response;
    }
}
if (!function_exists('_oauth_dispatcher')) {
    /*
     * OAuth Services Handler
     *
     * */
    function _oauth_dispatcher($provider, $action, $param, $extra_data = [], $queue = 'low', $connection = 'database')
    {
        return \Modules\Core\Jobs\OauthHandler::dispatch($provider, $action, $param, array_merge([
            'api_key' => _get_api_key(), // required, user login on the fly
            'timestamp' => time() // required, prevent caching job
        ], $extra_data))
            ->onConnection($connection)
            ->onQueue($queue);
    }
}


/*
 * CURL
 * */
if (!function_exists('_connect_curl')) {
    function _connect_curl(&$ch, $url, $data = null, $option = [])
    {
        _array_default([
            'show_header' => false,
            'show_body' => true,
            'ca_path' => false,
            'method' => count($data) ? "POST" : "GET",
            'cookies' => '',
        ], $option);
        $cookies_file = storage_path('cookies.txt');
        $uriParts = parse_url($url);
        $header = array();
        $header[0] = "Accept: text/xml,application/xml,application/xhtml+xml,";
        $header[0] .= "text/html;q=0.9,text/plain;q=0.8,image/png,";
        $header[0] .= "application/json,text/javascript,";
        $header[0] .= "*/*;q=0.01";
        $header[] = "Cache-Control: max-age=0";
        //        $header[] = "Expires: " . gmdate('D, d M Y H:i:s \G\M\T', time() + 31536000);
        // $header[] = "Connection: keep-alive";
        $header[] = "Connection: close";
        // $header[] = "Keep-Alive: 3600";
        // $header[] = "Accept-Encoding: gzip, deflate, br"; 
        $header[] = "Accept-Charset: ISO-8859-1,tis-620,utf-8;q=0.7,*;q=0.7";
        $header[] = "Accept-Language: en-us,en,th-TH,th;q=0.5";
        $header[] = "Pragma: no-cache";
        $header[] = "Host: " .  $uriParts['host'];
        if ($option['cookies'] == '') {
            $cookie_header = "Cookie:";
            foreach ($_COOKIE as $key => $val) {
                $cookie_header .= " " . $key . "=" . $val . ";";
            }
            $header[] = $cookie_header . ' path=/; ';
        }
        if (is_array($option['headers'])) {
            foreach ($option['headers'] as $key => $val) {
                $header[] =  ucfirst(strtolower($key)) . ": " . $val;
            }
        }
        // $header[] = "REMOTE_ADDR: " . $_SERVER['REMOTE_ADDR'];
        // $header[] = "HTTP_X_FORWARDED_FOR: " . $_SERVER['REMOTE_ADDR'];

        if (is_array($data)) {
            $requestBody = http_build_query($data, '', '&');
        } else
            $requestBody = $data;

        if (!$ch)
            $ch = curl_init();

        if ($option['referer']) {
            curl_setopt($ch, CURLOPT_USERAGENT, "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
            if ($option['referer'] == 'auto')
                curl_setopt($ch, CURLOPT_REFERER, 'http://' . $_SERVER['SERVER_NAME'] . $_SERVER['REQUEST_URI']);
            else
                curl_setopt($ch, CURLOPT_REFERER, $option['referer']);
        }
        //  enable it if need to debug
        curl_setopt($ch, CURLOPT_VERBOSE, 1);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);

        if ($option['method'] === 'POST' || $option['method'] === 'PUT') {
            // if ($requestBody && is_array($requestBody)) {
            //     $requestBody = http_build_query($requestBody, '', '&');
            // }

            if ($option['method'] === 'PUT') {
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
            } else {
                curl_setopt($ch, CURLOPT_POST, true);
            }

            curl_setopt($ch, CURLOPT_POSTFIELDS, $requestBody);
        } else {
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $option['method']);
            if ($requestBody !== "")
                $url .= (strpos($requestBody, '?') === -1 || strpos($requestBody, '?') === false  ? '?' : '&') . $requestBody;
        }

        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_COOKIESESSION, 1);
        curl_setopt($ch, CURLOPT_COOKIE, $option['cookies'] . ' user=zd; activity=reading');
        curl_setopt($ch, CURLOPT_COOKIEFILE, $cookies_file);
        curl_setopt($ch, CURLOPT_COOKIEJAR, $cookies_file);

        curl_setopt($ch, CURLOPT_TIMEOUT, 360000);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 360000);

        curl_setopt($ch, CURLOPT_MAXREDIRS, 100);
        curl_setopt($ch, CURLOPT_ENCODING, '');
        curl_setopt($ch, CURLOPT_NOBODY, !$option['show_body']);
        curl_setopt($ch, CURLOPT_FAILONERROR, 1);
        if ($option['force_SSL3']) {
            curl_setopt($ch, CURLOPT_SSLVERSION, 3);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, !!$option['ca_path']);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, !!$option['ca_path']);
            curl_setopt($ch, CURLOPT_CAINFO, $option['ca_path']);
        }
        if ($option['to_file']) {
            curl_setopt($ch, CURLOPT_PUT, true); // without CURLOPT_PUT, CURLOPT_INFILE is ignored.
            curl_setopt($ch, CURLOPT_FILE, $option['to_file']);
        }
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch, CURLOPT_HEADER, !!$option['show_header']);


        if ($option['test'])
            dd("header", $header, "url", $url, "requestBody", $requestBody, "curl_exec", curl_exec($ch), "curl_error", curl_error($ch));

        if (curl_error($ch))
            return curl_error($ch);

        return curl_exec($ch);
        //        $exec = curl_exec($ch);
        //        curl_close($ch);
        //        return $exec;
    }
}
if (!function_exists('_debug_curl')) {
    function _debug_curl(&$ch, $print = 0)
    {
        if ($print)
            print curl_error($ch);
        else
            dd(curl_getinfo($ch));
    }
}
if (!function_exists('_disconnect_curl')) {
    function _disconnect_curl(&$ch)
    {
        curl_close($ch);
    }
}
if (!function_exists('_load_curl')) {
    /*
     * Example:
            $url = 'http://www.some-shop.com/products?page=:PAGER';
            $queryList = "//ul[@id='product_list']/li";

            $queryItem = [
                // get data from html attribute
                "img" => [
                    'query' => ".//div[@class='product_item']/a[@class='product_img_link']/img"
                    , 'attr' => "src"
                ],

                // get data inside link
                "link" => [
                    'query' => ".//div[@class='product_item']/a[@class='product_img_link']"
                    , 'attr' => "href"

                    , 'next' => [
                        "content" => [
                            'query' => "//div[@id='single_product_page']/div[@id='tab_description']"
                            , 'load' => "link"
                        ]
                    ]
                ],

                // get text content from html tag
                "model" => [
                    'query' => ".//div[@class='product_item']/h3"
                ],

                // get text content from html tag with callback
                "price" => [
                    'query' => ".//span[@class='price']"
                    , 'callback' => function _($result) {
                        return trim(str_replace(['\n', '\r', '฿', ','], '', $result));
                    }
                ],
            ];

     *** OR:
            $queryItem = [
                '_key' => ['img', 'link', 'content', 'model', 'price'],
                '_query' => './/td',
                '_callback' => function _(&$row) {
                    $row['price'] = intval($row['price']);
                }
            ];

     * Query:

            $result = [];
            load_curl($result, $url, $queryList, $queryItem, ['paged' => 1]);

     * Result:
            array:10 [
              0 => array:4 [
                    "img" => "",
                    "link" => "",
                    "content" => "",
                    "model" => "",
                    "price" => "",
                ]
              1 => ...
            ]
     * */
    function _load_curl(&$result, &$ch, $url = '', $queryList = '', $queryItem = [], $formData = '', $option = [])
    {
        _array_default([
            // curl
            'show_header' => true,
            'ca_path' => false,
            'cookies' => '',

            // query
            'paged' => null,
            'search' => null,

            'debug' => false,
            'force-ssl' => false,

        ], $option);

        if ($option['paged'])
            $url = str_replace(':PAGER', $option['paged'], $url);
        $dom = new \DOMDocument();
        $html = '';
        foreach (_array_wrap($url, [$url]) as $u)
            $html .= _connect_curl($ch, $option['force-ssl'] ? str_replace('http:', 'https:', $u) : $u, $formData, $option);
        //        $html = connect_curl($ch, $url, $formData, $option);
        @$dom->loadHTML($html);
        //        debug_curl($ch, 0);
        $xpath = new \DOMXPath($dom);
        if ($option['debug']) {
            $result['url'] = $url;
            $result['ch'] = $ch;
            $result['queryList'] = $xpath->query($queryList, $dom);
            //            $result['option'] = $option;
            $result['html'] = $html;
            //            $result['body'] = [$xpath->query("//body", $dom)[0]->textContent];
            //            $result['frmProduct'] = [$dom->saveHTML($xpath->query("//form[@name='frmProduct']", $dom)->item(0))];
            //            $result['searchResult'] = [$dom->saveHTML($xpath->query("//form[@name='searchResult']", $dom)->item(0))];
            return;
        }

        //        if (is_numeric($queryList)) {
        //            $result['option'] = $option;
        //            $result['url'] = $option['force-ssl'] ? str_replace('http:', 'https:', $url) : $url;
        //            $result['query'] = $xpath->query("//p[@class='old-price']/span", $dom)->item(0);
        //            $result['html'] = $dom->saveHTML($xpath->query("//p[@class='old-price']/span", $dom)->item(0));
        //            $result['queryList'] = $queryList;
        //            $result['queryItem'] = $queryItem;
        //            dd($result);
        //            return;
        //        }
        $form = is_numeric($queryList) ? [$queryList => $dom] : $xpath->query($queryList, $dom);
        foreach (_array_wrap($form) as $j => $row) {
            if (is_array($queryItem['_key']) && is_string($queryItem['_query'])) {
                $key = $queryItem['_key'];
                $col = $xpath->query($queryItem['_query'], $row);
                if ($col->length) {
                    foreach ($col as $k => $data)
                        if (isset($key[$k]) && $key[$k] != '') {
                            $result[$j][$key[$k]] = trim(str_replace(['\n', '\r', '฿', ','], '', $data->textContent));
                            if (is_callable($queryItem['_callback'])) {
                                $rCallback = $queryItem['_callback']($result, $j, $key[$k], $data, $xpath, $option['search']);
                                if ($rCallback !== false && $rCallback !== null)
                                    $result[$j][$key[$k]] = $rCallback;
                            }
                            //                                $queryItem['_callback']($result[$j], $data, $xpath, $option['search']);
                        }
                }

                if (isset($result[$j]) && count((array)$result[$j]) != count((array)$result[0]))
                    unset($result[$j]);
            } else {
                foreach ($queryItem as $n => $q) {
                    if (isset($q['query']))
                        foreach (_array_wrap($xpath->query($q['query'], $row)) as $k => $data) {
                            switch ($q['type']) {
                                case 'array':
                                    //                                    $result[$j][$n] = $dom->saveHTML($data);
                                    $aResult = [];
                                    foreach (_array_wrap($xpath->query($q['array-query'], $data)) as $ak => $adata) {
                                        if (!isset($q['attr'])) {
                                            $aResult[] = trim($adata->textContent);
                                        } else {
                                            $aResult[] = $adata->getAttribute($q['attr']);
                                        }
                                    }
                                    $result[$j][$n] = array_unique($aResult);
                                    break;
                                case 'html':
                                    $html = $dom->saveHTML($data);
                                    $html = preg_replace([
                                        '#<script\s*(type=("|\').*("|\'))?>(.+|\n|\r)*</script>#i',
                                        '#(class|style|id)=("|\').*("|\')#i',
                                        '#\s>#s',
                                        '/\>[^\S ]+/s',     // strip whitespaces after tags, except space
                                        '/[^\S ]+\</s',     // strip whitespaces before tags, except space
                                        '/(\s)+/s',         // shorten multiple whitespace sequences
                                        '/<!--(.|\s)*?-->/', // Remove HTML comments
                                        '#<(/?)button>#i',
                                        '#<(\w+)></(\w+)>#i'
                                    ], [
                                        '',
                                        '',
                                        '>',
                                        '>',
                                        '<',
                                        '\\1',
                                        '',
                                        '<$1a>',
                                        ''
                                    ], $html);
                                    $result[$j][$n] = $html;
                                    break;
                                case 'attr':
                                case 'text':
                                default:
                                    if (!isset($q['attr'])) {
                                        $result[$j][$n] = trim($data->textContent);
                                    } else {
                                        $result[$j][$n] = $data->getAttribute($q['attr']);
                                    }
                                    break;
                            }
                            if (is_callable($q['callback'])) {
                                $rCallback = $q['callback']($result, $j, $n, $data, $xpath, $option['search']);
                                if ($rCallback !== false && $rCallback !== null)
                                    $result[$j][$n] = $rCallback;
                            }
                            //                                $result[$j][$n] = $q['callback']($result[$j][$n], $data, $xpath, $option['search']);
                            if (is_array($q['next'])) {
                                _load_curl($result, $ch, $result[$j][$n], $j, $q['next'], $formData, $option);
                            }
                        }
                }
            }

            //            if (isset($result[$j]) && count((array)$result[$j]) != count((array)$result[0]))
            //                unset($result[$j]);
        }
    }
}
if (!function_exists('_check_remote_file')) {
    function _check_remote_file($url)
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        // don't download content
        curl_setopt($ch, CURLOPT_NOBODY, 1);
        curl_setopt($ch, CURLOPT_FAILONERROR, 1);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        //        curl_setopt($ch, CURLOPT_TIMEOUT, 1); // seconds unit
        if (curl_exec($ch) !== FALSE)
            return true;
        return false;
    }
}
if (!function_exists('get_remote_file_info')) {
    function get_remote_file_info($url)
    {
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
        curl_setopt($ch, CURLOPT_HEADER, TRUE);
        curl_setopt($ch, CURLOPT_NOBODY, TRUE);
        $data = curl_exec($ch);
        $fileSize = curl_getinfo($ch, CURLINFO_CONTENT_LENGTH_DOWNLOAD);
        $httpResponseCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        return [
            'file_exists' => (int) $httpResponseCode == 200,
            'file_size' => (int) $fileSize
        ];
    }
}
if (!function_exists('_retrieve_response')) {

    /**
     * Any implementing HTTP providers should send a request to the provided endpoint with the parameters.
     * They should return, in string form, the response body and throw an exception on error.
     *
     * @param Uri $endpoint
     * @param mixed $requestBody
     * @param array $extraHeaders
     * @param string $method
     *
     * @return string
     *
     * @throws \Exception
     * @throws \InvalidArgumentException
     */
    function _retrieve_response(
        Uri $endpoint,
        $requestBody,
        array $extraHeaders = array(),
        $method = 'POST'
    ) {
        /**
         * Additional parameters (as `key => value` pairs) to be passed to `curl_setopt`
         *
         * @var array
         */
        $parameters = [];

        /**
         * If true, explicitly sets cURL to use SSL version 3. Use this if cURL
         * compiles with GnuTLS SSL.
         *
         * @var bool
         */
        $forceSSL3 = false;

        /**
         * @var string The user agent string passed to services
         */
        $userAgent = null;

        /**
         * @var int The maximum number of redirects
         */
        $maxRedirects = 5;

        /**
         * @var int The maximum timeout
         */
        $timeout = 15;

        // Normalize method name
        $method = strtoupper($method);

        _normalize_headers($extraHeaders);

        if ($method === 'GET' && !empty($requestBody)) {
            throw new \InvalidArgumentException('No body expected for "GET" request.');
        }

        if (!isset($extraHeaders['Content-Type']) && $method === 'POST' && is_array($requestBody)) {
            $extraHeaders['Content-Type'] = 'Content-Type: application/x-www-form-urlencoded';
        }

        $extraHeaders['Host'] = 'Host: ' . $endpoint->getHost();
        $extraHeaders['Connection'] = 'Connection: close';

        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $endpoint->getAbsoluteUri());

        if ($method === 'POST' || $method === 'PUT') {
            if ($requestBody && is_array($requestBody)) {
                $requestBody = http_build_query($requestBody, '', '&');
            }

            if ($method === 'PUT') {
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
            } else {
                curl_setopt($ch, CURLOPT_POST, true);
            }

            curl_setopt($ch, CURLOPT_POSTFIELDS, $requestBody);
        } else {
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
        }

        if ($maxRedirects > 0) {
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_MAXREDIRS, $maxRedirects);
        }

        curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HEADER, false);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $extraHeaders);
        curl_setopt($ch, CURLOPT_USERAGENT, $userAgent);

        foreach ($parameters as $key => $value) {
            curl_setopt($ch, $key, $value);
        }

        if ($forceSSL3) {
            curl_setopt($ch, CURLOPT_SSLVERSION, 3);
        }

        $response = curl_exec($ch);
        $responseCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if (false === $response) {
            $errNo = curl_errno($ch);
            $errStr = curl_error($ch);
            curl_close($ch);
            if (empty($errStr)) {
                throw new \Exception('Failed to request resource.', $responseCode);
            }
            throw new \Exception('cURL Error # ' . $errNo . ': ' . $errStr, $responseCode);
        }

        curl_close($ch);

        return $response;
    }
}
if (!function_exists('_normalize_headers')) {
    /**
     * @param array $headers
     */
    function _normalize_headers(&$headers)
    {
        // Normalize headers
        Arr::walk(
            $headers,
            function (&$val, &$key) {
                $key = ucfirst(strtolower($key));
                $val = ucfirst(strtolower($key)) . ': ' . $val;
            }
        );
    }
}

if (!function_exists('_pre_render')) {
    function _pre_render($url)
    {
        $ch = curl_init();
        $content = _connect_curl($ch, $url);
        $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        return $content;
        //        return new Response($content, $status);
    }
}
