<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- CSRF Token -->
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <meta name="user-api-token"
        content="<?php echo e(optional(auth()->user())->getFirstApiKey() ?? config('auth.defaults.api')); ?>">

    <title>
        <?php $__env->startSection("title"); ?>
        <?php echo SettingDirective::show(['core::site-name']); ?> 
        <?php echo $__env->yieldSection(); ?>
    </title>

    <!-- Google font-->
    
    
    <link
        href="https://fonts.googleapis.com/css?family=Prompt:200,200i,300,300i,400,400i,600,600i,700,700i,800,800i,900,900i"
        rel="stylesheet">
    <?php $__currentLoopData = $cssFiles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $css): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <link media="all" type="text/css" rel="stylesheet" href="<?php echo e(asset($css)); ?>">
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

    <!-- animate -->
    <link rel="stylesheet" type="text/css" href="<?php echo e(Module::asset('core:css/animate.min.css')); ?>">

    <!-- Font Face-->
    

    <!-- Font Awesome-->
    <link rel="stylesheet" type="text/css" href="<?php echo e(Module::asset("core:vendor/font-awesome/v5.8.2/css/all.min.css")); ?>">
    

    <!-- Flag icon-->
    <link rel="stylesheet" type="text/css" href="<?php echo e(_asset('themes/multikart/css/themify.css')); ?>">

    <!-- Flag icon-->
    <link rel="stylesheet" type="text/css" href="<?php echo e(_asset('themes/multikart/css/flag-icon.css')); ?>">

    <!-- slick icon-->
    

    <!-- jsgrid css-->
    

    <!-- Bootstrap css-->
    <link rel="stylesheet" type="text/css" href="<?php echo e(_asset('themes/multikart/css/bootstrap.css')); ?>">

    <!-- sweetalert2 css-->
    <link rel="stylesheet" type="text/css"
        href="<?php echo e(Module::asset('core:vendor/sweetalert2/v11.1.5/sweetalert2.min.css')); ?>">

    <!-- App css-->
    <link rel="stylesheet" type="text/css" href="<?php echo e(_asset('themes/multikart/css/admin.css')); ?>">
    <link rel="stylesheet" type="text/css" href="<?php echo e(Module::asset('core:css/main.css?v=' . time())); ?>">

    <?php $__env->startSection('styles'); ?>
    <?php echo $__env->yieldSection(); ?>
    <?php echo $__env->yieldPushContent('css-stack'); ?>

    <script src="<?php echo e(_asset('themes/multikart/js/jquery-3.3.1.min.js')); ?>"></script>
    <script>
        $.ajaxSetup({
            headers: {
                'Authorization': 'Bearer <?php echo e(optional($currentUser)->getFirstApiKey() ?? config("auth.defaults.api")); ?>',
                'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
            },
            data: {
                _token: '<?php echo e(csrf_token()); ?>'
            }
        });
        var AuthorizationHeaderValue = 'Bearer <?php echo e(optional($currentUser)->getFirstApiKey() ?? config("auth.defaults.api")); ?>';
    </script>

    <!-- Global site tag (gtag.js) - Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-XPKT80QMV3"></script>
    <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());

    gtag('config', 'G-XPKT80QMV3');
    </script>
</head>

<body>
    <?php echo $__env->make('partials.loader', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php echo $__env->make('partials.nav', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <div class="cd-overlay">
        <!-- shadow layer visible when navigation is visible -->
    </div>

    <!-- page-wrapper Start-->
    <div class="page-wrapper">

        <!-- Page Header Start-->
        <?php $__env->startSection('header'); ?>
            <?php echo $__env->make('partials.master-header', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        <?php echo $__env->yieldSection(); ?>
        <!-- Page Header Ends -->

        <!-- Page Body Start-->
        <div class="page-body-wrapper">

            <?php $__env->startSection('sidebar'); ?>
            <?php echo $__env->yieldSection(); ?>
            <?php echo $__env->yieldPushContent('sidebar-stack'); ?>

            <div class="page-body">
                <!-- Container-fluid starts-->
                <div class="container-fluid">
                    <?php $__env->startSection('page-header'); ?>
                        
                    <?php echo $__env->yieldSection(); ?>
                </div>
                <!-- Container-fluid Ends-->

                <div class="container-fluid">
                    <?php echo $__env->make('partials.notifications', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    <?php $__env->startSection('content'); ?>
                    <?php echo $__env->yieldSection(); ?>
                </div>
            </div>

        </div>
    </div>


    <!-- footer start-->
    <footer class="footer d-print-none">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-6 footer-copyright">
                    <p class="mb-0">Copyright 2021 © SUKSAPAN PANIT, All rights reserved.</p>
                </div>
                <div class="col-md-6">
                    <p class="pull-right mb-0">Hand crafted & made with<i class="fa fa-heart"></i></p>
                </div>
            </div>
        </div>
    </footer>
    <!-- footer end-->
    <!-- latest jquery-->
    
    
    

    <?php $__currentLoopData = $jsFiles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $js): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <script src="<?php echo e(asset($js)); ?>" type="text/javascript"></script>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

    <!-- Bootstrap js-->
    <script src="<?php echo e(_asset('themes/multikart/js/popper.min.js')); ?>"></script>
    <script src="<?php echo e(_asset('themes/multikart/js/bootstrap.js')); ?>"></script>

    <!-- feather icon js-->
    <script src="<?php echo e(_asset('themes/multikart/js/icons/feather-icon/feather.min.js')); ?>"></script>
    <script src="<?php echo e(_asset('themes/multikart/js/icons/feather-icon/feather-icon.js')); ?>"></script>

    <script src="<?php echo e(_asset('themes/multikart/js/sidebar-menu.js')); ?>"></script>

    <!-- Slick -->
    

    <!-- Jsgrid js-->
    

    <!-- lazyload js-->
    <script src="<?php echo e(_asset('themes/multikart/js/lazysizes.min.js')); ?>"></script>

    <!--right sidebar js-->
    

    <!-- sweetalert2 js-->
    <script src="<?php echo e(Module::asset('core:vendor/sweetalert2/v11.1.5/sweetalert2.min.js')); ?>"></script>

    <!-- fastclick js-->
    

    <!--script admin-->
    <script src="<?php echo e(_asset('themes/multikart/js/clipboard/clipboard.min.js')); ?>"></script>
    <script src="<?php echo e(_asset('themes/multikart/js/admin-script.js?v=1.0.1')); ?>"></script>
    <script src="<?php echo e(Module::asset('core:js/functions.js?v=1.0.2')); ?>"></script>
    <script src="<?php echo e(Module::asset('core:js/main.js?v=1.0.2')); ?>"></script>
    <?php $__env->startSection('scripts'); ?>
    <?php echo $__env->yieldSection(); ?>
    <?php echo $__env->yieldPushContent('js-stack'); ?>
</body>

</html>
<?php /**PATH C:\Projects\Web\htdocs\ssp4-v11\Themes\Multikart\views/layouts/master.blade.php ENDPATH**/ ?>