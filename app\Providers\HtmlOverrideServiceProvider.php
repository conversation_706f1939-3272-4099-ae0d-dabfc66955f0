<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Foundation\AliasLoader;

class HtmlOverrideServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Override the Html facade after all packages are loaded
        $loader = AliasLoader::getInstance();
        $loader->alias('Html', \App\Support\Facades\Html::class);
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Override the ThemeHtmlBuilder binding after all packages are loaded
        $this->overrideThemeHtmlBuilder();
    }

    /**
     * Override the ThemeHtmlBuilder to use our custom Html implementation
     */
    protected function overrideThemeHtmlBuilder(): void
    {
        // Override the stylist.theme binding to use our custom Html implementation
        $this->app->singleton('stylist.theme', function($app) {
            // Create a custom ThemeHtmlBuilder that uses our FormBuilder for script/style methods
            return new \App\Support\CustomThemeHtmlBuilder(
                $app->make(\Spatie\Html\Html::class), // Still need the original Spatie instance for type hint
                $app->make('form'), // Our FormBuilder for script/style methods
                $app['url']
            );
        });
    }
}
