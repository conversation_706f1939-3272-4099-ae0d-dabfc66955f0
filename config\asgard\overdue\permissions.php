<?php

return [
    'overdue.self' => [
        'index' => 'overdue::overdues.list resource',
        'create' => 'overdue::overdues.create resource',
        'edit' => 'overdue::overdues.edit resource',
        'destroy' => 'overdue::overdues.destroy resource',
    ],
    'overdue.pricings' => [
        'index' => 'overdue::pricings.list resource',
        'create' => 'overdue::pricings.create resource',
        'edit' => 'overdue::pricings.edit resource',
        'destroy' => 'overdue::pricings.destroy resource',
        'setting' => 'overdue::pricings.setting',
    ],
    'overdue.overdues' => [
        'index' => 'overdue::overdues.list resource',
        'create' => 'overdue::overdues.create resource',
        'edit' => 'overdue::overdues.edit resource',
        'destroy' => 'overdue::overdues.destroy resource',
    ],
    'overdue.shops' => [
        'index' => 'overdue::shops.list resource',
        'create' => 'overdue::shops.create resource',
        'edit' => 'overdue::shops.edit resource',
        'destroy' => 'overdue::shops.destroy resource',
    ],
    'overdue.records' => [
        'index' => 'overdue::records.list resource',
        'create' => 'overdue::records.create resource',
        'edit' => 'overdue::records.edit resource',
        'destroy' => 'overdue::records.destroy resource',
    ],
// append



];
