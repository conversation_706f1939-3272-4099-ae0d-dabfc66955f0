<?php

use Illuminate\Support\Arr;
use Illuminate\Support\Str;

if (!function_exists('_random_password')) {
    function _random_password()
    {
        $alphabet = 'abcdefghijklmnopqrstuwxyzABCDEFGHIJKLMNOPQRSTUWXYZ0123456789';
        $pass = array();
        $alphaLength = strlen($alphabet) - 1;
        for ($i = 0; $i < 9; $i++) {
            $n = rand(0, $alphaLength);
            $pass[] = $alphabet[$n];
        }
        return implode($pass);
    }
}

if (!function_exists('_random_string')) {
    function _random_string($length = 10)
    {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $charactersLength = strlen($characters);
        $randomString = '';
        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[rand(0, $charactersLength - 1)];
        }
        return $randomString;
    }
}

if (!function_exists('_generate_acronym')) {
    /**
     * Generate Acronym
     * Example
     *   Home Audio Theathers => HAT
     *   Accessories => ACC
     *   Office Electronic => OFE
     *
     * @return string
     */
    function _generate_acronym($text, $length = 3)
    {
        $text = strtoupper($text);
        if (preg_match_all('/\b(\w)/', $text, $matched)) {
            $str = implode('', $matched[1]);
            $len = strlen($str);
            if ($len <= $length) {
                if ($len > 1) {
                    $postfix = substr($str, 1);
                    $str = Arr::first(str_split($text, $length - ($len - 1))) . $postfix;
                } else
                    $str = Arr::irst(str_split($text, $length));
            }
            return strlen($str) <= $length ? str_pad($str, $length, 0) : substr($str, 0, $length);
        }
        return null;
    }
}

if (!function_exists('_get_attr_data')) {
    function _get_attr_data($data = [], $prefix = "")
    {
        $attr = [];
        foreach (_array_wrap($data) as $key => $val) {
            $attr[] = "data-{$prefix}{$key}=\"{$val}\"";
        }
        return implode(" ", $attr);
    }
}

if (!function_exists('_unique_name')) {
    function _unique_name($text, $separator = '_')
    {
        preg_match("/^(.*){$separator}(\d+)$/", $text, $match);
        return ($match[2] ? $match[1] : $text) . $separator . (++$match[2]);
    }
}

if (!function_exists('_dynamic_search_term')) {
    function _dynamic_search_term($text, $type = 'php', $searchBy = null)
    {
        if (is_numeric($text))
            return $text;
        switch ($type) {
            case 'php':
                $replace = ['/', '-', ',', ' '];
                if (!$searchBy['post_type'] || in_array($searchBy['post_type'], ['place']))
                    $replace = array_merge(['จังหวัด', 'อำเภอ', 'ตำบล', 'เขต', 'แขวง', 'รหัสไปรษณีย์', 'country', 'street', 'city', 'state'], $replace);
//                $str = str_replace($replace, '', strtolower($text));
                $str = str_replace($replace, '%', strtolower($text));
                return $str;
//                preg_match_all('/([0-9]+|[a-zA-Zก-ฮ]+)/', $str, $arr);
//                $end = end($arr);
//                return implode('%', is_array($end) ? $end : $arr);
            case 'mysql':
            case 'sql':
                if(Str::endsWith($text, ['.id', '_id']) || Str::startsWith($text, ['CONCAT', 'SUM']))
                    return $text;
                return "REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(LOWER({$text}), '}', ''), '{', ''), ':', ''), ',', ''), '\\\"', ''), ' ', ''), '-', '')";
        }
        return $text;
    }
}