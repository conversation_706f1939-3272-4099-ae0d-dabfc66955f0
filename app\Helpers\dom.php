<?php

use Illuminate\Support\Arr;
use Illuminate\Support\Str;

if (!function_exists('_html_decode')) {
    function _html_decode($html)
    {
        return html_entity_decode(trim($html), ENT_QUOTES, 'UTF-8');
    }
}

if (!function_exists('_str_get_html')) {
    function _str_get_html($html)
    {
        return str_get_html($html, true, true, DEFAULT_TARGET_CHARSET, false);
    }
}

if (!function_exists('simple_directive_array')) {
    /**
     * @call  $content = simple_directive_dom($template, $loopPostsDirective, ['posts' => $posts_array]);
     *
     * @param string $html_template
     * @param array $directive
     * @param array $data
     *
     * @return string
     */
    function simple_directive_array($html_template, $directive, $data)
    {
        require_once(app_path("Library/simple_html_dom.php"));
        $ns = _str_get_html($html_template);
        recursive_directive_array($ns, $directive, $data);
        return $ns->save();
    }
}
if (!function_exists('recursive_directive_array')) {
    /**
     * @param simple_html_dom &$ns
     * @param array $directive
     * @param array $data
     * @param string $type (direct|blade)
     *
     * @return void
     */
    function recursive_directive_array(simple_html_dom &$ns, $directive, $data)
    {
        if (!is_array($directive) || !is_array($data))
            return;

        foreach ($directive as $query => $data_key) {
            if (!is_array($data_key)) {
                $d = $attr = $selector = null;

                // get data
                if (is_string($data_key)) {
                    if (str_contains($data_key, '$pos')) {
                        $pos = Arr::get($data, "pos");
                        try {
                            $d = eval("return " . str_replace("\$pos", $pos, $data_key) . ";");
                        } catch (Throwable $t) {
                            $d = 0;
                        }
                    } else
                        $d = Arr::get($data, $data_key);
                } else if (is_callable($data_key))
                    $d = call_user_func_array($data_key, $data);

                // check query
                $concat_front = Str::startsWith($query, "+"); // bool
                $concat_back = Str::endsWith($query, "+"); // bool
                $_query = str_replace("+", "", $query);
                if (preg_match("/(.+)@(.+)/", $_query, $matched)) {
                    list($q, $selector, $attr) = $matched;
                } else $selector = $_query;
                /* @var simple_html_dom_node $item */
                foreach ($ns->find($selector) as $item) {
                    if ($attr) {
                        if ($concat_front || $concat_back) {
                            $old = $item->getAttribute($attr);
                            if ($concat_front)
                                $d = $d . $old;
                            if ($concat_back)
                                $d = $old . $d;
                        }
                        $item->setAttribute($attr, $d);
                    } else {
                        if ($concat_front || $concat_back) {
                            $old = $item->innertext;
                            if ($concat_front)
                                $d = $d . $old;
                            if ($concat_back)
                                $d = $old . $d;
                        }
                        $item->innertext = $d;
                    }
                }

            } else {
                foreach ($data_key as $loop => $new_directive) {
                    if (preg_match("/(.*)<-(.*)/", $loop, $matched)) {
                        list($q, $as, $arr) = $matched;
                        $sub_data = $arr && is_array($data[$arr]) ? $data[$arr] : $data;
                        foreach ($sub_data as $pos => $item) {
                            $item['pos'] = $pos;
                            recursive_directive_array($ns, $new_directive, [$as => $item, "pos" => $pos]);
                        }
                    }
                }
            }
        }
    }
}


if (!function_exists('simple_directive_blade')) {
    /**
     * @call  $content = simple_directive_dom($template, $loopPostsDirective, ['posts' => $posts_array]);
     *
     * @param string $html_template
     * @param array $directive
     * @param string $array_name
     *
     * @return string
     */
    function simple_directive_blade($html_template, $directive, $array_name = "posts", $parent_container = false)
    {
        require_once(app_path("Library/simple_html_dom.php"));
        $ns = _str_get_html($html_template);
        recursive_directive_blade($ns, $directive, $array_name);

        if ($parent_container)
            return $ns->find($parent_container, 0)->innertext;
        return $ns->save();
    }
}
if (!function_exists('recursive_directive_blade')) {
    /**
     * @param simple_html_dom &$ns
     * @param array $directive
     * @param string $array_name
     *
     * @return void
     */
    function recursive_directive_blade(simple_html_dom &$ns, $directive, $array_name = "posts", $parent_selector = false)
    {

        if (!is_array($directive))
            return;

        foreach ($directive as $query => $data_key) {
            if (!is_array($data_key)) {
                $d = $attr = $selector = null;

                // get data
                if (is_string($data_key)) {
                    if (str_contains($data_key, "\$")) {
                        $d = "{!! $data_key !!}";
                    } else if (preg_match("/^$array_name\.(.+)$/", $data_key, $matched)) {
                        $data_key = end($matched);
                        $d = "{!! Arr::get(\$$array_name, '$data_key') !!}";
                    } else {
                        $d = "{!! Arr::get(\$$array_name, '$data_key') !!}";
                    }
                } else continue;

                // check query
                $concat_front = Str::startsWith($query, "+"); // bool
                $concat_back = Str::endsWith($query, "+"); // bool
                $_query = str_replace("+", "", $query);

                if (preg_match("/(.+)@(.+)/", $_query, $matched)) {
                    list($q, $selector, $attr) = $matched;
                } else $selector = $_query;

                if ($selector == "." && $parent_selector)
                    $selector = $parent_selector;

                /* @var simple_html_dom_node $item */
                foreach ($ns->find($selector) as $item) {
                    set_directive_blade($item, $d, $attr, $concat_front, $concat_back);
                }

            } else { // $query is .[data]-item class

                foreach ($ns->find($query . ".loop_item") as $item) {
                    if (!$item->hasClass("main")) {
                        $item->remove();
                    } else {
                        foreach ($data_key as $loop => $new_directive) {
                            if (preg_match("/(.*)<-(.*)/", $loop, $matched)) {
                                list($q, $as, $arr) = $matched;

                                if (preg_match("/^$array_name\.(.+)$/", $arr, $matched)) {
                                    $arr = end($matched);
                                    $arr = "Arr::get(\$$array_name, '$arr')";
                                } else
                                    $arr = "\$$arr";

                                $for = "\n\r @foreach($arr as \$pos => \$$as) \n\r";
                                $ns2 = _str_get_html($item->outertext);
                                recursive_directive_blade($ns2, $new_directive, "$as", $query);
                                $for .= $ns2->save();
                                $for .= "\n\r @endforeach \n\r";

                                $item->parentNode()->innertext = $for;
                            }
                        }
                    }
                }

            }
        }
    }
}

if (!function_exists('set_directive_blade')) {
    /**
     * @return void
     */
    function set_directive_blade(&$item, $data, $set_attr, $concat_front, $concat_back)
    {

        if ($set_attr) {
            if ($concat_front || $concat_back) {
                $old = $item->getAttribute($set_attr);
                if ($concat_front)
                    $data = $data . $old;
                if ($concat_back)
                    $data = $old . $data;
            }
            $item->setAttribute($set_attr, $data);
        } else {
            if ($concat_front || $concat_back) {
                $old = $item->innertext;
                if ($concat_front)
                    $data = $data . $old;
                if ($concat_back)
                    $data = $old . $data;
            }
            $item->innertext = $data;
        }
    }
}
if (!function_exists('get_directive_post_content')) {
    /**
     * @return array
     */
    function get_directive_post_content($param = 'post')
    {
        if ($param)
            $param .= ".";
        return [
            '.@id+' => $param . 'id',
            '.@data-post_id' => $param . 'id',
            '.@data-index' => '$pos',
            '.zd_post_item@id+' => $param . 'id',
            '.zd_post_item@data-index' => '$pos',
            '.zd_post_item@data-post_id' => $param . 'id',
            '.zd_posts_id@data-post_id' => $param . 'id',
            'input.zd_posts_id@value' => $param . 'id',
            '.zd_posts_number' => '1+$pos',
            '.zd_posts_image@data-src' => $param . 'featured_image.original',
            'a.zd_posts_image@href' => $param . 'featured_image.original',
            'img.zd_posts_image@src' => $param . 'featured_image.original',
            'img.zd_posts_image_lg@src' => $param . 'featured_image.original',
            'img.zd_posts_image_md@src' => $param . 'featured_image.md',
            'img.zd_posts_image_sm@src' => $param . 'featured_image.sm',
            '.zd_posttype_label' => $param . 'type_label',
            '.zd_posts_title' => $param . 'translations.title',
            '.zd_posts_link@href' => $param . 'urls.public_url',
            '.zd_posts_summary' => $param . 'translations.summary',
            '.zd_posts_body' => $param . 'translations.body',
            '.zd_posts_like_stat' => $param . 'stat_like',
            '.zd_posts_view_stat' => $param . 'stat_view',
            '.zd_posts_comment_link@href' => $param . 'urls.comment_url',
            '.zd_posts_comment_stat' => $param . 'stat_comment',
            '.zd_base_price' => $param . 'option_default.base_price',
            '.zd_price' => $param . 'option_default.price',
            '.zd_posts_orderable@data-title' => $param . 'translations.title',
            '.zd_posts_orderable@data-price' => $param . 'option_default.price',
            '.zd_posts_orderable@data-image' => $param . 'featured_image.md',
            '.zd_posts_orderable@data-post_id' => $param . 'id',
            '.zd_posts_orderable@data-option_id' => $param . 'option_default.id',
            '.zd_posts_by' => $param . 'created_by',
            '.zd_date_full' => $param . 'created_at.full',
            'time.zd_datetime@datetime' => $param . 'created_at.date',
            '.zd_date_year' => $param . 'created_at.year',
            '.zd_date_month_short' => $param . 'created_at.month_short',
            '.zd_date_month_full' => $param . 'created_at.zd_date_month_full',
            '.zd_date_day' => $param . 'created_at.day',
            '.zd_gallery_item' => [
                "g<-{$param}gallery" => [
                    '.@data-owl-item' => '$pos',
//                    '.@href' => 'g.original',
                    'img.zd_gallery_image@src' => 'g.original',
                    'img.zd_gallery_image_lg@src' => 'g.original',
                    'img.zd_gallery_image_md@src' => 'g.md',
                    'img.zd_gallery_image_sm@src' => 'g.sm',
                ]
            ],
            '.zd_tag_item' => [
                "t<-{$param}tags" => [
                    '.' => 't',
                    '.@href+' => 't',
                ]
            ],
            '.zd_category_item' => [
                "t<-{$param}categories" => [
                    '.' => 't',
                    '.@href+' => 't',
                ]
            ],
            '.zd_options' => [
//                "option<-{$param}properties.options" => [
                "option<-{$param}options_prop" => [
                    '.zd_option_item' => [
                        'o<-option.list' => [
                            '.' => 'o',
                            '.@value' => "o"
                        ]
                    ]
                ]
            ],
            '.zd_option1_item' => [
//                "option1<-{$param}properties.options.option1.list" => [
                "option1<-{$param}options_prop.option1.list" => [
                    '.txt' => 'option1',
                    '.@data-key' => "o1_ . \$pos",
                    '.@value' => "o1_ . \$pos",
                ]
            ],
            '.zd_option2_item' => [
//                "option2<-{$param}properties.options.option2.list" => [
                "option2<-{$param}options_prop.option2.list" => [
                    '.txt' => 'option2',
                    '.@data-key' => "os_ . \$pos",
                    '.@value' => "os_ . \$pos",
                ]
            ]
        ];
    }
}
if (!function_exists('get_directive_post_archive')) {
    /**
     * @return array
     */
    function get_directive_post_archive($param = "posts")
    {
        return [
            '.zd_post_item' => [
                "p<-{$param}" => get_directive_post_content('p')
            ],
            '.zd_pagination' => '$pagination',
            '.zd_poststype_label' => '$type_label',
        ];
    }
}