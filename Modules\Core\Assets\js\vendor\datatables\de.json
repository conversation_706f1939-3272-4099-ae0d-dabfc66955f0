{"sEmptyTable": "<PERSON><PERSON> in der Tabelle vorhanden", "sInfo": "_START_ bis _<PERSON><PERSON>_ von _TOTAL_ Einträgen", "sInfoEmpty": "0 bis 0 von 0 Einträgen", "sInfoFiltered": "(g<PERSON><PERSON><PERSON> von _MAX_ Einträgen)", "sInfoPostFix": "", "sInfoThousands": ".", "sLengthMenu": "_MENU_ Einträge anzeigen", "sLoadingRecords": "Wird geladen...", "sProcessing": "Bitte warten...", "sSearch": "<PERSON><PERSON>", "sZeroRecords": "<PERSON><PERSON> Ein<PERSON>ä<PERSON> vorhanden.", "oPaginate": {"sFirst": "<PERSON><PERSON><PERSON>", "sPrevious": "Zurück", "sNext": "Nächste", "sLast": "Letzte"}, "oAria": {"sSortAscending": ": aktiv<PERSON><PERSON>, um Spalte aufsteigend zu sortieren", "sSortDescending": ": aktivieren, um Spalte absteigend zu sortieren"}}