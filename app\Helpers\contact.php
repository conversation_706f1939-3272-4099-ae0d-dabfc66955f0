
<?php

use Illuminate\Support\Facades\Config;
// use Illuminate\Contracts\Mail\Mailer;
use Illuminate\Mail\Mailer;
use Illuminate\Support\Facades\Mail;
use Illuminate\View\View;

// use Illuminate\Support\Facades\View;

if (!function_exists('_set_custom_smtp')) {
    function _set_custom_smtp($smtp = null)
    {
        $smtp = $smtp ?? _setting("contact::mail-server.smtp", null, null, true);

        if (is_array($smtp)) {
            if ($smtp['host'] != '') {
                Config::set('mail.host', $smtp['host']);
                Config::set('mail.encryption', $smtp['encryption']);
                if ($smtp['port'] != '')
                    Config::set('mail.port', $smtp['port']);
            }
            if ($smtp['username'] != '')
                Config::set('mail.username', $smtp['username']);
            if ($smtp['password'] != '')
                Config::set('mail.password', $smtp['password']);
        }

        // Setting the server, port and encryption
        $transport = new \Swift_SmtpTransport(config('mail.host'), config('mail.port'), config('mail.encryption'));
        //            $transport = \Swift_MailTransport ::newInstance(config('mail.host'), config('mail.port'), config('mail.encryption'))
        $transport->setStreamOptions([
            'ssl' => array(
                'verify_peer' => false,
                'verify_peer_name' => false,
                'allow_self_signed' => true
            )
        ])
            ->setUsername(config('mail.username'))
            ->setPassword(config('mail.password'));

        // Creating the Swift_Mailer instance and pass the config settings
        $swift = new \Swift_Mailer($transport);

        return $swift;
    }
}

/**
 * _send_mail_smtp([
 * 'mailto' => '<EMAIL>,<EMAIL>',
 * 'sender_email' => '<EMAIL>',
 * 'sender_first_name' => '',
 * 'sender_last_name' => '',
 * ]);
 */
if (!function_exists('_send_mail_smtp')) {
    function _send_mail_smtp(Mailer &$mailer, $view, $input)
    // function _send_mail_smtp($view, $input)
    {

        // $recipient = _setting("contact::recipient.default", null, ['enable' => true, 'mailto' => config('mail.from.address')], 'mailto');
        $mailto = explode(',', $input['mailto'] ?? config('mail.from.address'));
        // $view = "contact::emails.default";

        if (isset($input['smtp'])) {
            // Creating the Swift_Mailer instance and pass the config settings
            $swift = _set_custom_smtp($input['smtp']);

            // Set new Swift Mailer instance.
            $mailer->setSwiftMailer($swift);
        }

        unset($input['accept']);
        unset($input['g-recaptcha-response']);
        $mailer->send($view, ['input' => $input], function ($message) use ($input, $mailto) {
            $message->from($input['sender_email'], $input['sender_first_name'] . " " . $input['sender_last_name']);
            $message->to($mailto);
            $message->subject($input['subject'] . " | " . date("d M Y h:i A"));
        });
    }
}


if (!function_exists('verify_reCaptcha')) {
    /**
     * @param string $recaptcha_response ( $_POST['g-recaptcha-response'] )
     */
    function verify_reCaptcha(string $recaptcha_response, $siteKey = null, $secret = null)
    {

        // Initiate the autoloader.
        require_once app_path('Library/ReCaptcha/autoload.php');

        // Register You API keys at https://www.google.com/recaptcha/admin
        // And write it here
        $siteKey = $siteKey ?? _setting("contact::recaptcha.sitekey"); // config("services.recaptcha.key"); // core.i-styles.co
        $secret = $secret ?? _setting("contact::recaptcha.secret"); // config("services.recaptcha.secret"); // core.i-styles.co

        // reCAPTCHA supported 40+ languages listed here: https://developers.google.com/recaptcha/docs/language
        $lang = 'en';

        // If No key
        if ($siteKey === '' || $secret === '') :
            return ('CPT001');
        elseif (isset($recaptcha_response)) :

            // If the form submission includes the "g-captcha-response" field
            // Create an instance of the service using your secret
            $recaptcha = new \App\Library\ReCaptcha\ReCaptcha($secret);

            // Make the call to verify the response and also pass the user's IP address
            $resp = $recaptcha->verify($recaptcha_response, $_SERVER['REMOTE_ADDR']);

            if ($resp->isSuccess()) :
                // If the response is a success, that's it!
                return true; // die('CPT000');
            else :
                // Something wrong
                return ('CPT002');
            endif;

        endif;
    }
}
