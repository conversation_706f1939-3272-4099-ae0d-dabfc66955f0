[2025-06-23 09:00:03] production.ERROR: SQLSTATE[HY000] [2002] A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond (Connection: mysqlfix, SQL: select * from `setting__settings` where `name` = core::locales limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond (Connection: mysqlfix, SQL: select * from `setting__settings` where `name` = core::locales limit 1) at C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(979): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(958): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#2 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(781): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#3 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#5 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(343): Illuminate\\Database\\Query\\Builder->get(Array)
#9 C:\\Projects\\Web\\htdocs\\ssp4-v11\\Modules\\Core\\Providers\\CoreServiceProvider.php(301): Illuminate\\Database\\Query\\Builder->first()
#10 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(427): Modules\\Core\\Providers\\CoreServiceProvider->Modules\\Core\\Providers\\{closure}()
#11 C:\\Projects\\Web\\htdocs\\ssp4-v11\\Modules\\Core\\Providers\\CoreServiceProvider.php(297): Illuminate\\Cache\\Repository->remember('asgard.locales', 120, Object(Closure))
#12 C:\\Projects\\Web\\htdocs\\ssp4-v11\\Modules\\Core\\Providers\\CoreServiceProvider.php(87): Modules\\Core\\Providers\\CoreServiceProvider->setLocalesConfigurations()
#13 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(896): Modules\\Core\\Providers\\CoreServiceProvider->register()
#14 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register(Object(Modules\\Core\\Providers\\CoreServiceProvider))
#15 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\nwidart\\laravel-modules\\src\\Laravel\\Module.php(33): Illuminate\\Foundation\\ProviderRepository->load(Array)
#16 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\nwidart\\laravel-modules\\src\\Module.php(269): Nwidart\\Modules\\Laravel\\Module->registerProviders()
#17 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\nwidart\\laravel-modules\\src\\FileRepository.php(321): Nwidart\\Modules\\Module->register()
#18 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\nwidart\\laravel-modules\\src\\Providers\\BootstrapServiceProvider.php(23): Nwidart\\Modules\\FileRepository->register()
#19 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(896): Nwidart\\Modules\\Providers\\BootstrapServiceProvider->register()
#20 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\nwidart\\laravel-modules\\src\\ModulesServiceProvider.php(31): Illuminate\\Foundation\\Application->register(Object(Nwidart\\Modules\\Providers\\BootstrapServiceProvider))
#21 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\nwidart\\laravel-modules\\src\\LaravelModulesServiceProvider.php(17): Nwidart\\Modules\\ModulesServiceProvider->registerModules()
#22 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Nwidart\\Modules\\LaravelModulesServiceProvider->boot()
#23 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#28 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1132): Illuminate\\Foundation\\Application->bootProvider(Object(Nwidart\\Modules\\LaravelModulesServiceProvider))
#29 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Nwidart\\Modules\\LaravelModulesServiceProvider), 'Nwidart\\\\Modules...')
#30 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): array_walk(Array, Object(Closure))
#31 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#32 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#33 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(187): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#34 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(171): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#35 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#36 C:\\Projects\\Web\\htdocs\\ssp4-v11\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#37 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond at C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:66)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(66): PDO->__construct('mysql:host=192....', 'suksapan_tc', Object(SensitiveParameterValue), Array)
#1 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(85): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=192....', 'suksapan_tc', Object(SensitiveParameterValue), Array)
#2 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(48): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection(Object(PDOException), 'mysql:host=192....', 'suksapan_tc', Object(SensitiveParameterValue), Array)
#3 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=192....', Array, Array)
#4 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func(Object(Closure))
#7 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1267): Illuminate\\Database\\Connection->getPdo()
#8 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(512): Illuminate\\Database\\Connection->getReadPdo()
#9 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect(true)
#10 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#11 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(979): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#12 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(958): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#13 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(781): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#14 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#15 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#16 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#17 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#18 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#19 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(343): Illuminate\\Database\\Query\\Builder->get(Array)
#20 C:\\Projects\\Web\\htdocs\\ssp4-v11\\Modules\\Core\\Providers\\CoreServiceProvider.php(301): Illuminate\\Database\\Query\\Builder->first()
#21 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(427): Modules\\Core\\Providers\\CoreServiceProvider->Modules\\Core\\Providers\\{closure}()
#22 C:\\Projects\\Web\\htdocs\\ssp4-v11\\Modules\\Core\\Providers\\CoreServiceProvider.php(297): Illuminate\\Cache\\Repository->remember('asgard.locales', 120, Object(Closure))
#23 C:\\Projects\\Web\\htdocs\\ssp4-v11\\Modules\\Core\\Providers\\CoreServiceProvider.php(87): Modules\\Core\\Providers\\CoreServiceProvider->setLocalesConfigurations()
#24 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(896): Modules\\Core\\Providers\\CoreServiceProvider->register()
#25 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register(Object(Modules\\Core\\Providers\\CoreServiceProvider))
#26 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\nwidart\\laravel-modules\\src\\Laravel\\Module.php(33): Illuminate\\Foundation\\ProviderRepository->load(Array)
#27 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\nwidart\\laravel-modules\\src\\Module.php(269): Nwidart\\Modules\\Laravel\\Module->registerProviders()
#28 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\nwidart\\laravel-modules\\src\\FileRepository.php(321): Nwidart\\Modules\\Module->register()
#29 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\nwidart\\laravel-modules\\src\\Providers\\BootstrapServiceProvider.php(23): Nwidart\\Modules\\FileRepository->register()
#30 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(896): Nwidart\\Modules\\Providers\\BootstrapServiceProvider->register()
#31 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\nwidart\\laravel-modules\\src\\ModulesServiceProvider.php(31): Illuminate\\Foundation\\Application->register(Object(Nwidart\\Modules\\Providers\\BootstrapServiceProvider))
#32 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\nwidart\\laravel-modules\\src\\LaravelModulesServiceProvider.php(17): Nwidart\\Modules\\ModulesServiceProvider->registerModules()
#33 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Nwidart\\Modules\\LaravelModulesServiceProvider->boot()
#34 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#35 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#36 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#37 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#38 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#39 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1132): Illuminate\\Foundation\\Application->bootProvider(Object(Nwidart\\Modules\\LaravelModulesServiceProvider))
#40 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Nwidart\\Modules\\LaravelModulesServiceProvider), 'Nwidart\\\\Modules...')
#41 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): array_walk(Array, Object(Closure))
#42 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#43 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#44 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(187): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#45 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(171): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#46 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#47 C:\\Projects\\Web\\htdocs\\ssp4-v11\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#48 {main}
"} 
[2025-06-23 09:04:39] production.ERROR: SQLSTATE[HY000] [2002] A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond (Connection: mysqlfix, SQL: select * from `setting__settings` where `name` = core::locales limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond (Connection: mysqlfix, SQL: select * from `setting__settings` where `name` = core::locales limit 1) at C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(979): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(958): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#2 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(781): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#3 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#5 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(343): Illuminate\\Database\\Query\\Builder->get(Array)
#9 C:\\Projects\\Web\\htdocs\\ssp4-v11\\Modules\\Core\\Providers\\CoreServiceProvider.php(301): Illuminate\\Database\\Query\\Builder->first()
#10 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(427): Modules\\Core\\Providers\\CoreServiceProvider->Modules\\Core\\Providers\\{closure}()
#11 C:\\Projects\\Web\\htdocs\\ssp4-v11\\Modules\\Core\\Providers\\CoreServiceProvider.php(297): Illuminate\\Cache\\Repository->remember('asgard.locales', 120, Object(Closure))
#12 C:\\Projects\\Web\\htdocs\\ssp4-v11\\Modules\\Core\\Providers\\CoreServiceProvider.php(87): Modules\\Core\\Providers\\CoreServiceProvider->setLocalesConfigurations()
#13 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(896): Modules\\Core\\Providers\\CoreServiceProvider->register()
#14 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register(Object(Modules\\Core\\Providers\\CoreServiceProvider))
#15 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\nwidart\\laravel-modules\\src\\Laravel\\Module.php(33): Illuminate\\Foundation\\ProviderRepository->load(Array)
#16 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\nwidart\\laravel-modules\\src\\Module.php(269): Nwidart\\Modules\\Laravel\\Module->registerProviders()
#17 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\nwidart\\laravel-modules\\src\\FileRepository.php(321): Nwidart\\Modules\\Module->register()
#18 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\nwidart\\laravel-modules\\src\\Providers\\BootstrapServiceProvider.php(23): Nwidart\\Modules\\FileRepository->register()
#19 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(896): Nwidart\\Modules\\Providers\\BootstrapServiceProvider->register()
#20 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\nwidart\\laravel-modules\\src\\ModulesServiceProvider.php(31): Illuminate\\Foundation\\Application->register(Object(Nwidart\\Modules\\Providers\\BootstrapServiceProvider))
#21 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\nwidart\\laravel-modules\\src\\LaravelModulesServiceProvider.php(17): Nwidart\\Modules\\ModulesServiceProvider->registerModules()
#22 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Nwidart\\Modules\\LaravelModulesServiceProvider->boot()
#23 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#28 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1132): Illuminate\\Foundation\\Application->bootProvider(Object(Nwidart\\Modules\\LaravelModulesServiceProvider))
#29 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Nwidart\\Modules\\LaravelModulesServiceProvider), 'Nwidart\\\\Modules...')
#30 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): array_walk(Array, Object(Closure))
#31 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#32 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#33 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(187): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#34 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(171): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#35 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#36 C:\\Projects\\Web\\htdocs\\ssp4-v11\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#37 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond at C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:66)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(66): PDO->__construct('mysql:host=192....', 'suksapan_tc', Object(SensitiveParameterValue), Array)
#1 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(85): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=192....', 'suksapan_tc', Object(SensitiveParameterValue), Array)
#2 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(48): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection(Object(PDOException), 'mysql:host=192....', 'suksapan_tc', Object(SensitiveParameterValue), Array)
#3 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=192....', Array, Array)
#4 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func(Object(Closure))
#7 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1267): Illuminate\\Database\\Connection->getPdo()
#8 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(512): Illuminate\\Database\\Connection->getReadPdo()
#9 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect(true)
#10 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#11 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(979): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#12 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(958): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#13 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(781): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#14 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#15 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#16 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#17 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#18 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#19 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(343): Illuminate\\Database\\Query\\Builder->get(Array)
#20 C:\\Projects\\Web\\htdocs\\ssp4-v11\\Modules\\Core\\Providers\\CoreServiceProvider.php(301): Illuminate\\Database\\Query\\Builder->first()
#21 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(427): Modules\\Core\\Providers\\CoreServiceProvider->Modules\\Core\\Providers\\{closure}()
#22 C:\\Projects\\Web\\htdocs\\ssp4-v11\\Modules\\Core\\Providers\\CoreServiceProvider.php(297): Illuminate\\Cache\\Repository->remember('asgard.locales', 120, Object(Closure))
#23 C:\\Projects\\Web\\htdocs\\ssp4-v11\\Modules\\Core\\Providers\\CoreServiceProvider.php(87): Modules\\Core\\Providers\\CoreServiceProvider->setLocalesConfigurations()
#24 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(896): Modules\\Core\\Providers\\CoreServiceProvider->register()
#25 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register(Object(Modules\\Core\\Providers\\CoreServiceProvider))
#26 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\nwidart\\laravel-modules\\src\\Laravel\\Module.php(33): Illuminate\\Foundation\\ProviderRepository->load(Array)
#27 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\nwidart\\laravel-modules\\src\\Module.php(269): Nwidart\\Modules\\Laravel\\Module->registerProviders()
#28 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\nwidart\\laravel-modules\\src\\FileRepository.php(321): Nwidart\\Modules\\Module->register()
#29 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\nwidart\\laravel-modules\\src\\Providers\\BootstrapServiceProvider.php(23): Nwidart\\Modules\\FileRepository->register()
#30 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(896): Nwidart\\Modules\\Providers\\BootstrapServiceProvider->register()
#31 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\nwidart\\laravel-modules\\src\\ModulesServiceProvider.php(31): Illuminate\\Foundation\\Application->register(Object(Nwidart\\Modules\\Providers\\BootstrapServiceProvider))
#32 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\nwidart\\laravel-modules\\src\\LaravelModulesServiceProvider.php(17): Nwidart\\Modules\\ModulesServiceProvider->registerModules()
#33 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Nwidart\\Modules\\LaravelModulesServiceProvider->boot()
#34 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#35 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#36 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#37 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#38 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#39 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1132): Illuminate\\Foundation\\Application->bootProvider(Object(Nwidart\\Modules\\LaravelModulesServiceProvider))
#40 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Nwidart\\Modules\\LaravelModulesServiceProvider), 'Nwidart\\\\Modules...')
#41 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): array_walk(Array, Object(Closure))
#42 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#43 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#44 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(187): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#45 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(171): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#46 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#47 C:\\Projects\\Web\\htdocs\\ssp4-v11\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#48 {main}
"} 
[2025-06-23 09:06:37] local.ERROR: Route [user.download.list] not defined. {"view":{"view":"C:\\Projects\\Web\\htdocs\\ssp4-v11\\Themes\\Multikart\\views\\home.blade.php","data":{"alternate":"<pre class=sf-dump id=sf-dump-1124741335 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>
  \"<span class=sf-dump-key>th</span>\" => \"<span class=sf-dump-str title=\"4 characters\">home</span>\"
</samp>]
</pre><script>Sfdump(\"sf-dump-1124741335\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","errors":"<pre class=sf-dump id=sf-dump-1013068357 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#2005</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-1013068357\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","page":"<pre class=sf-dump id=sf-dump-1779589263 data-indent-pad=\"  \"><span class=sf-dump-note>Modules\\Page\\Entities\\Page</span> {<a class=sf-dump-ref>#2016</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"8 characters\">mysqlfix</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"11 characters\">page__pages</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>is_home</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>template</span>\" => \"<span class=sf-dump-str title=\"4 characters\">home</span>\"
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2021-04-19 05:56:07</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2021-04-19 05:56:07</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>is_home</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>template</span>\" => \"<span class=sf-dump-str title=\"4 characters\">home</span>\"
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2021-04-19 05:56:07</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2021-04-19 05:56:07</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>is_home</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>translations</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#2019</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">is_home</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">template</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"7 characters\">page_id</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"5 characters\">title</span>\"
    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"
    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"
    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"4 characters\">body</span>\"
    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"10 characters\">meta_title</span>\"
    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"16 characters\">meta_description</span>\"
    <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"8 characters\">og_title</span>\"
    <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"14 characters\">og_description</span>\"
    <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"8 characters\">og_image</span>\"
    <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"7 characters\">og_type</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
  </samp>]
  +<span class=sf-dump-public title=\"Public property\">translatedAttributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">page_id</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"5 characters\">title</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"
    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"4 characters\">body</span>\"
    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"10 characters\">meta_title</span>\"
    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"16 characters\">meta_description</span>\"
    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"8 characters\">og_title</span>\"
    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"14 characters\">og_description</span>\"
    <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"8 characters\">og_image</span>\"
    <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"7 characters\">og_type</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">defaultLocale</span>: <span class=sf-dump-const>null</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-1779589263\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","currentLocale":"<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"2 characters\">th</span>\"
</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","currentUser":"<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-const>null</span>
</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Route [user.download.list] not defined. at C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:517)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(853): Illuminate\\Routing\\UrlGenerator->route('user.download.l...', Array, true)
#1 C:\\Projects\\Web\\htdocs\\ssp4-v11\\app\\Support\\FormBuilder.php(36): route('user.download.l...', Array)
#2 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): App\\Support\\FormBuilder->open(Array)
#3 C:\\Projects\\Web\\htdocs\\ssp4-v11\\Themes\\Multikart\\views\\home.blade.php(71): Illuminate\\Support\\Facades\\Facade::__callStatic('open', Array)
#4 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Projects\\\\Web...')
#5 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#6 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Projects\\\\Web...', Array)
#7 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Projects\\\\Web...', Array)
#8 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#9 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#10 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#11 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#12 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#13 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(920): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#14 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#15 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#16 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#27 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#39 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#40 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#41 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#42 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 C:\\Projects\\Web\\htdocs\\ssp4-v11\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 {main}

[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [user.download.list] not defined. at C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:517)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(853): Illuminate\\Routing\\UrlGenerator->route('user.download.l...', Array, true)
#1 C:\\Projects\\Web\\htdocs\\ssp4-v11\\app\\Support\\FormBuilder.php(36): route('user.download.l...', Array)
#2 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): App\\Support\\FormBuilder->open(Array)
#3 C:\\Projects\\Web\\htdocs\\ssp4-v11\\storage\\framework\\views\\1120230c048fdd755ac7f2299ba93480.php(59): Illuminate\\Support\\Facades\\Facade::__callStatic('open', Array)
#4 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Projects\\\\Web...')
#5 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#6 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Projects\\\\Web...', Array)
#7 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Projects\\\\Web...', Array)
#8 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#9 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#10 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#11 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#12 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#13 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(920): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#14 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#15 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#16 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#27 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#39 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#40 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#41 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#42 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 C:\\Projects\\Web\\htdocs\\ssp4-v11\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 {main}
"} 
[2025-06-23 09:08:07] local.ERROR: Class Modules\User\Guards\Sentinel contains 1 abstract method and must therefore be declared abstract or implement the remaining methods (Illuminate\Contracts\Auth\Guard::hasUser) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Class Modules\\User\\Guards\\Sentinel contains 1 abstract method and must therefore be declared abstract or implement the remaining methods (Illuminate\\Contracts\\Auth\\Guard::hasUser) at C:\\Projects\\Web\\htdocs\\ssp4-v11\\Modules\\User\\Guards\\Sentinel.php:9)
[stacktrace]
#0 {main}
"} 
[2025-06-23 09:08:31] local.ERROR: include(C:\Projects\Web\htdocs\ssp4-v11\vendor\composer/../../Modules/User/Entities/Sentinel/User.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer/../../Modules/User/Entities/Sentinel/User.php): Failed to open stream: No such file or directory at C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer\\ClassLoader.php:576)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(C:\\\\Proj...', 'C:\\\\Projects\\\\Web...', 576)
#1 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer\\ClassLoader.php(576): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(C:\\\\Proj...', 'C:\\\\Projects\\\\Web...', 576)
#2 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer\\ClassLoader.php(576): include('C:\\\\Projects\\\\Web...')
#3 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\Projects\\\\Web...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('Modules\\\\User\\\\En...')
#5 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\cartalyst\\sentinel\\src\\Laravel\\SentinelServiceProvider.php(463): class_exists('Modules\\\\User\\\\En...')
#6 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\cartalyst\\sentinel\\src\\Laravel\\SentinelServiceProvider.php(47): Cartalyst\\Sentinel\\Laravel\\SentinelServiceProvider->setOverrides()
#7 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Cartalyst\\Sentinel\\Laravel\\SentinelServiceProvider->boot()
#8 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#11 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#12 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#13 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1132): Illuminate\\Foundation\\Application->bootProvider(Object(Cartalyst\\Sentinel\\Laravel\\SentinelServiceProvider))
#14 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Cartalyst\\Sentinel\\Laravel\\SentinelServiceProvider), 'Cartalyst\\\\Senti...')
#15 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): array_walk(Array, Object(Closure))
#16 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#17 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#18 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#19 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#20 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-06-23 09:08:31] local.ERROR: include(C:\Projects\Web\htdocs\ssp4-v11\vendor\composer/../../Modules/User/Entities/Sentinel/User.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer/../../Modules/User/Entities/Sentinel/User.php): Failed to open stream: No such file or directory at C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer\\ClassLoader.php:576)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(C:\\\\Proj...', 'C:\\\\Projects\\\\Web...', 576)
#1 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer\\ClassLoader.php(576): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(C:\\\\Proj...', 'C:\\\\Projects\\\\Web...', 576)
#2 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer\\ClassLoader.php(576): include('C:\\\\Projects\\\\Web...')
#3 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\Projects\\\\Web...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('Modules\\\\User\\\\En...')
#5 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\cartalyst\\sentinel\\src\\Laravel\\SentinelServiceProvider.php(463): class_exists('Modules\\\\User\\\\En...')
#6 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\cartalyst\\sentinel\\src\\Laravel\\SentinelServiceProvider.php(47): Cartalyst\\Sentinel\\Laravel\\SentinelServiceProvider->setOverrides()
#7 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Cartalyst\\Sentinel\\Laravel\\SentinelServiceProvider->boot()
#8 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#11 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#12 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#13 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1132): Illuminate\\Foundation\\Application->bootProvider(Object(Cartalyst\\Sentinel\\Laravel\\SentinelServiceProvider))
#14 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Cartalyst\\Sentinel\\Laravel\\SentinelServiceProvider), 'Cartalyst\\\\Senti...')
#15 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): array_walk(Array, Object(Closure))
#16 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#17 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#18 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#19 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#20 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-06-23 09:08:31] local.ERROR: include(C:\Projects\Web\htdocs\ssp4-v11\vendor\composer/../../Modules/User/Entities/Sentinel/User.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer/../../Modules/User/Entities/Sentinel/User.php): Failed to open stream: No such file or directory at C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer\\ClassLoader.php:576)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(C:\\\\Proj...', 'C:\\\\Projects\\\\Web...', 576)
#1 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer\\ClassLoader.php(576): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(C:\\\\Proj...', 'C:\\\\Projects\\\\Web...', 576)
#2 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer\\ClassLoader.php(576): include('C:\\\\Projects\\\\Web...')
#3 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\Projects\\\\Web...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('Modules\\\\User\\\\En...')
#5 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\cartalyst\\sentinel\\src\\Laravel\\SentinelServiceProvider.php(463): class_exists('Modules\\\\User\\\\En...')
#6 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\cartalyst\\sentinel\\src\\Laravel\\SentinelServiceProvider.php(47): Cartalyst\\Sentinel\\Laravel\\SentinelServiceProvider->setOverrides()
#7 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Cartalyst\\Sentinel\\Laravel\\SentinelServiceProvider->boot()
#8 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#11 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#12 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#13 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1132): Illuminate\\Foundation\\Application->bootProvider(Object(Cartalyst\\Sentinel\\Laravel\\SentinelServiceProvider))
#14 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Cartalyst\\Sentinel\\Laravel\\SentinelServiceProvider), 'Cartalyst\\\\Senti...')
#15 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): array_walk(Array, Object(Closure))
#16 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#17 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#18 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#19 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#20 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-06-23 09:08:31] local.ERROR: include(C:\Projects\Web\htdocs\ssp4-v11\vendor\composer/../../Modules/User/Entities/Sentinel/User.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer/../../Modules/User/Entities/Sentinel/User.php): Failed to open stream: No such file or directory at C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer\\ClassLoader.php:576)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(C:\\\\Proj...', 'C:\\\\Projects\\\\Web...', 576)
#1 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer\\ClassLoader.php(576): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(C:\\\\Proj...', 'C:\\\\Projects\\\\Web...', 576)
#2 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer\\ClassLoader.php(576): include('C:\\\\Projects\\\\Web...')
#3 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\Projects\\\\Web...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('Modules\\\\User\\\\En...')
#5 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\cartalyst\\sentinel\\src\\Laravel\\SentinelServiceProvider.php(463): class_exists('Modules\\\\User\\\\En...')
#6 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\cartalyst\\sentinel\\src\\Laravel\\SentinelServiceProvider.php(47): Cartalyst\\Sentinel\\Laravel\\SentinelServiceProvider->setOverrides()
#7 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Cartalyst\\Sentinel\\Laravel\\SentinelServiceProvider->boot()
#8 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#11 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#12 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#13 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1132): Illuminate\\Foundation\\Application->bootProvider(Object(Cartalyst\\Sentinel\\Laravel\\SentinelServiceProvider))
#14 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Cartalyst\\Sentinel\\Laravel\\SentinelServiceProvider), 'Cartalyst\\\\Senti...')
#15 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): array_walk(Array, Object(Closure))
#16 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#17 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#18 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#19 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#20 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-06-23 09:08:31] local.ERROR: include(C:\Projects\Web\htdocs\ssp4-v11\vendor\composer/../../Modules/User/Entities/Sentinel/User.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer/../../Modules/User/Entities/Sentinel/User.php): Failed to open stream: No such file or directory at C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer\\ClassLoader.php:576)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(C:\\\\Proj...', 'C:\\\\Projects\\\\Web...', 576)
#1 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer\\ClassLoader.php(576): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(C:\\\\Proj...', 'C:\\\\Projects\\\\Web...', 576)
#2 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer\\ClassLoader.php(576): include('C:\\\\Projects\\\\Web...')
#3 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\Projects\\\\Web...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('Modules\\\\User\\\\En...')
#5 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\cartalyst\\sentinel\\src\\Laravel\\SentinelServiceProvider.php(463): class_exists('Modules\\\\User\\\\En...')
#6 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\cartalyst\\sentinel\\src\\Laravel\\SentinelServiceProvider.php(47): Cartalyst\\Sentinel\\Laravel\\SentinelServiceProvider->setOverrides()
#7 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Cartalyst\\Sentinel\\Laravel\\SentinelServiceProvider->boot()
#8 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#11 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#12 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#13 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1132): Illuminate\\Foundation\\Application->bootProvider(Object(Cartalyst\\Sentinel\\Laravel\\SentinelServiceProvider))
#14 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Cartalyst\\Sentinel\\Laravel\\SentinelServiceProvider), 'Cartalyst\\\\Senti...')
#15 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): array_walk(Array, Object(Closure))
#16 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#17 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#18 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#19 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#20 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-06-23 09:08:31] local.ERROR: include(C:\Projects\Web\htdocs\ssp4-v11\vendor\composer/../../Modules/User/Entities/Sentinel/User.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer/../../Modules/User/Entities/Sentinel/User.php): Failed to open stream: No such file or directory at C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer\\ClassLoader.php:576)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(C:\\\\Proj...', 'C:\\\\Projects\\\\Web...', 576)
#1 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer\\ClassLoader.php(576): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(C:\\\\Proj...', 'C:\\\\Projects\\\\Web...', 576)
#2 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer\\ClassLoader.php(576): include('C:\\\\Projects\\\\Web...')
#3 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\Projects\\\\Web...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('Modules\\\\User\\\\En...')
#5 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\cartalyst\\sentinel\\src\\Laravel\\SentinelServiceProvider.php(463): class_exists('Modules\\\\User\\\\En...')
#6 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\cartalyst\\sentinel\\src\\Laravel\\SentinelServiceProvider.php(47): Cartalyst\\Sentinel\\Laravel\\SentinelServiceProvider->setOverrides()
#7 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Cartalyst\\Sentinel\\Laravel\\SentinelServiceProvider->boot()
#8 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#11 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#12 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#13 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1132): Illuminate\\Foundation\\Application->bootProvider(Object(Cartalyst\\Sentinel\\Laravel\\SentinelServiceProvider))
#14 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Cartalyst\\Sentinel\\Laravel\\SentinelServiceProvider), 'Cartalyst\\\\Senti...')
#15 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): array_walk(Array, Object(Closure))
#16 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#17 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#18 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#19 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#20 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-06-23 09:08:31] local.ERROR: include(C:\Projects\Web\htdocs\ssp4-v11\vendor\composer/../../Modules/User/Entities/Sentinel/User.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer/../../Modules/User/Entities/Sentinel/User.php): Failed to open stream: No such file or directory at C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer\\ClassLoader.php:576)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(C:\\\\Proj...', 'C:\\\\Projects\\\\Web...', 576)
#1 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer\\ClassLoader.php(576): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(C:\\\\Proj...', 'C:\\\\Projects\\\\Web...', 576)
#2 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer\\ClassLoader.php(576): include('C:\\\\Projects\\\\Web...')
#3 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\Projects\\\\Web...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('Modules\\\\User\\\\En...')
#5 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\cartalyst\\sentinel\\src\\Laravel\\SentinelServiceProvider.php(463): class_exists('Modules\\\\User\\\\En...')
#6 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\cartalyst\\sentinel\\src\\Laravel\\SentinelServiceProvider.php(47): Cartalyst\\Sentinel\\Laravel\\SentinelServiceProvider->setOverrides()
#7 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Cartalyst\\Sentinel\\Laravel\\SentinelServiceProvider->boot()
#8 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#11 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#12 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#13 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1132): Illuminate\\Foundation\\Application->bootProvider(Object(Cartalyst\\Sentinel\\Laravel\\SentinelServiceProvider))
#14 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Cartalyst\\Sentinel\\Laravel\\SentinelServiceProvider), 'Cartalyst\\\\Senti...')
#15 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): array_walk(Array, Object(Closure))
#16 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#17 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#18 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#19 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#20 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-06-23 09:08:31] local.ERROR: include(C:\Projects\Web\htdocs\ssp4-v11\vendor\composer/../../Modules/User/Entities/Sentinel/User.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer/../../Modules/User/Entities/Sentinel/User.php): Failed to open stream: No such file or directory at C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer\\ClassLoader.php:576)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(C:\\\\Proj...', 'C:\\\\Projects\\\\Web...', 576)
#1 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer\\ClassLoader.php(576): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(C:\\\\Proj...', 'C:\\\\Projects\\\\Web...', 576)
#2 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer\\ClassLoader.php(576): include('C:\\\\Projects\\\\Web...')
#3 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\Projects\\\\Web...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('Modules\\\\User\\\\En...')
#5 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\cartalyst\\sentinel\\src\\Laravel\\SentinelServiceProvider.php(463): class_exists('Modules\\\\User\\\\En...')
#6 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\cartalyst\\sentinel\\src\\Laravel\\SentinelServiceProvider.php(47): Cartalyst\\Sentinel\\Laravel\\SentinelServiceProvider->setOverrides()
#7 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Cartalyst\\Sentinel\\Laravel\\SentinelServiceProvider->boot()
#8 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#11 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#12 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#13 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1132): Illuminate\\Foundation\\Application->bootProvider(Object(Cartalyst\\Sentinel\\Laravel\\SentinelServiceProvider))
#14 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Cartalyst\\Sentinel\\Laravel\\SentinelServiceProvider), 'Cartalyst\\\\Senti...')
#15 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): array_walk(Array, Object(Closure))
#16 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#17 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#18 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#19 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#20 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-06-23 09:08:32] local.ERROR: include(C:\Projects\Web\htdocs\ssp4-v11\vendor\composer/../../Modules/User/Entities/Sentinel/User.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer/../../Modules/User/Entities/Sentinel/User.php): Failed to open stream: No such file or directory at C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer\\ClassLoader.php:576)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(C:\\\\Proj...', 'C:\\\\Projects\\\\Web...', 576)
#1 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer\\ClassLoader.php(576): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(C:\\\\Proj...', 'C:\\\\Projects\\\\Web...', 576)
#2 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer\\ClassLoader.php(576): include('C:\\\\Projects\\\\Web...')
#3 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\Projects\\\\Web...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('Modules\\\\User\\\\En...')
#5 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\cartalyst\\sentinel\\src\\Laravel\\SentinelServiceProvider.php(463): class_exists('Modules\\\\User\\\\En...')
#6 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\cartalyst\\sentinel\\src\\Laravel\\SentinelServiceProvider.php(47): Cartalyst\\Sentinel\\Laravel\\SentinelServiceProvider->setOverrides()
#7 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Cartalyst\\Sentinel\\Laravel\\SentinelServiceProvider->boot()
#8 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#11 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#12 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#13 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1132): Illuminate\\Foundation\\Application->bootProvider(Object(Cartalyst\\Sentinel\\Laravel\\SentinelServiceProvider))
#14 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Cartalyst\\Sentinel\\Laravel\\SentinelServiceProvider), 'Cartalyst\\\\Senti...')
#15 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): array_walk(Array, Object(Closure))
#16 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#17 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#18 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#19 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#20 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-06-23 09:08:32] local.ERROR: include(C:\Projects\Web\htdocs\ssp4-v11\vendor\composer/../../Modules/User/Entities/Sentinel/User.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer/../../Modules/User/Entities/Sentinel/User.php): Failed to open stream: No such file or directory at C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer\\ClassLoader.php:576)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(C:\\\\Proj...', 'C:\\\\Projects\\\\Web...', 576)
#1 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer\\ClassLoader.php(576): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(C:\\\\Proj...', 'C:\\\\Projects\\\\Web...', 576)
#2 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer\\ClassLoader.php(576): include('C:\\\\Projects\\\\Web...')
#3 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\Projects\\\\Web...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('Modules\\\\User\\\\En...')
#5 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\cartalyst\\sentinel\\src\\Laravel\\SentinelServiceProvider.php(463): class_exists('Modules\\\\User\\\\En...')
#6 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\cartalyst\\sentinel\\src\\Laravel\\SentinelServiceProvider.php(47): Cartalyst\\Sentinel\\Laravel\\SentinelServiceProvider->setOverrides()
#7 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Cartalyst\\Sentinel\\Laravel\\SentinelServiceProvider->boot()
#8 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#11 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#12 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#13 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1132): Illuminate\\Foundation\\Application->bootProvider(Object(Cartalyst\\Sentinel\\Laravel\\SentinelServiceProvider))
#14 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Cartalyst\\Sentinel\\Laravel\\SentinelServiceProvider), 'Cartalyst\\\\Senti...')
#15 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): array_walk(Array, Object(Closure))
#16 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#17 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#18 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#19 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#20 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-06-23 09:08:32] local.ERROR: include(C:\Projects\Web\htdocs\ssp4-v11\vendor\composer/../../Modules/User/Entities/Sentinel/User.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer/../../Modules/User/Entities/Sentinel/User.php): Failed to open stream: No such file or directory at C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer\\ClassLoader.php:576)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(C:\\\\Proj...', 'C:\\\\Projects\\\\Web...', 576)
#1 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer\\ClassLoader.php(576): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(C:\\\\Proj...', 'C:\\\\Projects\\\\Web...', 576)
#2 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer\\ClassLoader.php(576): include('C:\\\\Projects\\\\Web...')
#3 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\Projects\\\\Web...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('Modules\\\\User\\\\En...')
#5 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\cartalyst\\sentinel\\src\\Laravel\\SentinelServiceProvider.php(463): class_exists('Modules\\\\User\\\\En...')
#6 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\cartalyst\\sentinel\\src\\Laravel\\SentinelServiceProvider.php(47): Cartalyst\\Sentinel\\Laravel\\SentinelServiceProvider->setOverrides()
#7 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Cartalyst\\Sentinel\\Laravel\\SentinelServiceProvider->boot()
#8 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#11 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#12 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#13 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1132): Illuminate\\Foundation\\Application->bootProvider(Object(Cartalyst\\Sentinel\\Laravel\\SentinelServiceProvider))
#14 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Cartalyst\\Sentinel\\Laravel\\SentinelServiceProvider), 'Cartalyst\\\\Senti...')
#15 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): array_walk(Array, Object(Closure))
#16 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#17 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#18 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#19 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#20 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-06-23 09:08:32] local.ERROR: include(C:\Projects\Web\htdocs\ssp4-v11\vendor\composer/../../Modules/User/Entities/Sentinel/User.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer/../../Modules/User/Entities/Sentinel/User.php): Failed to open stream: No such file or directory at C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer\\ClassLoader.php:576)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(C:\\\\Proj...', 'C:\\\\Projects\\\\Web...', 576)
#1 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer\\ClassLoader.php(576): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(C:\\\\Proj...', 'C:\\\\Projects\\\\Web...', 576)
#2 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer\\ClassLoader.php(576): include('C:\\\\Projects\\\\Web...')
#3 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\Projects\\\\Web...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('Modules\\\\User\\\\En...')
#5 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\cartalyst\\sentinel\\src\\Laravel\\SentinelServiceProvider.php(463): class_exists('Modules\\\\User\\\\En...')
#6 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\cartalyst\\sentinel\\src\\Laravel\\SentinelServiceProvider.php(47): Cartalyst\\Sentinel\\Laravel\\SentinelServiceProvider->setOverrides()
#7 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Cartalyst\\Sentinel\\Laravel\\SentinelServiceProvider->boot()
#8 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#11 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#12 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#13 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1132): Illuminate\\Foundation\\Application->bootProvider(Object(Cartalyst\\Sentinel\\Laravel\\SentinelServiceProvider))
#14 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Cartalyst\\Sentinel\\Laravel\\SentinelServiceProvider), 'Cartalyst\\\\Senti...')
#15 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): array_walk(Array, Object(Closure))
#16 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#17 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#18 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#19 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#20 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-06-23 09:11:37] local.ERROR: Class Modules\User\Guards\Sentinel contains 1 abstract method and must therefore be declared abstract or implement the remaining methods (Illuminate\Contracts\Auth\Guard::hasUser) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Class Modules\\User\\Guards\\Sentinel contains 1 abstract method and must therefore be declared abstract or implement the remaining methods (Illuminate\\Contracts\\Auth\\Guard::hasUser) at C:\\Projects\\Web\\htdocs\\ssp4-v11\\Modules\\User\\Guards\\Sentinel.php:9)
[stacktrace]
#0 {main}
"} 
[2025-06-23 09:25:23] local.ERROR: Class Modules\User\Guards\Sentinel contains 1 abstract method and must therefore be declared abstract or implement the remaining methods (Illuminate\Contracts\Auth\Guard::hasUser) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Class Modules\\User\\Guards\\Sentinel contains 1 abstract method and must therefore be declared abstract or implement the remaining methods (Illuminate\\Contracts\\Auth\\Guard::hasUser) at C:\\Projects\\Web\\htdocs\\ssp4-v11\\Modules\\User\\Guards\\Sentinel.php:9)
[stacktrace]
#0 {main}
"} 
[2025-06-23 09:27:36] local.ERROR: Route [user.download.list] not defined. {"view":{"view":"C:\\Projects\\Web\\htdocs\\ssp4-v11\\Themes\\Multikart\\views\\home.blade.php","data":{"alternate":"<pre class=sf-dump id=sf-dump-962798074 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>
  \"<span class=sf-dump-key>th</span>\" => \"<span class=sf-dump-str title=\"4 characters\">home</span>\"
</samp>]
</pre><script>Sfdump(\"sf-dump-962798074\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","errors":"<pre class=sf-dump id=sf-dump-767099008 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#1967</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-767099008\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","page":"<pre class=sf-dump id=sf-dump-1995517990 data-indent-pad=\"  \"><span class=sf-dump-note>Modules\\Page\\Entities\\Page</span> {<a class=sf-dump-ref>#1978</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"8 characters\">mysqlfix</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"11 characters\">page__pages</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>is_home</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>template</span>\" => \"<span class=sf-dump-str title=\"4 characters\">home</span>\"
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2021-04-19 05:56:07</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2021-04-19 05:56:07</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>is_home</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>template</span>\" => \"<span class=sf-dump-str title=\"4 characters\">home</span>\"
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2021-04-19 05:56:07</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2021-04-19 05:56:07</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>is_home</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>translations</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#1981</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">is_home</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">template</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"7 characters\">page_id</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"5 characters\">title</span>\"
    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"
    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"
    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"4 characters\">body</span>\"
    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"10 characters\">meta_title</span>\"
    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"16 characters\">meta_description</span>\"
    <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"8 characters\">og_title</span>\"
    <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"14 characters\">og_description</span>\"
    <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"8 characters\">og_image</span>\"
    <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"7 characters\">og_type</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
  </samp>]
  +<span class=sf-dump-public title=\"Public property\">translatedAttributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">page_id</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"5 characters\">title</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"
    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"4 characters\">body</span>\"
    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"10 characters\">meta_title</span>\"
    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"16 characters\">meta_description</span>\"
    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"8 characters\">og_title</span>\"
    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"14 characters\">og_description</span>\"
    <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"8 characters\">og_image</span>\"
    <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"7 characters\">og_type</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">defaultLocale</span>: <span class=sf-dump-const>null</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-1995517990\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","currentLocale":"<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"2 characters\">th</span>\"
</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","currentUser":"<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-const>null</span>
</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Route [user.download.list] not defined. at C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:517)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(853): Illuminate\\Routing\\UrlGenerator->route('user.download.l...', Array, true)
#1 C:\\Projects\\Web\\htdocs\\ssp4-v11\\app\\Support\\FormBuilder.php(36): route('user.download.l...', Array)
#2 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): App\\Support\\FormBuilder->open(Array)
#3 C:\\Projects\\Web\\htdocs\\ssp4-v11\\Themes\\Multikart\\views\\home.blade.php(71): Illuminate\\Support\\Facades\\Facade::__callStatic('open', Array)
#4 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Projects\\\\Web...')
#5 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#6 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Projects\\\\Web...', Array)
#7 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Projects\\\\Web...', Array)
#8 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#9 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#10 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#11 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#12 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#13 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(920): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#14 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#15 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#16 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#27 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#39 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#40 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#41 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#42 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 C:\\Projects\\Web\\htdocs\\ssp4-v11\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 {main}

[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [user.download.list] not defined. at C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:517)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(853): Illuminate\\Routing\\UrlGenerator->route('user.download.l...', Array, true)
#1 C:\\Projects\\Web\\htdocs\\ssp4-v11\\app\\Support\\FormBuilder.php(36): route('user.download.l...', Array)
#2 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): App\\Support\\FormBuilder->open(Array)
#3 C:\\Projects\\Web\\htdocs\\ssp4-v11\\storage\\framework\\views\\1120230c048fdd755ac7f2299ba93480.php(59): Illuminate\\Support\\Facades\\Facade::__callStatic('open', Array)
#4 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Projects\\\\Web...')
#5 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#6 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Projects\\\\Web...', Array)
#7 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Projects\\\\Web...', Array)
#8 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#9 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#10 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#11 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#12 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#13 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(920): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#14 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#15 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#16 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#27 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#39 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#40 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#41 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#42 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 C:\\Projects\\Web\\htdocs\\ssp4-v11\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 {main}
"} 
[2025-06-23 09:29:10] local.ERROR: Route [user.download.list] not defined. {"view":{"view":"C:\\Projects\\Web\\htdocs\\ssp4-v11\\Themes\\Multikart\\views\\home.blade.php","data":{"alternate":"<pre class=sf-dump id=sf-dump-318888967 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>
  \"<span class=sf-dump-key>th</span>\" => \"<span class=sf-dump-str title=\"4 characters\">home</span>\"
</samp>]
</pre><script>Sfdump(\"sf-dump-318888967\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","errors":"<pre class=sf-dump id=sf-dump-608429104 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#1967</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-608429104\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","page":"<pre class=sf-dump id=sf-dump-2108420525 data-indent-pad=\"  \"><span class=sf-dump-note>Modules\\Page\\Entities\\Page</span> {<a class=sf-dump-ref>#1978</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"8 characters\">mysqlfix</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"11 characters\">page__pages</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>is_home</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>template</span>\" => \"<span class=sf-dump-str title=\"4 characters\">home</span>\"
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2021-04-19 05:56:07</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2021-04-19 05:56:07</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>is_home</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>template</span>\" => \"<span class=sf-dump-str title=\"4 characters\">home</span>\"
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2021-04-19 05:56:07</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2021-04-19 05:56:07</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>is_home</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>translations</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#1981</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">is_home</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">template</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"7 characters\">page_id</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"5 characters\">title</span>\"
    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"
    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"
    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"4 characters\">body</span>\"
    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"10 characters\">meta_title</span>\"
    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"16 characters\">meta_description</span>\"
    <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"8 characters\">og_title</span>\"
    <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"14 characters\">og_description</span>\"
    <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"8 characters\">og_image</span>\"
    <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"7 characters\">og_type</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
  </samp>]
  +<span class=sf-dump-public title=\"Public property\">translatedAttributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">page_id</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"5 characters\">title</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"
    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"4 characters\">body</span>\"
    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"10 characters\">meta_title</span>\"
    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"16 characters\">meta_description</span>\"
    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"8 characters\">og_title</span>\"
    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"14 characters\">og_description</span>\"
    <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"8 characters\">og_image</span>\"
    <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"7 characters\">og_type</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">defaultLocale</span>: <span class=sf-dump-const>null</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-2108420525\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","currentLocale":"<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"2 characters\">th</span>\"
</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","currentUser":"<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-const>null</span>
</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Route [user.download.list] not defined. at C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:517)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(853): Illuminate\\Routing\\UrlGenerator->route('user.download.l...', Array, true)
#1 C:\\Projects\\Web\\htdocs\\ssp4-v11\\app\\Support\\FormBuilder.php(36): route('user.download.l...', Array)
#2 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): App\\Support\\FormBuilder->open(Array)
#3 C:\\Projects\\Web\\htdocs\\ssp4-v11\\Themes\\Multikart\\views\\home.blade.php(57): Illuminate\\Support\\Facades\\Facade::__callStatic('open', Array)
#4 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Projects\\\\Web...')
#5 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#6 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Projects\\\\Web...', Array)
#7 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Projects\\\\Web...', Array)
#8 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#9 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#10 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#11 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#12 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#13 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(920): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#14 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#15 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#16 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#27 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#39 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#40 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#41 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#42 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 C:\\Projects\\Web\\htdocs\\ssp4-v11\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 {main}

[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [user.download.list] not defined. at C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:517)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(853): Illuminate\\Routing\\UrlGenerator->route('user.download.l...', Array, true)
#1 C:\\Projects\\Web\\htdocs\\ssp4-v11\\app\\Support\\FormBuilder.php(36): route('user.download.l...', Array)
#2 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): App\\Support\\FormBuilder->open(Array)
#3 C:\\Projects\\Web\\htdocs\\ssp4-v11\\storage\\framework\\views\\1120230c048fdd755ac7f2299ba93480.php(59): Illuminate\\Support\\Facades\\Facade::__callStatic('open', Array)
#4 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Projects\\\\Web...')
#5 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#6 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Projects\\\\Web...', Array)
#7 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Projects\\\\Web...', Array)
#8 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#9 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#10 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#11 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#12 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#13 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(920): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#14 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#15 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#16 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#27 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#39 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#40 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#41 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#42 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 C:\\Projects\\Web\\htdocs\\ssp4-v11\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 {main}
"} 
[2025-06-23 09:29:20] local.ERROR: Route [user.download.list] not defined. {"view":{"view":"C:\\Projects\\Web\\htdocs\\ssp4-v11\\Themes\\Multikart\\views\\home.blade.php","data":{"alternate":"<pre class=sf-dump id=sf-dump-1263674208 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>
  \"<span class=sf-dump-key>th</span>\" => \"<span class=sf-dump-str title=\"4 characters\">home</span>\"
</samp>]
</pre><script>Sfdump(\"sf-dump-1263674208\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","errors":"<pre class=sf-dump id=sf-dump-1113372963 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#1967</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-1113372963\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","page":"<pre class=sf-dump id=sf-dump-68045753 data-indent-pad=\"  \"><span class=sf-dump-note>Modules\\Page\\Entities\\Page</span> {<a class=sf-dump-ref>#1978</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"8 characters\">mysqlfix</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"11 characters\">page__pages</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>is_home</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>template</span>\" => \"<span class=sf-dump-str title=\"4 characters\">home</span>\"
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2021-04-19 05:56:07</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2021-04-19 05:56:07</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>is_home</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>template</span>\" => \"<span class=sf-dump-str title=\"4 characters\">home</span>\"
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2021-04-19 05:56:07</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2021-04-19 05:56:07</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>is_home</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>translations</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#1981</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">is_home</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">template</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"7 characters\">page_id</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"5 characters\">title</span>\"
    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"
    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"
    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"4 characters\">body</span>\"
    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"10 characters\">meta_title</span>\"
    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"16 characters\">meta_description</span>\"
    <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"8 characters\">og_title</span>\"
    <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"14 characters\">og_description</span>\"
    <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"8 characters\">og_image</span>\"
    <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"7 characters\">og_type</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
  </samp>]
  +<span class=sf-dump-public title=\"Public property\">translatedAttributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">page_id</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"5 characters\">title</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"
    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"4 characters\">body</span>\"
    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"10 characters\">meta_title</span>\"
    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"16 characters\">meta_description</span>\"
    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"8 characters\">og_title</span>\"
    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"14 characters\">og_description</span>\"
    <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"8 characters\">og_image</span>\"
    <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"7 characters\">og_type</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">defaultLocale</span>: <span class=sf-dump-const>null</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-68045753\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","currentLocale":"<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"2 characters\">th</span>\"
</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","currentUser":"<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-const>null</span>
</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Route [user.download.list] not defined. at C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:517)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(853): Illuminate\\Routing\\UrlGenerator->route('user.download.l...', Array, true)
#1 C:\\Projects\\Web\\htdocs\\ssp4-v11\\app\\Support\\FormBuilder.php(36): route('user.download.l...', Array)
#2 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): App\\Support\\FormBuilder->open(Array)
#3 C:\\Projects\\Web\\htdocs\\ssp4-v11\\Themes\\Multikart\\views\\home.blade.php(57): Illuminate\\Support\\Facades\\Facade::__callStatic('open', Array)
#4 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Projects\\\\Web...')
#5 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#6 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Projects\\\\Web...', Array)
#7 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Projects\\\\Web...', Array)
#8 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#9 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#10 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#11 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#12 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#13 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(920): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#14 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#15 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#16 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#27 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#39 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#40 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#41 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#42 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 C:\\Projects\\Web\\htdocs\\ssp4-v11\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 {main}

[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [user.download.list] not defined. at C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:517)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(853): Illuminate\\Routing\\UrlGenerator->route('user.download.l...', Array, true)
#1 C:\\Projects\\Web\\htdocs\\ssp4-v11\\app\\Support\\FormBuilder.php(36): route('user.download.l...', Array)
#2 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): App\\Support\\FormBuilder->open(Array)
#3 C:\\Projects\\Web\\htdocs\\ssp4-v11\\storage\\framework\\views\\1120230c048fdd755ac7f2299ba93480.php(59): Illuminate\\Support\\Facades\\Facade::__callStatic('open', Array)
#4 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Projects\\\\Web...')
#5 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#6 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Projects\\\\Web...', Array)
#7 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Projects\\\\Web...', Array)
#8 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#9 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#10 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#11 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#12 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#13 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(920): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#14 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#15 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#16 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#27 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#39 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#40 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#41 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#42 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 C:\\Projects\\Web\\htdocs\\ssp4-v11\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 {main}
"} 
[2025-06-23 09:33:17] local.ERROR: Route [user.download.list] not defined. {"view":{"view":"C:\\Projects\\Web\\htdocs\\ssp4-v11\\Themes\\Multikart\\views\\home.blade.php","data":{"alternate":"<pre class=sf-dump id=sf-dump-805747126 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>
  \"<span class=sf-dump-key>th</span>\" => \"<span class=sf-dump-str title=\"4 characters\">home</span>\"
</samp>]
</pre><script>Sfdump(\"sf-dump-805747126\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","errors":"<pre class=sf-dump id=sf-dump-819558838 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#1966</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-819558838\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","page":"<pre class=sf-dump id=sf-dump-1980996876 data-indent-pad=\"  \"><span class=sf-dump-note>Modules\\Page\\Entities\\Page</span> {<a class=sf-dump-ref>#1986</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"8 characters\">mysqlfix</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"11 characters\">page__pages</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>is_home</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>template</span>\" => \"<span class=sf-dump-str title=\"4 characters\">home</span>\"
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2021-04-19 05:56:07</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2021-04-19 05:56:07</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>is_home</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>template</span>\" => \"<span class=sf-dump-str title=\"4 characters\">home</span>\"
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2021-04-19 05:56:07</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2021-04-19 05:56:07</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>is_home</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>translations</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#1989</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">is_home</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">template</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"7 characters\">page_id</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"5 characters\">title</span>\"
    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"
    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"
    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"4 characters\">body</span>\"
    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"10 characters\">meta_title</span>\"
    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"16 characters\">meta_description</span>\"
    <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"8 characters\">og_title</span>\"
    <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"14 characters\">og_description</span>\"
    <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"8 characters\">og_image</span>\"
    <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"7 characters\">og_type</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
  </samp>]
  +<span class=sf-dump-public title=\"Public property\">translatedAttributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">page_id</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"5 characters\">title</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"
    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"4 characters\">body</span>\"
    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"10 characters\">meta_title</span>\"
    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"16 characters\">meta_description</span>\"
    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"8 characters\">og_title</span>\"
    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"14 characters\">og_description</span>\"
    <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"8 characters\">og_image</span>\"
    <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"7 characters\">og_type</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">defaultLocale</span>: <span class=sf-dump-const>null</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-1980996876\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","currentLocale":"<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"2 characters\">th</span>\"
</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","currentUser":"<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-const>null</span>
</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Route [user.download.list] not defined. at C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:517)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(853): Illuminate\\Routing\\UrlGenerator->route('user.download.l...', Array, true)
#1 C:\\Projects\\Web\\htdocs\\ssp4-v11\\app\\Support\\FormBuilder.php(36): route('user.download.l...', Array)
#2 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): App\\Support\\FormBuilder->open(Array)
#3 C:\\Projects\\Web\\htdocs\\ssp4-v11\\Themes\\Multikart\\views\\home.blade.php(57): Illuminate\\Support\\Facades\\Facade::__callStatic('open', Array)
#4 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Projects\\\\Web...')
#5 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#6 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Projects\\\\Web...', Array)
#7 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Projects\\\\Web...', Array)
#8 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#9 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#10 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#11 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#12 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#13 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(920): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#14 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#15 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#16 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#27 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#39 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#40 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#41 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#42 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 C:\\Projects\\Web\\htdocs\\ssp4-v11\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 {main}

[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [user.download.list] not defined. at C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:517)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(853): Illuminate\\Routing\\UrlGenerator->route('user.download.l...', Array, true)
#1 C:\\Projects\\Web\\htdocs\\ssp4-v11\\app\\Support\\FormBuilder.php(36): route('user.download.l...', Array)
#2 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): App\\Support\\FormBuilder->open(Array)
#3 C:\\Projects\\Web\\htdocs\\ssp4-v11\\storage\\framework\\views\\1120230c048fdd755ac7f2299ba93480.php(59): Illuminate\\Support\\Facades\\Facade::__callStatic('open', Array)
#4 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Projects\\\\Web...')
#5 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#6 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Projects\\\\Web...', Array)
#7 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Projects\\\\Web...', Array)
#8 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#9 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#10 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#11 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#12 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#13 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(920): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#14 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#15 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#16 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#27 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#39 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#40 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#41 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#42 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 C:\\Projects\\Web\\htdocs\\ssp4-v11\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 {main}
"} 
[2025-06-23 09:35:37] local.ERROR: Route [user.download.list] not defined. {"view":{"view":"C:\\Projects\\Web\\htdocs\\ssp4-v11\\Themes\\Multikart\\views\\home.blade.php","data":{"alternate":"<pre class=sf-dump id=sf-dump-1520295691 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>
  \"<span class=sf-dump-key>th</span>\" => \"<span class=sf-dump-str title=\"4 characters\">home</span>\"
</samp>]
</pre><script>Sfdump(\"sf-dump-1520295691\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","errors":"<pre class=sf-dump id=sf-dump-523473155 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#1967</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-523473155\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","page":"<pre class=sf-dump id=sf-dump-1726697767 data-indent-pad=\"  \"><span class=sf-dump-note>Modules\\Page\\Entities\\Page</span> {<a class=sf-dump-ref>#1986</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"8 characters\">mysqlfix</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"11 characters\">page__pages</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>is_home</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>template</span>\" => \"<span class=sf-dump-str title=\"4 characters\">home</span>\"
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2021-04-19 05:56:07</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2021-04-19 05:56:07</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>is_home</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>template</span>\" => \"<span class=sf-dump-str title=\"4 characters\">home</span>\"
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2021-04-19 05:56:07</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2021-04-19 05:56:07</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>is_home</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>translations</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#1989</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">is_home</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">template</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"7 characters\">page_id</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"5 characters\">title</span>\"
    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"
    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"
    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"4 characters\">body</span>\"
    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"10 characters\">meta_title</span>\"
    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"16 characters\">meta_description</span>\"
    <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"8 characters\">og_title</span>\"
    <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"14 characters\">og_description</span>\"
    <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"8 characters\">og_image</span>\"
    <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"7 characters\">og_type</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
  </samp>]
  +<span class=sf-dump-public title=\"Public property\">translatedAttributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">page_id</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"5 characters\">title</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"
    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"4 characters\">body</span>\"
    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"10 characters\">meta_title</span>\"
    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"16 characters\">meta_description</span>\"
    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"8 characters\">og_title</span>\"
    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"14 characters\">og_description</span>\"
    <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"8 characters\">og_image</span>\"
    <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"7 characters\">og_type</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">defaultLocale</span>: <span class=sf-dump-const>null</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-1726697767\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","currentLocale":"<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"2 characters\">th</span>\"
</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","currentUser":"<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-const>null</span>
</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Route [user.download.list] not defined. at C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:517)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(853): Illuminate\\Routing\\UrlGenerator->route('user.download.l...', Array, true)
#1 C:\\Projects\\Web\\htdocs\\ssp4-v11\\app\\Support\\FormBuilder.php(36): route('user.download.l...', Array)
#2 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): App\\Support\\FormBuilder->open(Array)
#3 C:\\Projects\\Web\\htdocs\\ssp4-v11\\Themes\\Multikart\\views\\home.blade.php(57): Illuminate\\Support\\Facades\\Facade::__callStatic('open', Array)
#4 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Projects\\\\Web...')
#5 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#6 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Projects\\\\Web...', Array)
#7 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Projects\\\\Web...', Array)
#8 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#9 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#10 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#11 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#12 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#13 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(920): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#14 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#15 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#16 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#27 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#39 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#40 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#41 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#42 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 C:\\Projects\\Web\\htdocs\\ssp4-v11\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 {main}

[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [user.download.list] not defined. at C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:517)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(853): Illuminate\\Routing\\UrlGenerator->route('user.download.l...', Array, true)
#1 C:\\Projects\\Web\\htdocs\\ssp4-v11\\app\\Support\\FormBuilder.php(36): route('user.download.l...', Array)
#2 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): App\\Support\\FormBuilder->open(Array)
#3 C:\\Projects\\Web\\htdocs\\ssp4-v11\\storage\\framework\\views\\1120230c048fdd755ac7f2299ba93480.php(59): Illuminate\\Support\\Facades\\Facade::__callStatic('open', Array)
#4 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Projects\\\\Web...')
#5 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#6 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Projects\\\\Web...', Array)
#7 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Projects\\\\Web...', Array)
#8 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#9 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#10 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#11 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#12 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#13 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(920): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#14 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#15 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#16 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#27 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#39 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#40 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#41 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#42 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 C:\\Projects\\Web\\htdocs\\ssp4-v11\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 {main}
"} 
[2025-06-23 09:36:55] local.ERROR: Route [user.download.list] not defined. {"view":{"view":"C:\\Projects\\Web\\htdocs\\ssp4-v11\\Themes\\Multikart\\views\\home.blade.php","data":{"alternate":"<pre class=sf-dump id=sf-dump-1506210168 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>
  \"<span class=sf-dump-key>th</span>\" => \"<span class=sf-dump-str title=\"4 characters\">home</span>\"
</samp>]
</pre><script>Sfdump(\"sf-dump-1506210168\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","errors":"<pre class=sf-dump id=sf-dump-175884621 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#1965</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-175884621\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","page":"<pre class=sf-dump id=sf-dump-1569237235 data-indent-pad=\"  \"><span class=sf-dump-note>Modules\\Page\\Entities\\Page</span> {<a class=sf-dump-ref>#1985</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"8 characters\">mysqlfix</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"11 characters\">page__pages</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>is_home</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>template</span>\" => \"<span class=sf-dump-str title=\"4 characters\">home</span>\"
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2021-04-19 05:56:07</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2021-04-19 05:56:07</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>is_home</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>template</span>\" => \"<span class=sf-dump-str title=\"4 characters\">home</span>\"
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2021-04-19 05:56:07</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2021-04-19 05:56:07</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>is_home</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>translations</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#1988</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">is_home</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">template</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"7 characters\">page_id</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"5 characters\">title</span>\"
    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"
    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"
    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"4 characters\">body</span>\"
    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"10 characters\">meta_title</span>\"
    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"16 characters\">meta_description</span>\"
    <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"8 characters\">og_title</span>\"
    <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"14 characters\">og_description</span>\"
    <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"8 characters\">og_image</span>\"
    <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"7 characters\">og_type</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
  </samp>]
  +<span class=sf-dump-public title=\"Public property\">translatedAttributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">page_id</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"5 characters\">title</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"
    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"4 characters\">body</span>\"
    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"10 characters\">meta_title</span>\"
    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"16 characters\">meta_description</span>\"
    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"8 characters\">og_title</span>\"
    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"14 characters\">og_description</span>\"
    <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"8 characters\">og_image</span>\"
    <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"7 characters\">og_type</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">defaultLocale</span>: <span class=sf-dump-const>null</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-1569237235\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","currentLocale":"<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"2 characters\">th</span>\"
</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","currentUser":"<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-const>null</span>
</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Route [user.download.list] not defined. at C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:517)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(853): Illuminate\\Routing\\UrlGenerator->route('user.download.l...', Array, true)
#1 C:\\Projects\\Web\\htdocs\\ssp4-v11\\app\\Support\\FormBuilder.php(36): route('user.download.l...', Array)
#2 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): App\\Support\\FormBuilder->open(Array)
#3 C:\\Projects\\Web\\htdocs\\ssp4-v11\\Themes\\Multikart\\views\\home.blade.php(57): Illuminate\\Support\\Facades\\Facade::__callStatic('open', Array)
#4 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Projects\\\\Web...')
#5 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#6 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Projects\\\\Web...', Array)
#7 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Projects\\\\Web...', Array)
#8 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#9 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#10 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#11 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#12 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#13 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(920): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#14 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#15 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#16 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#27 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#39 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#40 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#41 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#42 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 C:\\Projects\\Web\\htdocs\\ssp4-v11\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 {main}

[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [user.download.list] not defined. at C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:517)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(853): Illuminate\\Routing\\UrlGenerator->route('user.download.l...', Array, true)
#1 C:\\Projects\\Web\\htdocs\\ssp4-v11\\app\\Support\\FormBuilder.php(36): route('user.download.l...', Array)
#2 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): App\\Support\\FormBuilder->open(Array)
#3 C:\\Projects\\Web\\htdocs\\ssp4-v11\\storage\\framework\\views\\1120230c048fdd755ac7f2299ba93480.php(59): Illuminate\\Support\\Facades\\Facade::__callStatic('open', Array)
#4 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Projects\\\\Web...')
#5 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#6 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Projects\\\\Web...', Array)
#7 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Projects\\\\Web...', Array)
#8 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#9 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#10 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#11 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#12 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#13 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(920): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#14 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#15 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#16 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#27 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#39 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#40 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#41 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#42 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 C:\\Projects\\Web\\htdocs\\ssp4-v11\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 {main}
"} 
[2025-06-23 09:38:29] local.ERROR: include(C:\Projects\Web\htdocs\ssp4-v11\vendor\composer/../../Modules/Translation/Providers/TranslationServiceProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer/../../Modules/Translation/Providers/TranslationServiceProvider.php): Failed to open stream: No such file or directory at C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer\\ClassLoader.php:576)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(C:\\\\Proj...', 'C:\\\\Projects\\\\Web...', 576)
#1 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer\\ClassLoader.php(576): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(C:\\\\Proj...', 'C:\\\\Projects\\\\Web...', 576)
#2 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer\\ClassLoader.php(576): include('C:\\\\Projects\\\\Web...')
#3 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\Projects\\\\Web...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('Modules\\\\Transla...')
#5 C:\\Projects\\Web\\htdocs\\ssp4-v11\\Modules\\Core\\Providers\\AsgardServiceProvider.php(15): class_exists('Modules\\\\Transla...')
#6 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(896): Modules\\Core\\Providers\\AsgardServiceProvider->register()
#7 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register(Object(Modules\\Core\\Providers\\AsgardServiceProvider))
#8 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(871): Illuminate\\Foundation\\ProviderRepository->load(Array)
#9 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(37): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#10 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#11 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#12 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#13 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#14 {main}
"} 
[2025-06-23 09:38:29] local.ERROR: Uncaught ReflectionException: Class "translator" does not exist in C:\Projects\Web\htdocs\ssp4-v11\vendor\laravel\framework\src\Illuminate\Container\Container.php:959
Stack trace:
#0 C:\Projects\Web\htdocs\ssp4-v11\vendor\laravel\framework\src\Illuminate\Container\Container.php(959): ReflectionClass->__construct('translator')
#1 C:\Projects\Web\htdocs\ssp4-v11\vendor\laravel\framework\src\Illuminate\Container\Container.php(832): Illuminate\Container\Container->build('translator')
#2 C:\Projects\Web\htdocs\ssp4-v11\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(1078): Illuminate\Container\Container->resolve('translator', Array, true)
#3 C:\Projects\Web\htdocs\ssp4-v11\vendor\laravel\framework\src\Illuminate\Container\Container.php(763): Illuminate\Foundation\Application->resolve('translator', Array)
#4 C:\Projects\Web\htdocs\ssp4-v11\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(1058): Illuminate\Container\Container->make('translator', Array)
#5 C:\Projects\Web\htdocs\ssp4-v11\vendor\laravel\framework\src\Illuminate\Foundation\helpers.php(124): Illuminate\Foundation\Application->make('translator', Array)
#6 Command line code(1): app('translator')
#7 {main}

Next Illuminate\Contracts\Container\BindingResolutionException: Target class [translator] does not exist. in C:\Projects\Web\htdocs\ssp4-v11\vendor\laravel\framework\src\Illuminate\Container\Container.php:961
Stack trace:
#0 C:\Projects\Web\htdocs\ssp4-v11\vendor\laravel\framework\src\Illuminate\Container\Container.php(832): Illuminate\Container\Container->build('translator')
#1 C:\Projects\Web\htdocs\ssp4-v11\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(1078): Illuminate\Container\Container->resolve('translator', Array, true)
#2 C:\Projects\Web\htdocs\ssp4-v11\vendor\laravel\framework\src\Illuminate\Container\Container.php(763): Illuminate\Foundation\Application->resolve('translator', Array)
#3 C:\Projects\Web\htdocs\ssp4-v11\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(1058): Illuminate\Container\Container->make('translator', Array)
#4 C:\Projects\Web\htdocs\ssp4-v11\vendor\laravel\framework\src\Illuminate\Foundation\helpers.php(124): Illuminate\Foundation\Application->make('translator', Array)
#5 Command line code(1): app('translator')
#6 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class \"translator\" does not exist in C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:959
Stack trace:
#0 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(959): ReflectionClass->__construct('translator')
#1 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(832): Illuminate\\Container\\Container->build('translator')
#2 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1078): Illuminate\\Container\\Container->resolve('translator', Array, true)
#3 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(763): Illuminate\\Foundation\\Application->resolve('translator', Array)
#4 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1058): Illuminate\\Container\\Container->make('translator', Array)
#5 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(124): Illuminate\\Foundation\\Application->make('translator', Array)
#6 Command line code(1): app('translator')
#7 {main}

Next Illuminate\\Contracts\\Container\\BindingResolutionException: Target class [translator] does not exist. in C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:961
Stack trace:
#0 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(832): Illuminate\\Container\\Container->build('translator')
#1 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1078): Illuminate\\Container\\Container->resolve('translator', Array, true)
#2 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(763): Illuminate\\Foundation\\Application->resolve('translator', Array)
#3 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1058): Illuminate\\Container\\Container->make('translator', Array)
#4 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(124): Illuminate\\Foundation\\Application->make('translator', Array)
#5 Command line code(1): app('translator')
#6 {main}
  thrown at C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:961)
[stacktrace]
#0 {main}
"} 
[2025-06-23 09:38:30] local.ERROR: include(C:\Projects\Web\htdocs\ssp4-v11\vendor\composer/../../Modules/Translation/Providers/TranslationServiceProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer/../../Modules/Translation/Providers/TranslationServiceProvider.php): Failed to open stream: No such file or directory at C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer\\ClassLoader.php:576)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(C:\\\\Proj...', 'C:\\\\Projects\\\\Web...', 576)
#1 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer\\ClassLoader.php(576): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(C:\\\\Proj...', 'C:\\\\Projects\\\\Web...', 576)
#2 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer\\ClassLoader.php(576): include('C:\\\\Projects\\\\Web...')
#3 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\Projects\\\\Web...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('Modules\\\\Transla...')
#5 C:\\Projects\\Web\\htdocs\\ssp4-v11\\Modules\\Core\\Providers\\AsgardServiceProvider.php(15): class_exists('Modules\\\\Transla...')
#6 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(896): Modules\\Core\\Providers\\AsgardServiceProvider->register()
#7 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register(Object(Modules\\Core\\Providers\\AsgardServiceProvider))
#8 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(871): Illuminate\\Foundation\\ProviderRepository->load(Array)
#9 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(37): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#10 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#11 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#12 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#13 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#14 {main}
"} 
[2025-06-23 09:49:04] local.ERROR: Class Modules\User\Guards\Sentinel contains 1 abstract method and must therefore be declared abstract or implement the remaining methods (Illuminate\Contracts\Auth\Guard::hasUser) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Class Modules\\User\\Guards\\Sentinel contains 1 abstract method and must therefore be declared abstract or implement the remaining methods (Illuminate\\Contracts\\Auth\\Guard::hasUser) at C:\\Projects\\Web\\htdocs\\ssp4-v11\\Modules\\User\\Guards\\Sentinel.php:9)
[stacktrace]
#0 {main}
"} 
[2025-06-23 09:49:34] local.ERROR: PHP Parse error: Syntax error, unexpected T_OBJECT_OPERATOR on line 1 {"exception":"[object] (Psy\\Exception\\ParseErrorException(code: 0): PHP Parse error: Syntax error, unexpected T_OBJECT_OPERATOR on line 1 at C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\psy\\psysh\\src\\Exception\\ParseErrorException.php:44)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\psy\\psysh\\src\\CodeCleaner.php(306): Psy\\Exception\\ParseErrorException::fromParseError(Object(PhpParser\\Error))
#1 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\psy\\psysh\\src\\CodeCleaner.php(240): Psy\\CodeCleaner->parse('<?php echo 'Tes...', false)
#2 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\psy\\psysh\\src\\Shell.php(852): Psy\\CodeCleaner->clean(Array, false)
#3 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\psy\\psysh\\src\\Shell.php(881): Psy\\Shell->addCode('echo 'Testing F...', true)
#4 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\psy\\psysh\\src\\Shell.php(1390): Psy\\Shell->setCode('echo 'Testing F...', true)
#5 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\tinker\\src\\Console\\TinkerCommand.php(76): Psy\\Shell->execute('echo 'Testing F...')
#6 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Tinker\\Console\\TinkerCommand->handle()
#7 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#8 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#9 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#10 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#11 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#12 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#13 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Laravel\\Tinker\\Console\\TinkerCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\Projects\\Web\\htdocs\\ssp4-v11\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 {main}
"} 
[2025-06-23 09:52:17] local.ERROR: Route [user.download.list] not defined. {"view":{"view":"C:\\Projects\\Web\\htdocs\\ssp4-v11\\Themes\\Multikart\\views\\home.blade.php","data":{"alternate":"<pre class=sf-dump id=sf-dump-1590563615 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>
  \"<span class=sf-dump-key>th</span>\" => \"<span class=sf-dump-str title=\"4 characters\">home</span>\"
</samp>]
</pre><script>Sfdump(\"sf-dump-1590563615\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","errors":"<pre class=sf-dump id=sf-dump-430900210 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#1961</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-430900210\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","page":"<pre class=sf-dump id=sf-dump-1860885851 data-indent-pad=\"  \"><span class=sf-dump-note>Modules\\Page\\Entities\\Page</span> {<a class=sf-dump-ref>#1981</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"8 characters\">mysqlfix</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"11 characters\">page__pages</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>is_home</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>template</span>\" => \"<span class=sf-dump-str title=\"4 characters\">home</span>\"
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2021-04-19 05:56:07</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2021-04-19 05:56:07</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>is_home</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>template</span>\" => \"<span class=sf-dump-str title=\"4 characters\">home</span>\"
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2021-04-19 05:56:07</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2021-04-19 05:56:07</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>is_home</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>translations</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#1984</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">is_home</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">template</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"7 characters\">page_id</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"5 characters\">title</span>\"
    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"
    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"
    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"4 characters\">body</span>\"
    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"10 characters\">meta_title</span>\"
    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"16 characters\">meta_description</span>\"
    <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"8 characters\">og_title</span>\"
    <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"14 characters\">og_description</span>\"
    <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"8 characters\">og_image</span>\"
    <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"7 characters\">og_type</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
  </samp>]
  +<span class=sf-dump-public title=\"Public property\">translatedAttributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">page_id</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"5 characters\">title</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"
    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"4 characters\">body</span>\"
    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"10 characters\">meta_title</span>\"
    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"16 characters\">meta_description</span>\"
    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"8 characters\">og_title</span>\"
    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"14 characters\">og_description</span>\"
    <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"8 characters\">og_image</span>\"
    <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"7 characters\">og_type</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">defaultLocale</span>: <span class=sf-dump-const>null</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-1860885851\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","currentLocale":"<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"2 characters\">th</span>\"
</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","currentUser":"<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-const>null</span>
</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Route [user.download.list] not defined. at C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:517)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(853): Illuminate\\Routing\\UrlGenerator->route('user.download.l...', Array, true)
#1 C:\\Projects\\Web\\htdocs\\ssp4-v11\\app\\Support\\FormBuilder.php(36): route('user.download.l...', Array)
#2 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): App\\Support\\FormBuilder->open(Array)
#3 C:\\Projects\\Web\\htdocs\\ssp4-v11\\Themes\\Multikart\\views\\home.blade.php(57): Illuminate\\Support\\Facades\\Facade::__callStatic('open', Array)
#4 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Projects\\\\Web...')
#5 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#6 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Projects\\\\Web...', Array)
#7 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Projects\\\\Web...', Array)
#8 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#9 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#10 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#11 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#12 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#13 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(920): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#14 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#15 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#16 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#27 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#39 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#40 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#41 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#42 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 C:\\Projects\\Web\\htdocs\\ssp4-v11\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 {main}

[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [user.download.list] not defined. at C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:517)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(853): Illuminate\\Routing\\UrlGenerator->route('user.download.l...', Array, true)
#1 C:\\Projects\\Web\\htdocs\\ssp4-v11\\app\\Support\\FormBuilder.php(36): route('user.download.l...', Array)
#2 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): App\\Support\\FormBuilder->open(Array)
#3 C:\\Projects\\Web\\htdocs\\ssp4-v11\\storage\\framework\\views\\1120230c048fdd755ac7f2299ba93480.php(59): Illuminate\\Support\\Facades\\Facade::__callStatic('open', Array)
#4 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Projects\\\\Web...')
#5 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#6 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Projects\\\\Web...', Array)
#7 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Projects\\\\Web...', Array)
#8 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#9 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#10 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#11 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#12 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#13 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(920): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#14 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#15 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#16 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#27 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Mcamara\\LaravelLocalization\\Middleware\\LaravelLocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\mcamara\\laravel-localization\\src\\Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Mcamara\\LaravelLocalization\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#39 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#40 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#41 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#42 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 C:\\Projects\\Web\\htdocs\\ssp4-v11\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 C:\\Projects\\Web\\htdocs\\ssp4-v11\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 {main}
"} 
