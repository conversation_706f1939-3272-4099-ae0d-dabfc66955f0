<?php

namespace App\Support;

use Spatie\Html\Html;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Traits\Macroable;

class FormBuilder
{
    use Macroable;

    protected $html;

    public function __construct(Html $html)
    {
        $this->html = $html;
    }

    /**
     * Open a form
     */
    public function open(array $options = [])
    {
        $method = $options['method'] ?? 'GET';
        $url = '';

        // Ensure method is a string
        if (!is_string($method)) {
            $method = 'GET';
        }

        // Handle route parameter
        if (isset($options['route'])) {
            if (is_array($options['route'])) {
                $url = route($options['route'][0], $options['route'][1] ?? []);
            } else {
                $url = route($options['route']);
            }
        } elseif (isset($options['url'])) {
            $url = $options['url'];
        }

        $attributes = [];

        if ($url) {
            $attributes['action'] = $url;
        }

        if (strtoupper($method) !== 'GET') {
            $attributes['method'] = 'POST';
        }

        if (isset($options['enctype'])) {
            $attributes['enctype'] = $options['enctype'];
        }

        if (isset($options['id'])) {
            $attributes['id'] = $options['id'];
        }

        if (isset($options['class'])) {
            $attributes['class'] = $options['class'];
        }

        // Start building the form HTML
        $html = '<form';
        foreach ($attributes as $key => $value) {
            if ($value !== null) {
                $html .= ' ' . $key . '="' . htmlspecialchars((string)$value) . '"';
            }
        }
        $html .= '>';

        // Add CSRF token for non-GET requests
        if (strtoupper($method) !== 'GET') {
            $html .= '<input type="hidden" name="_token" value="' . csrf_token() . '">';
        }

        // Add method spoofing for PUT, PATCH, DELETE
        $upperMethod = strtoupper($method);
        if (in_array($upperMethod, ['PUT', 'PATCH', 'DELETE'])) {
            $html .= '<input type="hidden" name="_method" value="' . $upperMethod . '">';
        }

        return new HtmlString($html);
    }

    /**
     * Close a form
     */
    public function close()
    {
        return new HtmlString('</form>');
    }

    /**
     * Create a text input
     */
    public function text($name, $value = null, array $options = [])
    {
        $attributes = array_merge(['name' => $name, 'type' => 'text'], $options);

        if ($value !== null) {
            $attributes['value'] = $value;
        } else {
            $attributes['value'] = old($name, '');
        }

        return new HtmlString($this->buildInput($attributes));
    }

    /**
     * Create a password input
     */
    public function password($name, array $options = [])
    {
        $attributes = array_merge(['name' => $name, 'type' => 'password'], $options);
        return new HtmlString($this->buildInput($attributes));
    }

    /**
     * Create a hidden input
     */
    public function hidden($name, $value = null, array $options = [])
    {
        $attributes = array_merge(['name' => $name, 'type' => 'hidden'], $options);

        if ($value !== null) {
            $attributes['value'] = $value;
        } else {
            $attributes['value'] = old($name, '');
        }

        return new HtmlString($this->buildInput($attributes));
    }

    /**
     * Create a textarea
     */
    public function textarea($name, $value = null, array $options = [])
    {
        $attributes = array_merge(['name' => $name], $options);

        if ($value === null) {
            $value = old($name, '');
        }

        return new HtmlString($this->buildTextarea($name, $value, $attributes));
    }

    /**
     * Create a select dropdown
     */
    public function select($name, $list = [], $selected = null, array $options = [])
    {
        $attributes = array_merge(['name' => $name], $options);

        // Ensure $list is an array
        if (!is_array($list)) {
            $list = [];
        }

        if ($selected === null) {
            $selected = old($name);
        }

        return new HtmlString($this->buildSelect($name, $list, $selected, $attributes));
    }

    /**
     * Create a label
     */
    public function label($for, $text = null, array $options = [])
    {
        $attributes = array_merge(['for' => $for], $options);
        $text = $text ?: $for;

        $html = '<label';
        foreach ($attributes as $key => $value) {
            if ($value !== null && $value !== false) {
                $html .= ' ' . $key . '="' . htmlspecialchars($value) . '"';
            }
        }
        $html .= '>' . htmlspecialchars($text) . '</label>';

        return new HtmlString($html);
    }

    /**
     * Create a submit button
     */
    public function submit($value = null, array $options = [])
    {
        $attributes = array_merge(['type' => 'submit'], $options);
        if ($value !== null) {
            $attributes['value'] = $value;
        }
        return new HtmlString($this->buildInput($attributes));
    }

    /**
     * Create a button
     */
    public function button($value = null, array $options = [])
    {
        $attributes = array_merge(['type' => 'button'], $options);

        $html = '<button';
        foreach ($attributes as $key => $val) {
            if ($val !== null && $val !== false) {
                $html .= ' ' . $key . '="' . htmlspecialchars($val) . '"';
            }
        }
        $html .= '>' . htmlspecialchars($value ?: 'Button') . '</button>';

        return new HtmlString($html);
    }

    /**
     * Create a checkbox
     */
    public function checkbox($name, $value = 1, $checked = null, array $options = [])
    {
        $attributes = array_merge(['name' => $name, 'type' => 'checkbox', 'value' => $value], $options);

        if ($checked === null) {
            $checked = old($name) == $value;
        }

        if ($checked) {
            $attributes['checked'] = true;
        }

        return new HtmlString($this->buildInput($attributes));
    }

    /**
     * Create a radio button
     */
    public function radio($name, $value = null, $checked = null, array $options = [])
    {
        $attributes = array_merge(['name' => $name, 'type' => 'radio', 'value' => $value], $options);

        if ($checked === null) {
            $checked = old($name) == $value;
        }

        if ($checked) {
            $attributes['checked'] = true;
        }

        return new HtmlString($this->buildInput($attributes));
    }

    /**
     * Create a file input
     */
    public function file($name, array $options = [])
    {
        $attributes = array_merge(['name' => $name, 'type' => 'file'], $options);
        return new HtmlString($this->buildInput($attributes));
    }

    /**
     * Create an email input
     */
    public function email($name, $value = null, array $options = [])
    {
        $attributes = array_merge(['name' => $name, 'type' => 'email'], $options);

        if ($value !== null) {
            $attributes['value'] = $value;
        } else {
            $attributes['value'] = old($name, '');
        }

        return new HtmlString($this->buildInput($attributes));
    }

    /**
     * Create a number input
     */
    public function number($name, $value = null, array $options = [])
    {
        $attributes = array_merge(['name' => $name, 'type' => 'number'], $options);

        if ($value !== null) {
            $attributes['value'] = $value;
        } else {
            $attributes['value'] = old($name, '');
        }

        return new HtmlString($this->buildInput($attributes));
    }

    /**
     * Create a date input
     */
    public function date($name, $value = null, array $options = [])
    {
        $attributes = array_merge(['name' => $name, 'type' => 'date'], $options);

        if ($value !== null) {
            $attributes['value'] = $value;
        } else {
            $attributes['value'] = old($name, '');
        }

        return new HtmlString($this->buildInput($attributes));
    }

    /**
     * Create a generic input of any type
     */
    public function input($type, $name, $value = null, array $options = [])
    {
        $attributes = array_merge(['name' => $name, 'type' => $type], $options);

        if ($value !== null) {
            $attributes['value'] = $value;
        } else {
            $attributes['value'] = old($name, '');
        }

        return new HtmlString($this->buildInput($attributes));
    }

    /**
     * Generate a script tag
     */
    public function script($url, array $attributes = [], $secure = null)
    {
        $attributes['src'] = $url;
        $attributes['type'] = $attributes['type'] ?? 'text/javascript';

        $html = '<script';
        foreach ($attributes as $key => $value) {
            if ($value !== null && $value !== false) {
                $html .= ' ' . $key . '="' . htmlspecialchars((string)$value) . '"';
            }
        }
        $html .= '></script>';

        return new HtmlString($html);
    }

    /**
     * Generate a style/CSS link tag
     */
    public function style($url, array $attributes = [], $secure = null)
    {
        $attributes['href'] = $url;
        $attributes['rel'] = $attributes['rel'] ?? 'stylesheet';
        $attributes['type'] = $attributes['type'] ?? 'text/css';

        $html = '<link';
        foreach ($attributes as $key => $value) {
            if ($value !== null && $value !== false) {
                $html .= ' ' . $key . '="' . htmlspecialchars((string)$value) . '"';
            }
        }
        $html .= '>';

        return new HtmlString($html);
    }

    /**
     * Generate a link tag
     */
    public function link($url, $title = null, array $attributes = [], $secure = null)
    {
        $attributes['href'] = $url;

        $html = '<a';
        foreach ($attributes as $key => $value) {
            if ($value !== null && $value !== false) {
                $html .= ' ' . $key . '="' . htmlspecialchars((string)$value) . '"';
            }
        }
        $html .= '>' . htmlspecialchars((string)($title ?: $url)) . '</a>';

        return new HtmlString($html);
    }

    /**
     * Generate an image tag
     */
    public function image($url, $alt = null, array $attributes = [], $secure = null)
    {
        $attributes['src'] = $url;
        if ($alt !== null) {
            $attributes['alt'] = $alt;
        }

        return new HtmlString($this->buildInput($attributes + ['type' => null]));
    }

    /**
     * Generate a link to an asset
     */
    public function linkAsset($url, $title = null, array $attributes = [], $secure = null)
    {
        return $this->link($url, $title, $attributes, $secure);
    }

    /**
     * Build an input HTML string
     */
    protected function buildInput(array $attributes)
    {
        $html = '<input';
        foreach ($attributes as $key => $value) {
            if ($value !== null && $value !== false) {
                if ($value === true) {
                    $html .= ' ' . $key;
                } else {
                    $html .= ' ' . $key . '="' . htmlspecialchars((string)$value) . '"';
                }
            }
        }
        $html .= '>';
        return $html;
    }

    /**
     * Build a textarea HTML string
     */
    protected function buildTextarea($name, $value, array $attributes)
    {
        $html = '<textarea';
        foreach ($attributes as $key => $val) {
            if ($val !== null && $val !== false) {
                if ($val === true) {
                    $html .= ' ' . $key;
                } else {
                    $html .= ' ' . $key . '="' . htmlspecialchars((string)$val) . '"';
                }
            }
        }
        $html .= '>' . htmlspecialchars((string)($value ?? '')) . '</textarea>';
        return $html;
    }

    /**
     * Build a select HTML string
     */
    protected function buildSelect($name, $list, $selected, array $attributes)
    {
        $html = '<select';
        foreach ($attributes as $key => $val) {
            if ($val !== null && $val !== false) {
                if ($val === true) {
                    $html .= ' ' . $key;
                } else {
                    $html .= ' ' . $key . '="' . htmlspecialchars((string)$val) . '"';
                }
            }
        }
        $html .= '>';

        // Ensure $list is an array
        if (is_array($list)) {
            foreach ($list as $value => $text) {
                $selectedAttr = ($value == $selected) ? ' selected' : '';
                $html .= '<option value="' . htmlspecialchars((string)$value) . '"' . $selectedAttr . '>' . htmlspecialchars((string)$text) . '</option>';
            }
        }

        $html .= '</select>';
        return $html;
    }

    /**
     * Handle dynamic method calls for any missing methods
     */
    public function __call($method, $parameters)
    {
        // Check if it's a macro first
        if (static::hasMacro($method)) {
            $macro = static::$macros[$method];

            if ($macro instanceof \Closure) {
                $macro = $macro->bindTo($this, static::class);
            }

            return $macro(...$parameters);
        }

        // Try to call the method on the Html instance
        if (method_exists($this->html, $method)) {
            return $this->html->$method(...$parameters);
        }

        throw new \BadMethodCallException("Method {$method} does not exist.");
    }
}
