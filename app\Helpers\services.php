<?php

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use \Modules\Post\Entities\Post;

if (!function_exists('_services')) {
    function _services($types, $where = null, $val = true)
    {
        $result = [];
        $services = collect(config("services"));
        foreach (Arr::wrap($types) as $type) {
            $s = $services->whereIn("type", Arr::wrap($type));
            $result[$type] = $where ? $s->where($where, "=", $val) : $s;
        }
        return $result;
    }
}
if (!function_exists('_services_connected')) {
    function _services_connected($types = null)
    {
        if (!$types) $types = ['social', 'marketplace'];
        $servicesActive = _services($types, "active", true);
        $servicesConnected = [];
        //        $dbServicesConnected = _settings_uid("core::services-%", null, [], true);
        $dbServicesConnected = optional(auth()->user()->oauth->keyBy("provider"))->toArray() ?? [];

        foreach ($servicesActive as $type => $services) {
            $servicesConnected[$type] = [];
            foreach ($services as $s => $service) {
                $servicesConnected[$type][$s] = [
                    'name' => $service['name'],
                    'icon' => $service['icon'],
                    'color' => $service['color'],
                    'connected_user' => $dbServicesConnected[$s] ?? null,
                    'connected_at' => isset($dbServicesConnected[$s]) ? $dbServicesConnected[$s]["updated_at"] : null
                    //                    'connected_user' =>$dbServicesConnected["services-{$s}.user"],
                    //                    'connected_pages' =>$dbServicesConnected["services-{$s}.pages"],
                ];
            }
        }
        return $servicesConnected;
    }
}
if (!function_exists('_services_submodule')) {
    function _services_submodule($type, $submodule = 'login')
    {
        // Enabled Module: login autopost leads
        return collect(config("services"))->where("type", "=", $type)->where("active", "=", true)->where("module_{$submodule}", "=", true);
    }
}

if (!function_exists('_services_setting_user')) {
    function _services_setting_user($provider, $user_id = false)
    {
        $user = $user_id ? \Modules\User\Entities\Sentinel\User::whereId($user_id)->with('oauth')->first() : auth()->user();
        if ($user)
            return optional($user->oauth->where("provider", $provider)->first());
        return optional([]);
    }
}
if (!function_exists('_services_setting')) {
    function _services_setting($provider, $name = null, $locale = null, $default = null, $array_get = null)
    {
        return _setting(_services_setting_prefix($provider) . $name, $locale, $default, $array_get);
    }
}
if (!function_exists('_services_setting_prefix')) {
    function _services_setting_prefix($provider)
    {
        $uid = auth()->id();
        return "uid{$uid}@core::services-{$provider}.";
    }
}
if (!function_exists('_social_api_service')) {
    function _social_api_service($provider)
    {
        return app('\Modules\Core\Services\Social\\' . Str::studly("{$provider}APIService"));
    }
}
if (!function_exists('_marketplace_api_service')) {
    function _marketplace_api_service($provider)
    {
        return app('\Modules\Core\Services\Marketplace\\' . Str::studly("{$provider}APIService"));
    }
}
if (!function_exists('_payment_api_service')) {
    function _payment_api_service($provider)
    {
        return app('\Modules\Core\Services\Payment\\' . Str::studly("{$provider}APIService"));
    }
}

if (!function_exists('_social')) {
    function _social($where = null, $val = true)
    {
        return _services('social', $where, $val);
    }
}
if (!function_exists('_social_link')) {
    function _social_link($type, $url)
    {
        //        return $url;
        if (!preg_match('#^(https?:)?//#', $url)) {
            $url = ltrim($url, '@');
            switch ($type) {
                case 'facebook':
                    $url = 'https://www.facebook.com/' . $url;
                    break;
                case 'pinterest':
                    $url = 'https://www.pinterest.com/' . $url;
                    break;
                case 'twitter':
                    $url = 'https://twitter.com/' . $url;
                    break;
                case 'googleplus':
                    if (is_numeric($url))
                        $url = 'http://www.google.com/profiles/' . $url;
                    else
                        $url = 'https://plus.google.com/u/0/+' . $url;
                    break;
                case 'line':
                    // Ref: https://developers.line.biz/en/docs/messaging-api/using-line-url-scheme/
                    $url = 'http://line.me/ti/p/~' . $url;
                    // http://line.me/ti/p/%40{LINE ID}
                    break;
            }
        }
        return $url;
    }
}

if (!function_exists('_post_social')) {
    function _post_social($post, $data)
    {
        $responses = [];
        $published_items = null;
        foreach (_array_wrap($data['post-social']) as $provider => $target_ids) {
            if ($target_ids && $target_ids != '') {
                $param = @call_user_func_array("_map_post_to_{$provider}", [$post, &$published_items]);
                if (is_array($param)) {
                    $response = _oauth_handler($provider, "post", [
                        'posts' => $param,
                        "target_ids" => explode(",", $target_ids),
                    ], [
                        'post_id' => $post->id,
                        'target_id' => $target_ids,
                        'published_items' => $published_items,
                    ]);
                    $responses[$provider] = $response;
                }
            }
        }
        return $responses;
    }
}
if (!function_exists('_post_marketplace')) {
    function _post_marketplace($post, $data)
    {
        $responses = [];
        $published_items = null;
        foreach (_array_wrap($data['post-marketplace']) as $provider => $target_ids) {
            if ($target_ids && $target_ids != '') {
                $param = @call_user_func_array("_map_stock_to_{$provider}", [$post, &$published_items]);
                if (is_array($param)) {
                    //                    $response = _oauth_dispatcher($provider, 'update_price_quantity', $param, [
                    $response = _oauth_handler($provider, 'update_price_quantity', $param, [
                        'log' => true,
                        'post_id' => $post->id,
                        'target_id' => $target_ids,
                        'published_items' => $published_items,
                    ]);
                    $responses[$provider] = $response;
                }
            }
        }
        return $responses;
    }
}

if (!function_exists('_lazada_product_create_or_update')) {
    /*
     * Make create/update on lazada marketplace
     * @param int $post
     *
     * */
    function _lazada_product_create_or_update($post, &$param = null)
    {
        if (!is_array($post)) {

            // if (is_a($post, 'Modules\Post\Entities\Post')) {
            //     $model = $post;
            // } else  $model = \Modules\Post\Entities\Post::find($post);

            // $param = optional(new \Modules\Post\Transformers\Marketplace\PostToLazadaTransformer($model))->toArray(request());

        } else $param = $post;

        $result = _oauth_handler('lazada', 'update_product', $param);
        if ($result['errors'] == 1)
            $result = _oauth_handler('lazada', 'create_product', $param);

        if ($result['errors'] == 1) {
            Log::info("_lazada_create_or_update user: " . auth()->user(), $result);
        }
        return $result; // result.data.detail => show list of data invalid [field, message, seller_sku, code]
    }
}
if (!function_exists('_lazada_product_data_from_url')) {
    /*
     * Get Data From Url Path String
     * https://www.lazada.co.th/{path} -> https://www.lazada.co.th/-i633052906-s1214210096.html
     * -i633052906-s1214210096.html -> -i{item_id}-s{SkuId}.html
     * @param string $url
     *
     * @return array
     * */
    function _lazada_product_data_from_url($url, $key = null)
    {
        preg_match("#-i([^-]+)-s([^\.]+)\.html#", $url, $matched); // 0 -> path, 1 -> item_id, 2 -> SkuId
        if ($key == 'path')
            return $matched[0];
        else if ($key == 'item_id')
            return $matched[1];
        else if ($key == 'sku_id')
            return $matched[2];
        return array_combine(['path', 'item_id', 'sku_id'], $matched);
    }
}
if (!function_exists('_shopee_product_data_from_url')) {
    /*
     * Get Data From Url Path String
     * format1  https://shopee.co.th/product/{path} -> https://shopee.co.th/product/237289339/4141846069
     * format1  https://shopee.co.th/product/{shop_id}/{item_id}
     * format2  https://shopee.co.th/เจลนวดหน้า-สูตรฟื้นฟูผิว-(Skin-Rejuvenation-Gel-5-in-1)-รวมทุกวิตมินในกระปุกเดียว-ใช้กับเครื่องนวดหน้า-i.222694003.7043938108
     * format2  https://shopee.co.th/{product-slug}-i.{shop_id}.{item_id}
     *
     * @param string $url
     *
     * @return array
     * */
    function _shopee_product_data_from_url($url, $key = null)
    {
        $format1 = preg_match("#/product/([^/]+)/([^/?]+)#", $url, $matched1); // 0 -> path, 1 -> shop_id, 2 -> item_id
        if ($format1) {
            if ($key == 'path')
                return $matched1[0];
            else if ($key == 'shop_id')
                return $matched1[1];
            else if ($key == 'item_id')
                return $matched1[2];
            return array_combine(['path', 'shop_id', 'item_id'], $matched1);
        } else {
            $format2 = preg_match("#/([^\/]+)-i\.(\d+)\.(\d+)#", $url, $matched2); // 0 -> path, 1 -> slug, 2 -> shop_id, 3 -> item_id
            if ($format2) {
                if ($key == 'path')
                    return $matched2[0];
                else if ($key == 'slug')
                    return $matched2[1];
                else if ($key == 'shop_id')
                    return $matched2[2];
                else if ($key == 'item_id')
                    return $matched2[3];
                return array_combine(['path', 'slug', 'shop_id', 'item_id'], $matched2);
            }
        }
        return false;
    }
}


if (!function_exists('_make_google_translate')) {
    /**
     * Google translate (Short text)
     * Example th => en
     *    source: "ทดสอบการแปล i'm developer"
     *    translated: "Translation test, i'm developer"
     *
     * @return array
     */
    function _make_google_translate($text, $source_lang = "th", $target_lang = "en")
    {
        $result = json_decode(_file_get_contents("https://translate.googleapis.com/translate_a/single?client=gtx&sl={$source_lang}&tl={$target_lang}&dt=t&q=" . urlencode($text)));

        return [
            'source' => $result[0][0][1],
            'translated' => $result[0][0][0]
        ];
    }
}
if (!function_exists('_get_youtube_id')) {
    function _get_youtube_id(&$url_or_id)
    {
        $is_youtube = false;
        if (preg_match('/^https:\/\/w{3}?.youtube.com\/embed\//', $url_or_id)) {
            $url = parse_url($url_or_id);
            $is_youtube = $url_or_id = end(explode('/', $url['path']));
        } elseif (preg_match('/^https:\/\/w{3}?.youtube.com\//', $url_or_id)) {
            $url = parse_url($url_or_id);
            if (is_array($url) && count($url) > 0 && isset($url['query']) && !empty($url['query'])) {
                $parts = explode('&', $url['query']);
                if (is_array($parts) && count($parts) > 0) {
                    foreach ($parts as $p) {
                        $pattern = '/^v\=/';

                        if (preg_match($pattern, $p)) {
                            $is_youtube = $url_or_id = preg_replace($pattern, '', $p);
                            break;
                        }
                    }
                }
                if (!$url_or_id) {
                    return view('errors.404', ['info' => '<p>No video id passed in</p>']);
                }
            } else {
                return view('errors.404', ['info' => '<p>Invalid url</p>']);
            }
        } elseif (preg_match('/^https?:\/\/youtu.be/', $url_or_id)) {
            $url = parse_url($url_or_id);
            $is_youtube = $url_or_id = preg_replace('/^\//', '', $url['path']);
        }
        return $is_youtube;
    }
}
