<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\VMMigrationService;

class AccessKeyCredentials extends \Google\Model
{
  /**
   * @var string
   */
  public $accessKeyId;
  /**
   * @var string
   */
  public $secretAccessKey;
  /**
   * @var string
   */
  public $sessionToken;

  /**
   * @param string
   */
  public function setAccessKeyId($accessKeyId)
  {
    $this->accessKeyId = $accessKeyId;
  }
  /**
   * @return string
   */
  public function getAccessKeyId()
  {
    return $this->accessKeyId;
  }
  /**
   * @param string
   */
  public function setSecretAccessKey($secretAccessKey)
  {
    $this->secretAccessKey = $secretAccessKey;
  }
  /**
   * @return string
   */
  public function getSecretAccessKey()
  {
    return $this->secretAccessKey;
  }
  /**
   * @param string
   */
  public function setSessionToken($sessionToken)
  {
    $this->sessionToken = $sessionToken;
  }
  /**
   * @return string
   */
  public function getSessionToken()
  {
    return $this->sessionToken;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(AccessKeyCredentials::class, 'Google_Service_VMMigrationService_AccessKeyCredentials');
