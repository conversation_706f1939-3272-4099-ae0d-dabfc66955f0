<?php

return [
    'contest.types' => [
        'boardgame' => 'contest::types.boardgame',
        'other' => 'contest::types.other', 
    ],
    'contest.contests' => [
        'index' => 'contest::contests.list resource',
        'create' => 'contest::contests.create resource',
        'edit' => 'contest::contests.edit resource',
        'destroy' => 'contest::contests.destroy resource',
    ],
    'contest.schedules' => [
        'index' => 'contest::schedules.list resource',
        'create' => 'contest::schedules.create resource',
        'edit' => 'contest::schedules.edit resource',
        'destroy' => 'contest::schedules.destroy resource',
    ],
    'contest.members' => [
        'index' => 'contest::members.list resource',
        'join' => 'contest::members.join',
        'certificate' => 'contest::members.get-certificate',
        'create' => 'contest::members.create resource',
        'edit' => 'contest::members.edit resource',
        'destroy' => 'contest::members.destroy resource',
    ],
// append





];
