# Laravel Menus

[![Latest Version on Packagist](https://img.shields.io/packagist/v/nwidart/laravel-menus.svg?style=flat-square)](https://packagist.org/packages/nwidart/laravel-menus)
[![Software License](https://img.shields.io/badge/license-MIT-brightgreen.svg?style=flat-square)](LICENSE.md)
[![Build Status](https://img.shields.io/travis/nWidart/laravel-menus/master.svg?style=flat-square)](https://travis-ci.org/nWidart/laravel-menus)
[![Scrutinizer Coverage](https://img.shields.io/scrutinizer/coverage/g/nWidart/laravel-menus.svg?style=flat-square)](https://scrutinizer-ci.com/g/nWidart/laravel-menus/?branch=master)
[![SensioLabsInsight](https://img.shields.io/sensiolabs/i/6b187410-e586-465f-a137-2d1fbf7ac724.svg?style=flat-square)](https://insight.sensiolabs.com/projects/6b187410-e586-465f-a137-2d1fbf7ac724)
[![Quality Score](https://img.shields.io/scrutinizer/g/nWidart/laravel-menus.svg?style=flat-square)](https://scrutinizer-ci.com/g/nWidart/laravel-menus)
[![Total Downloads](https://img.shields.io/packagist/dt/nwidart/laravel-menus.svg?style=flat-square)](https://packagist.org/packages/nwidart/laravel-menus)

| **Laravel**  |  **laravel-menus** |
|---|---|
| 5.4  | ^0.5  |
| 5.5  | ^1.0  |
| 5.6  | ^2.0  |
| 5.7  | ^3.0  |
| 5.8  | ^4.0  |
| 6.0  | ^5.0  |
| 8.0  | ^7.0  |

`nwidart/laravel-menus` is a laravel package which created to manage menus. It has a feature called presenters which enables easy styling and custom structure of menu rendering.

This package is a re-published, re-organised and maintained version of [pingpong/menus](https://github.com/pingpong-labs/menus), which isn't maintained anymore. This package is used in [AsgardCMS](https://asgardcms.com/).

With one big added bonus that the original package didn't have: **tests**.

## Documentation

You'll find installation instructions and full documentation on https://nwidart.com/laravel-menus/.

## Credits

- [Nicolas Widart](https://github.com/nwidart)
- [gravitano](https://github.com/gravitano)
- [All Contributors](../../contributors)

## License

The MIT License (MIT). Please see [License File](LICENSE.md) for more information.
