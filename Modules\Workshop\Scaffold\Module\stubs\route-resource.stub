    $router->bind('$LOWERCASE_CLASS_NAME$', function ($id) {
        return app('Modules\$MODULE_NAME$\Repositories\$CLASS_NAME$Repository')->find($id);
    });
    $router->get('$PLURAL_LOWERCASE_CLASS_NAME$', [
        'as' => 'admin.$LOWERCASE_MODULE_NAME$.$LOWERCASE_CLASS_NAME$.index',
        'uses' => '$CLASS_NAME$Controller@index',
        'middleware' => 'can:$LOWERCASE_MODULE_NAME$.$PLURAL_LOWERCASE_CLASS_NAME$.index'
    ]);
    $router->get('$PLURAL_LOWERCASE_CLASS_NAME$/create', [
        'as' => 'admin.$LOWERCASE_MODULE_NAME$.$LOWERCASE_CLASS_NAME$.create',
        'uses' => '$CLASS_NAME$Controller@create',
        'middleware' => 'can:$LOWERCASE_MODULE_NAME$.$PLURAL_LOWERCASE_CLASS_NAME$.create'
    ]);
    $router->post('$PLURAL_LOWERCASE_CLASS_NAME$', [
        'as' => 'admin.$LOWERCASE_MODULE_NAME$.$LOWERCASE_CLASS_NAME$.store',
        'uses' => '$CLASS_NAME$Controller@store',
        'middleware' => 'can:$LOWERCASE_MODULE_NAME$.$PLURAL_LOWERCASE_CLASS_NAME$.create'
    ]);
    $router->get('$PLURAL_LOWERCASE_CLASS_NAME$/{$LOWERCASE_CLASS_NAME$}/edit', [
        'as' => 'admin.$LOWERCASE_MODULE_NAME$.$LOWERCASE_CLASS_NAME$.edit',
        'uses' => '$CLASS_NAME$Controller@edit',
        'middleware' => 'can:$LOWERCASE_MODULE_NAME$.$PLURAL_LOWERCASE_CLASS_NAME$.edit'
    ]);
    $router->put('$PLURAL_LOWERCASE_CLASS_NAME$/{$LOWERCASE_CLASS_NAME$}', [
        'as' => 'admin.$LOWERCASE_MODULE_NAME$.$LOWERCASE_CLASS_NAME$.update',
        'uses' => '$CLASS_NAME$Controller@update',
        'middleware' => 'can:$LOWERCASE_MODULE_NAME$.$PLURAL_LOWERCASE_CLASS_NAME$.edit'
    ]);
    $router->delete('$PLURAL_LOWERCASE_CLASS_NAME$/{$LOWERCASE_CLASS_NAME$}', [
        'as' => 'admin.$LOWERCASE_MODULE_NAME$.$LOWERCASE_CLASS_NAME$.destroy',
        'uses' => '$CLASS_NAME$Controller@destroy',
        'middleware' => 'can:$LOWERCASE_MODULE_NAME$.$PLURAL_LOWERCASE_CLASS_NAME$.destroy'
    ]);
// append
