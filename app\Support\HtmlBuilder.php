<?php

namespace App\Support;

use Spatie\Html\Html;
use Illuminate\Support\HtmlString;

class HtmlBuilder
{
    protected $html;
    protected $formBuilder;

    public function __construct(Html $html, FormBuilder $formBuilder)
    {
        $this->html = $html;
        $this->formBuilder = $formBuilder;
    }

    /**
     * Generate a script tag
     */
    public function script($url, array $attributes = [], $secure = null)
    {
        return $this->formBuilder->script($url, $attributes, $secure);
    }

    /**
     * Generate a style/CSS link tag
     */
    public function style($url, array $attributes = [], $secure = null)
    {
        return $this->formBuilder->style($url, $attributes, $secure);
    }

    /**
     * Generate a link tag
     */
    public function link($url, $title = null, array $attributes = [], $secure = null)
    {
        return $this->formBuilder->link($url, $title, $attributes, $secure);
    }

    /**
     * Generate an image tag
     */
    public function image($url, $alt = null, array $attributes = [], $secure = null)
    {
        return $this->formBuilder->image($url, $alt, $attributes, $secure);
    }

    /**
     * Generate a link to an asset
     */
    public function linkAsset($url, $title = null, array $attributes = [], $secure = null)
    {
        return $this->formBuilder->linkAsset($url, $title, $attributes, $secure);
    }

    /**
     * Handle dynamic method calls - delegate to FormBuilder first, then Spatie\Html
     */
    public function __call($method, $parameters)
    {
        // Try FormBuilder first (for methods like script, style, etc.)
        if (method_exists($this->formBuilder, $method)) {
            return $this->formBuilder->$method(...$parameters);
        }

        // Try Spatie\Html for other methods
        if (method_exists($this->html, $method)) {
            return $this->html->$method(...$parameters);
        }

        throw new \BadMethodCallException("Method {$method} does not exist.");
    }
}
