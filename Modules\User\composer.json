{"name": "idavoll/user-module", "type": "asgard-module", "description": "User module for AsgardCMS. Handles the authentication and authorisation as well as the user management.", "keywords": ["asgardcms", "user", "authentication", "authorisation"], "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "support": {"email": "<EMAIL>", "issues": "https://github.com/AsgardCms/Platform/issues", "source": "https://github.com/AsgardCms/User"}, "autoload": {"psr-4": {"Modules\\User\\": ".", "Modules\\": "Mo<PERSON>les/"}}, "autoload-dev": {"psr-4": {"Modules\\User\\Tests\\": "Tests"}}, "extra": {"branch-alias": {"dev-master": "4.0.x-dev"}}, "minimum-stability": "dev", "prefer-stable": true}