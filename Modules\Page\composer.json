{"name": "idavoll/page-module", "type": "asgard-module", "description": "Page module for AsgardCMS. Page management.", "keywords": ["asgardcms", "pages"], "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "support": {"email": "<EMAIL>", "issues": "https://github.com/AsgardCms/Page/issues", "source": "https://github.com/AsgardCms/Page"}, "autoload-dev": {"psr-4": {"Modules\\Page\\": ".", "Modules\\": "Mo<PERSON>les/"}}, "extra": {"branch-alias": {"dev-master": "4.0.x-dev"}}, "minimum-stability": "dev", "prefer-stable": true}