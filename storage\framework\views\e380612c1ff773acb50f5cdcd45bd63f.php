

<div class="row">
    <div
        class="form-group col-sm-12 <?php echo e($errors->has('res_r3.presentation') ? ' has-error has-feedback' : ''); ?>">
        <label for="presentation">นำเสนอลูกค้าทางได</label>
        <select name="res_r3[presentation]" id="presentation" class="form-control"
            placeholder="<?php echo e(_trans('user::users.form.presentation')); ?>">
            
            <?php $__currentLoopData = Arr::get($selects, 'presentation'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $p => $presentation): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <option <?php echo e(old('res_r3.presentation') === $p ? 'selected' : ''); ?>

                    value="<?php echo e($p); ?>"><?php echo e($presentation); ?>

                </option>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </select>
        <?php echo $errors->first('res_r3.presentation', '<span class="help-block">:message</span>'); ?>

    </div>
    <div id="row_presentation_other"
        class='col-sm-12 d-none form-group<?php echo e($errors->has('res_r3.presentation_other') ? ' has-error' : ''); ?>'>
        <label for="">อื่นๆ (ระบุ)</label>
        <?php echo Form::input('text', 'res_r3[presentation_other]', old('res_r3.presentation_other'), ['class' => 'form-control']); ?>

        <?php echo $errors->first('res_r3.presentation_other', '<span class="help-block">:message</span>'); ?>

    </div>
</div>
<div class="row">
    <div
        class="form-group col-sm-12 <?php echo e($errors->has('res_r3.product_type') ? ' has-error has-feedback' : ''); ?>">
        <label for="product_type">ประเภทสินค้าที่นำเสนอ</label>
        <select name="res_r3[product_type][]" id="product_type" multiple class="form-control selectize">
            
            <?php $__currentLoopData = Arr::get($selects, 'product_type'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $p => $product_type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <option <?php echo e(old('res_r3.product_type') === $p ? 'selected' : ''); ?>

                    value="<?php echo e($p); ?>"><?php echo e($product_type); ?>

                </option>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </select>
        <?php echo $errors->first('res_r3.product_type', '<span class="help-block">:message</span>'); ?>

    </div>
    <div id="row_product_type_other"
        class='col-sm-12 d-none form-group<?php echo e($errors->has('res_r3.product_type_other') ? ' has-error' : ''); ?>'>
        <label for="">อื่นๆ (ระบุ)</label>
        <?php echo Form::input('text', 'res_r3[product_type_other]', old('res_r3.product_type_other'), ['class' => 'form-control']); ?>

        <?php echo $errors->first('res_r3.product_type_other', '<span class="help-block">:message</span>'); ?>

    </div>

    <div id="container_order_date" class='col-12 form-group<?php echo e($errors->has('order_date_from') || $errors->has('order_date_to') ? ' has-error' : ''); ?>'>
     
        <label for="">ลูกค้าจะสั่งซื้อในช่วง</label>
        <div class="input-group mb-2">
            <div class="input-group-prepend"> 
                <span class="input-group-text" id="basic-addon1">วันที่</span>
            </div>
            <select name="order_date_from[d]" id="order_date_from_d" class="form-control">
                <option value="">- วัน -</option>
                <?php for($d = 1; $d <= 31; $d++): ?>
                    <option value="<?php echo e($d); ?>"><?php echo e($d); ?></option>
                    
                <?php endfor; ?>
            </select>
            <select name="order_date_from[m]" id="order_date_from_m" class="form-control">
                <option value="">- เดือน -</option>
                <?php $__currentLoopData = Arr::get($selects, 'order_month'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $m => $order_month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($m); ?>"><?php echo e($order_month); ?></option>
                    
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>
            <select name="order_date_from[y]" id="order_date_from_y" class="form-control">
                <option value="">- ปี -</option>
                <?php for($y = date("Y") + 1; $y >= date("Y") - 3; $y--): ?>
                <option value="<?php echo e($y); ?>"><?php echo e($y+543); ?></option>
                
                <?php endfor; ?>
            </select>
        </div> 
        
        <label for="">ถึง</label>
        <div class="input-group mb-2">
            <div class="input-group-prepend">
                <span class="input-group-text" id="basic-addon2">วันที่</span>
            </div>
            <select name="order_date_to[d]" id="order_date_to_d" class="form-control">
                <option value="">- วัน -</option>
                <?php for($d = 1; $d <= 31; $d++): ?>
                    <option value="<?php echo e($d); ?>"><?php echo e($d); ?></option>
                    
                <?php endfor; ?>
            </select>
            <select name="order_date_to[m]" id="order_date_to_m" class="form-control">
                <option value="">- เดือน -</option>
                <?php $__currentLoopData = Arr::get($selects, 'order_month'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $m => $order_month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($m); ?>"><?php echo e($order_month); ?></option>
                    
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>
            <select name="order_date_to[y]" id="order_date_to_y" class="form-control">
                <option value="">- ปี -</option>
                <?php for($y = date("Y") + 1; $y >= date("Y") - 3; $y--): ?>
                <option value="<?php echo e($y); ?>"><?php echo e($y+543); ?></option>
                
                <?php endfor; ?>
            </select>
        </div>
        
    </div>

</div>
<?php /**PATH C:\Projects\Web\htdocs\ssp4-v11\Modules/SalePrinting/Resources/views/member/sales/partials/create-fields/presentation-data.blade.php ENDPATH**/ ?>