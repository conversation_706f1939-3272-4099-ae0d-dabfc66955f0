@extends('layouts.master')

@section('content-header')
    <h1>
        {{ trans('workshop::workbench.title') }}
    </h1>
    <ol class="breadcrumb">
        <li><a href="{{ route('dashboard.index') }}"><i class="fa fa-dashboard"></i>
                {{ trans('user::users.breadcrumb.home') }}</a></li>
        <li class="active">{{ trans('workshop::workbench.title') }}</li>
    </ol>
@stop

@section('content')
    <div class="row">
        <div class="col-md-12">
            <div class="box box-primary">
                <div class="box-header">
                </div> 

                <div class="box-content">
                    {!! Form::open(['route' => ['admin.workshop.tools.base64topdf'], 'method' => 'post']) !!}
                    {{-- <textarea name="b" id="b" cols="30" rows="10" style="width: 100%" class="form-control"
                        placeholder="PDF Base64">
                    </textarea> 
                        OR --}}

                        URL : {{ $url }} <br>
                        Cookie :
                    {{-- <input type="text" class="form-control" name="u" placeholder="URL"> --}}
                    <textarea name="c" id="c" cols="30" rows="10" style="width: 100%" class="form-control"
                        placeholder="Cookie"></textarea>
                    <button type="submit" class="btn btn-info">Download</button>
                    {!! Form::close() !!}
                    <hr>
                    @if ($link1)
                        Download: <br>
                        <a target="_blank" href="{{ $link1 }}">{{ $link1 }}</a><br>
                        <a target="_blank" href="{{ $link2 }}">{{ $link2 }}</a><br>
                    @endif
                    @if ($link3)                        
                    <iframe src="{{ $link3 }}" frameborder="0" style="width: 100%; height: 200px;"></iframe>
                    @endif

                </div>
            </div>
        </div>
    </div>
@stop

@push('js-stack')
    <script>
        $(document).ready(function() {
            $('input[type="checkbox"].flat-blue, input[type="radio"].flat-blue').iCheck({
                checkboxClass: 'icheckbox_flat-blue',
                radioClass: 'iradio_flat-blue'
            });
        });
    </script>
@endpush
