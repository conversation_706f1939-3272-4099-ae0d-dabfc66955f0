<?php

namespace App\Library\Line\API\v21;

use Illuminate\Support\Facades\Log;

class LineAPI {

  public function accessToken($code, $channelId, $channelSecret, $callback_url){
    return array(
      'Url' => 'https://api.line.me/oauth2/v2.1/token',
      'Method' => 'post',
      'Header' => 'Content-Type: application/x-www-form-urlencoded',
      'Body' => array(
            'grant_type'=>'authorization_code',
            'code'=>$code,
            'client_id'=>$channelId,
            'client_secret'=>$channelSecret,
            'redirect_uri'=>$callback_url
    ));
  }

  public function refreshToken($refresh_token, $channelId, $channelSecret){
    return array(
      'Url' => 'https://api.line.me/oauth2/v2.1/token',
      'Method' => 'post',
      'Header' => 'Content-Type: application/x-www-form-urlencoded',
      'Body' => array(
            urlencode('grant_type')=>urlencode('refresh_token'),
            urlencode('refresh_token')=>urlencode($refresh_token),
            urlencode('client_id')=>urlencode($channelId),
            urlencode('client_secret')=>urlencode($channelSecret)
    ));
  }

  public function verify($access_token){
    return array(
      'Url' => 'https://api.line.me/oauth2/v2.1/verify',
      'Method' => 'post',
      'Header' => 'Content-Type: application/x-www-form-urlencoded',
      'Body' => array(
            urlencode('access_token')=>urlencode($access_token)
    ));
  }

  public function revoke($access_token, $channelId, $channelSecret){
    return array(
      'Url' => 'https://api.line.me/oauth2/v2.1/revoke',
      'Method' => 'post',
      'Header' => 'Content-Type: application/x-www-form-urlencoded',
      'Body' => array(
            urlencode('access_token')=>urlencode($access_token),
            urlencode('client_id')=>urlencode($channelId),
            urlencode('client_secret')=>urlencode($channelSecret)
    ));
  }

  /*
   * Gets a user's display name, profile image, and status message.
   * */
  public function profile($bearer){
    return array(
      'Url' => 'https://api.line.me/v2/profile',
      'Method' => 'get',
      'Header' => 'Authorization: ' . $bearer
    );
  }

  /*
   *  The user has added the LINE Official Account as a friend and has not blocked it.
   * */
  public function friendship_status($bearer){
    return array(
      'Url' => 'https://api.line.me/friendship/v1/status',
      'Method' => 'get',
      'Header' => 'Authorization: ' . $bearer
    );
  }
}
