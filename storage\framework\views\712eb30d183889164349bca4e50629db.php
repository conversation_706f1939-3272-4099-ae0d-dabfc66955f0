
<div id="filter-date-wrapper" class="input-group-prepend <?php echo e(request()->date ?"":" d-none "); ?>">
    <div class="dropdown mega-dropdown">
        <a class="btn custom-bg-outline-gray custom-filter-label current_filter_date dropdown-toggle"
            href="#" role="button" id="dropdownMenuLink" aria-haspopup="true"
            data-val="<?php echo e(request()->has('date') ? request()->date : ''); ?>"
            aria-expanded="false">
            <?php echo e(request()->has('date') ? Arr::get($current_filter_date, 'label') :  '- เลือกช่วงวันที่ -'); ?>

        </a>
        <div class="dropdown-menu w-100 p-2" aria-labelledby="dropdownMenuLink">
            <div class="">จากวันที่</div>
            <div class="">
                <form id="filter_date_from" class="input-group mb-2">
                    <select name="filter_date_from[d]" id="filter_date_from_d" class="form-control">
                        <option value="">วัน</option>
                        <?php for($d = 1; $d <= 31; $d++): ?>
                            <option <?php echo e($d == Arr::get($current_filter_date, 'from.d')?'selected':''); ?> value="<?php echo e($d); ?>"><?php echo e($d); ?></option>
                            
                        <?php endfor; ?>
                    </select>
                    <select name="filter_date_from[m]" id="filter_date_from_m" class="form-control">
                        <option value="">เดือน</option>
                        <?php $__currentLoopData = Arr::get($salemarketing_data, 'short_month'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $m => $month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option <?php echo e($m == Arr::get($current_filter_date, 'from.m')?'selected':''); ?> value="<?php echo e($m); ?>"><?php echo e($month); ?></option>
                            
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                    <select name="filter_date_from[y]" id="filter_date_from_y" class="form-control">
                        <option value="">ปี</option>
                        <?php for($y = date('Y') + 1; $y >= date('Y') - 3; $y--): ?>
                            <option <?php echo e($y == Arr::get($current_filter_date, 'from.y')?'selected':''); ?> value="<?php echo e($y); ?>"><?php echo e($y + 543); ?></option>
                            
                        <?php endfor; ?>
                    </select>
                </form>
            </div>
            
            <div class="">ถึงวันที่</div>
            <div class="">
                <form id="filter_date_to" class="input-group mb-2">
                    <select name="filter_date_to[d]" id="filter_date_to_d" class="form-control">
                        <option value="">วัน</option>
                        <?php for($d = 1; $d <= 31; $d++): ?>
                            <option <?php echo e($d == Arr::get($current_filter_date, 'to.d')?'selected':''); ?> value="<?php echo e($d); ?>"><?php echo e($d); ?></option>
                            
                        <?php endfor; ?>
                    </select>
                    <select name="filter_date_to[m]" id="filter_date_to_m" class="form-control">
                        <option value="">เดือน</option>
                        <?php $__currentLoopData = Arr::get($salemarketing_data, 'short_month'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $m => $month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option <?php echo e($m == Arr::get($current_filter_date, 'to.m')?'selected':''); ?> value="<?php echo e($m); ?>"><?php echo e($month); ?></option>
                            
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                    <select name="filter_date_to[y]" id="filter_date_to_y" class="form-control">
                        <option value="">ปี</option>
                        <?php for($y = date('Y') + 1; $y >= date('Y') - 3; $y--): ?>
                            <option <?php echo e($y == Arr::get($current_filter_date, 'to.y')?'selected':''); ?> value="<?php echo e($y); ?>"><?php echo e($y + 543); ?></option>
                            
                        <?php endfor; ?>
                    </select>
                </form>
                
                <div class=""><button id="apply_filter_date" class="btn btn-primary w-100">Apply</button></div>
                <div class=""><button id="clear_filter_date" class="btn btn-outline-primary w-100">Clear</button></div>
            </div>
            <input type="hidden" name="filter_date" class="custom-filter"
                value="<?php echo e(request()->has('date') ? request()->date : ''); ?>">
        </div>
    </div>
</div>

<?php $__env->startPush('css-stack'); ?>
<style>
    .dropdown select {padding: 0}
</style>
<?php $__env->stopPush(); ?>
<?php $__env->startPush('js-stack'); ?>

    <script id="script-filter-input-date" type="text/javascript">

        $(document).ready(function() {
            $(document).on('click', '.dropdown.mega-dropdown a.dropdown-toggle', function (event) { 
                let parent =  $(this).parent();
                parent.toggleClass('show');
                parent.find('.dropdown-menu').toggleClass('show');
            });
            $(document).on('click', '#apply_filter_date', function (event) {
                $(this).parents('.dropdown').removeClass('show');
                $(this).parents('.dropdown-menu').removeClass('show');
                
                let val_from = $("#filter_date_from_y").val() + "-"
                + $("#filter_date_from_m").val().padStart(2, '0') + "-"
                + $("#filter_date_from_d").val().padStart(2, '0');
                let val_to = $("#filter_date_to_y").val() + "-"
                + $("#filter_date_to_m").val().padStart(2, '0') + "-"
                + $("#filter_date_to_d").val().padStart(2, '0');

                // $(".custom-filter[name=filter_date]").val( val_from + "_" + val_to);
                window.filter["filter_date"] = val_from + "_" + val_to;
                var query = buildQueryString(window.filter).replaceAll("filter_", "");
                if(typeof window.resetPageDataTable === 'function')
                    resetPageDataTable();
                window.location.href = window.location.origin + window.location.pathname + "?" + query;
       
            });
            $(document).on('click', '#clear_filter_date', function (event) {
                $(this).parents('.dropdown').removeClass('show');
                $(this).parents('.dropdown-menu').removeClass('show'); 

                // $(".custom-filter[name=filter_date]").val( val_from + "_" + val_to);
                window.filter["filter_date"] = null;
                var query = buildQueryString(window.filter).replaceAll("filter_", "");
                if(typeof window.resetPageDataTable === 'function')
                    resetPageDataTable();
                window.location.href = window.location.origin + window.location.pathname + "?" + query;
       
            });
        });
    </script>

<?php $__env->stopPush(); ?><?php /**PATH C:\Projects\Web\htdocs\ssp4-v11\Modules/SaleMarketing/Resources/views/member/report/partials/filter-input-date.blade.php ENDPATH**/ ?>