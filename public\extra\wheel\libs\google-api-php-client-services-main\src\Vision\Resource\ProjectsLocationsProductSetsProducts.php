<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Vision\Resource;

use Google\Service\Vision\ListProductsInProductSetResponse;

/**
 * The "products" collection of methods.
 * Typical usage is:
 *  <code>
 *   $visionService = new Google\Service\Vision(...);
 *   $products = $visionService->projects_locations_productSets_products;
 *  </code>
 */
class ProjectsLocationsProductSetsProducts extends \Google\Service\Resource
{
  /**
   * Lists the Products in a ProductSet, in an unspecified order. If the
   * ProductSet does not exist, the products field of the response will be empty.
   * Possible errors: * Returns INVALID_ARGUMENT if page_size is greater than 100
   * or less than 1. (products.listProjectsLocationsProductSetsProducts)
   *
   * @param string $name Required. The ProductSet resource for which to retrieve
   * Products. Format is:
   * `projects/PROJECT_ID/locations/LOC_ID/productSets/PRODUCT_SET_ID`
   * @param array $optParams Optional parameters.
   *
   * @opt_param int pageSize The maximum number of items to return. Default 10,
   * maximum 100.
   * @opt_param string pageToken The next_page_token returned from a previous List
   * request, if any.
   * @return ListProductsInProductSetResponse
   */
  public function listProjectsLocationsProductSetsProducts($name, $optParams = [])
  {
    $params = ['name' => $name];
    $params = array_merge($params, $optParams);
    return $this->call('list', [$params], ListProductsInProductSetResponse::class);
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(ProjectsLocationsProductSetsProducts::class, 'Google_Service_Vision_Resource_ProjectsLocationsProductSetsProducts');
