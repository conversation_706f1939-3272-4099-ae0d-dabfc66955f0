<div class="input-group-prepend">
    <select id="btn_filter_fy" name="filter_fy" class="btn_filter_fy form-control custom-bg-outline-gray custom-filter">
        
        <?php for($fy = _year_fiscal_thai(); $fy > 2563; $fy--): ?>
            <option <?php echo e(!request()->date && request()->fy == $fy ? 'selected' : ''); ?> value="<?php echo e($fy); ?>">
                ปีงบประมาณ <?php echo e($fy); ?></option>
        <?php endfor; ?>
        
        <option <?php echo e(request()->date ?"selected":""); ?> value="0">- เลือกช่วงวันที่ -</option>
    </select>
</div>

<?php $__env->startPush('js-stack'); ?>
    <script id="script-filter-input-result" type="text/javascript">
        $(document).ready(function() {
            $(document).on("change", ".btn_filter_fy", function() {

                let val = $(this).val();

                if (val === "0")
                    $("#filter-date-wrapper").removeClass("d-none");
                else {
                    $("#filter-date-wrapper").addClass("d-none");
                    window.filter["filter_fy"] = val;
                    var query = buildQueryString(window.filter).replaceAll("filter_", "");
                    if (typeof window.resetPageDataTable === 'function')
                        resetPageDataTable();
                    window.location.href = window.location.origin + window.location.pathname + "?" + query;
                }
                // reloadDataTable();
            });

        });
    </script>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\Projects\Web\htdocs\ssp4-v11\Modules/SaleMarketing/Resources/views/member/report/partials/filter-input-fy.blade.php ENDPATH**/ ?>