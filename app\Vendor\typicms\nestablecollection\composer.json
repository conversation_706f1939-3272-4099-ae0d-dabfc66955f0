{"name": "typicms/nestablecollection", "description": "A Laravel Package that extends Collection to handle unlimited nested items following adjacency list model.", "keywords": ["<PERSON><PERSON>", "Eloquent", "Collection", "TypiCMS", "tree", "nested set", "adjacency list"], "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"illuminate/database": "~4.2|~5.0|~6.0|~7.0|~8.0|~9.0", "illuminate/support": "~4.2|~5.0|~6.0|~7.0|~8.0|~9.0"}, "autoload": {"psr-4": {"TypiCMS\\": "src/"}}, "minimum-stability": "stable"}