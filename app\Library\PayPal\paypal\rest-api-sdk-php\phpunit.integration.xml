<?xml version="1.0" encoding="UTF-8"?>
<phpunit bootstrap="./tests/bootstrap.php"
         colors="true"
         processIsolation="false"
         stopOnFailure="false"
         syntaxCheck="false"
         convertErrorsToExceptions="true"
         convertNoticesToExceptions="true"
         convertWarningsToExceptions="true"
         testSuiteLoaderClass="PHPUnit_Runner_StandardTestSuiteLoader">

    <php>
        <env name="REST_MODE" value="sandbox"/>
    </php>

    <testsuites>
        <testsuite name="All">
            <directory>tests</directory>
        </testsuite>
    </testsuites>

    <logging>
        <log type="junit" target="build/junit.xml" logIncompleteSkipped="false" />
        <log type="coverage-html" target="build/coverage/" charset="UTF-8"
             highlight="true" lowUpperBound="35" highLowerBound="70"/>
        <log type="coverage-clover" target="build/coverage/clover.xml"/>
    </logging>

    <filter>
        <whitelist>
            <directory suffix=".php">./lib</directory>
            <exclude>
            </exclude>
        </whitelist>
    </filter>
</phpunit>
