{"name": "idavoll/media-module", "type": "asgard-module", "description": "Media module for AsgardCMS. Handles the media library.", "keywords": ["asgardcms", "media", "files", "library", "thumbnails", "filters"], "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "support": {"email": "<EMAIL>", "issues": "https://github.com/AsgardCms/Platform/issues", "source": "https://github.com/AsgardCms/Media"}, "autoload-dev": {"psr-4": {"Modules\\Media\\": ".", "Modules\\": "Mo<PERSON>les/"}}, "extra": {"branch-alias": {"dev-master": "4.0.x-dev"}}, "minimum-stability": "dev", "prefer-stable": true}