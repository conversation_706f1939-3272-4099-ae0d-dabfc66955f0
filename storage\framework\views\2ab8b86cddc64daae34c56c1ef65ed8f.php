<div class="page-main-header open d-print-none">
    
    <div class="main-header-right row">
        
        <div class="main-header-left">
            <div class="logo-wrapper">
                <a href="<?php echo e(_url('/')); ?>"><img class="blur-up lazyloaded"
                        src="<?php echo e(_asset('assets/images/logo.png')); ?>"
                        style="width:121px!important; height:auto!important;" alt=""></a>
            </div>
        </div>
        
        <div class="nav-right col">

            <a href="#cd-nav" class="cd-nav-trigger">
                Menu<span>
                    <!-- used to create the menu icon -->
                </span>
            </a> <!-- .cd-nav-trigger -->
            <ul class="nav-menus">
                <li id="search-box" class="d-none">
                    
                    <div id="search-form" class="form-inline search-form">
                        <div class="form-group">
                            <input id="search-input" class="form-control-plaintext " type="search" placeholder="Search.."><span class="d-sm-none mobile-search"><i data-feather="search"></i></span>
                        </div>
                    </div>
                    
                </li>
                <?php echo $__env->yieldPushContent('nav-menus'); ?> 
                <?php if(auth()->guard()->check()): ?>
                    <li class="onhover-dropdown">
                        <div class="media align-items-center rounded-circle" 
                        style="height: 50px; overflow: hidden;">
                        <img class="lazyload align-self-center pull-right img-50 rounded-circle border"
                                data-src="<?php echo e($user->avatar ? _get_avatar_user($user->avatar, 'smallThumb') : _asset('svg/pink/undraw_mornings_re_cofi.svg')); ?>" alt="header-user">
                            <div class="dotted-animation"><span class="animate-circle"></span><span
                                    class="main-circle"></span></div>
                        </div>
                        <ul class="profile-dropdown onhover-show-div p-20">
                            <li><a style="color: #ff8084;" href="<?php echo e(route('user.profile.index')); ?>"><?php echo e(auth()->user()->nickname); ?></a>
                            </li>
                            <li class="p-0"><a style="color: #ff8084;" href="<?php echo e(route('user.profile.index')); ?>"><small><?php echo e(auth()->user()->name); ?></small></a>
                            </li>
                            <li class="p-0"><a href="<?php echo e(route('user.profile.index')); ?>"><small><?php echo e(auth()->user()->email); ?></small></a>
                            </li>
                            
                            <li></li>
                            <li><a href="<?php echo e(route('user.profile.index')); ?>"><i data-feather="user"></i>Edit Profile</a>
                            </li>
                            
                            <li><a href="<?php echo e(route('logout')); ?>"><i data-feather="log-out"></i>Logout</a></li>
                        </ul>
                    </li>
                
                    
                <?php endif; ?>
            </ul>
            <div class="d-lg-none mobile-toggle pull-right"><i data-feather="more-horizontal"></i></div>
        </div>
    </div>
</div>
<?php /**PATH C:\Projects\Web\htdocs\ssp4-v11\Themes\Multikart\views/partials/master-header.blade.php ENDPATH**/ ?>