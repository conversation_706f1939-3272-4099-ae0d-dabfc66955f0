<?php $__env->startSection('content-header'); ?>
    <h1>
        <?php echo e(_trans('user::users.title.edit-profile')); ?>

    </h1>
    <ol class="breadcrumb">
        <li><a href="<?php echo e(route('dashboard.index')); ?>"><i class="fa fa-tachometer-alt"></i>
                <?php echo e(_trans('core::core.breadcrumb.home')); ?></a></li>
        
        <li class="active"><?php echo e(_trans('user::users.breadcrumb.edit-salemarketing')); ?></li>
    </ol>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="row justify-content-center"> 
    <?php $__currentLoopData = Arr::get($salemarketing_data, 'department', []); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $dept_no => $dept): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    
        
        <div class="col-xl-12 col-6 xl-50">
            <div class="card height-equal">
                <div class="card-header">
                    
                    <h5><?php echo e($dept['name']); ?></h5>
                    <div class="card-header-right">
                        <ul class="list-unstyled card-option">
                            <li><i class="icofont icofont-simple-left"></i></li>
                            <li><i class="view-html fa fa-code"></i></li>
                            <li><i class="icofont icofont-maximize full-card"></i></li>
                            <li><i class="icofont icofont-minus minimize-card"></i></li>
                            <li><i class="icofont icofont-refresh reload-card"></i></li>
                            <li><i class="icofont icofont-error close-card"></i></li>
                        </ul>
                    </div>
                </div>
                <div class="card-body">
                    <div class="user-status table-responsive products-table">
                        <table class="table table-bordernone mb-0">
                            <thead>
                                <tr>
                                    <th scope="col">ชื่อ</th>
                                    
                                    
                                    <th scope="col">เข้าสู่ระบบล่าสุด</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $dept_users[$dept_no]; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $usr): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td class="bd-t-none u-s-tb">
                                        <div class="align-middle image-sm-size text-nowrap"><img
                                                class="img-radius align-top m-r-15 rounded-circle blur-up lazyloaded"
                                                src="/assets/images/thumb/user.png" alt="" data-original-title=""
                                                title="">
                                            <div class="d-inline-block mt-0">
                                                <h6>
                                                    <span class="text-nowrap"><?php echo e($usr->name); ?></span> <br>
                                                    <span class="text-muted digits">(<?php echo e($usr->nickname); ?>)</span>
                                                </h6>
                                            </div>
                                        </div>
                                    </td>
                                    
                                    
                                    <td><?php echo nl2br(_date_thai("d M y\nH:i", (string) $usr->last_login)); ?>

                                    </td>
                                </tr> 
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
<?php $__env->stopSection(); ?>


<?php echo $__env->make('core::partials.photoswipe', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php $__env->startPush('css-stack'); ?>
    <style type="text/css">
        .table {
            table-layout: fixed;
        }

        .table label {
            font-weight: 600;
        }

        .card-header h4 {
            color: #ff8084 !important
        }

        @media print {

            .table td,
            .card-body,
            .card-header {
                padding: 0 !important
            }
        }

    </style>
<?php $__env->stopPush(); ?>
<?php $__env->startPush('js-stack'); ?>

    
    <script type="text/javascript">
        initPhotoSwipeFromDOM('.demo-gallery');
    </script>

<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Projects\Web\htdocs\ssp4-v11\Modules/SaleMarketing/Resources/views/member/report/users.blade.php ENDPATH**/ ?>